#!/bin/bash

# Setup ECR permissions for ECS workspace
set -e

AWS_REGION="us-east-1"
ROLE_NAME="ecsTaskExecutionRole"
POLICY_NAME="ECRAccessPolicy"

echo "🔧 Setting up ECR permissions for ECS workspace..."

# Step 1: Check if role exists
echo "📋 Checking if role ${ROLE_NAME} exists..."
if ! aws iam get-role --role-name ${ROLE_NAME} --region ${AWS_REGION} >/dev/null 2>&1; then
    echo "❌ Role ${ROLE_NAME} does not exist. Please create it first."
    exit 1
fi

# Step 2: Attach AWS managed policy for ECS task execution
echo "🔗 Attaching AWS managed ECS task execution policy..."
aws iam attach-role-policy \
    --role-name ${ROLE_NAME} \
    --policy-arn arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy \
    --region ${AWS_REGION} || echo "Policy already attached or error occurred"

# Step 3: Add custom ECR access policy
echo "📝 Adding custom ECR access policy..."
aws iam put-role-policy \
    --role-name ${ROLE_NAME} \
    --policy-name ${POLICY_NAME} \
    --policy-document file://$(dirname "$0")/../aws-policies/ecr-access-policy.json \
    --region ${AWS_REGION}

# Step 4: List attached policies to verify
echo "📋 Current policies attached to ${ROLE_NAME}:"
echo "Managed policies:"
aws iam list-attached-role-policies --role-name ${ROLE_NAME} --region ${AWS_REGION} --query 'AttachedPolicies[*].PolicyName' --output table

echo "Inline policies:"
aws iam list-role-policies --role-name ${ROLE_NAME} --region ${AWS_REGION} --query 'PolicyNames' --output table

echo "✅ ECR permissions setup complete!"
echo ""
echo "🚀 Next steps:"
echo "1. Run: chmod +x backend/scripts/build-and-push-workspace.sh"
echo "2. Run: ./backend/scripts/build-and-push-workspace.sh"
echo "3. Test workspace creation"
