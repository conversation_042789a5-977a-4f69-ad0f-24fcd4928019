#!/bin/bash

# Build and push workspace container to ECR
set -e

# Configuration
AWS_REGION="us-east-1"
AWS_ACCOUNT_ID="************"
REPOSITORY_NAME="mergen/workspace"
IMAGE_TAG="latest"
ECR_URI="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${REPOSITORY_NAME}"

echo "🚀 Building and pushing workspace container to ECR..."
echo "Repository: ${ECR_URI}:${IMAGE_TAG}"

# Step 1: Create ECR repository if it doesn't exist
echo "📦 Ensuring ECR repository exists..."
aws ecr describe-repositories --repository-names ${REPOSITORY_NAME} --region ${AWS_REGION} 2>/dev/null || {
    echo "Creating ECR repository: ${REPOSITORY_NAME}"
    aws ecr create-repository --repository-name ${REPOSITORY_NAME} --region ${AWS_REGION}
}

# Step 2: Get ECR login token
echo "🔐 Logging into ECR..."
aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com

# Step 3: Build the Docker image for linux/amd64 platform (ECS Fargate requirement)
echo "🔨 Building Docker image for linux/amd64 platform..."
cd "$(dirname "$0")/../docker/workspace"
docker build --platform linux/amd64 -t ${REPOSITORY_NAME}:${IMAGE_TAG} .

# Step 4: Tag the image for ECR
echo "🏷️  Tagging image for ECR..."
docker tag ${REPOSITORY_NAME}:${IMAGE_TAG} ${ECR_URI}:${IMAGE_TAG}

# Step 5: Push the image to ECR
echo "⬆️  Pushing image to ECR..."
docker push ${ECR_URI}:${IMAGE_TAG}

echo "✅ Successfully built and pushed workspace container!"
echo "Image URI: ${ECR_URI}:${IMAGE_TAG}"
echo ""
echo "🔧 Next steps:"
echo "1. Ensure ECS task execution role has ECR permissions"
echo "2. Test workspace creation with: curl -X POST http://localhost:3001/api/ecs-workspace ..."
