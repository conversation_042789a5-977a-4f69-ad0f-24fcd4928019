# Environment Configuration Guide

This document describes all environment variables used in the Mergen AI backend application.

## 🔧 **High Priority Configuration (Centralized)**

### **Server Configuration**
- `PORT` - Server port (default: 8000)
- `NODE_ENV` - Environment mode (default: development)

### **Database Configuration**
- `MONGODB_URI` - MongoDB connection string (default: mongodb://localhost:27017/mergen_code)

### **JWT Configuration**
- `JWT_SECRET` - Secret key for JWT token generation (default: your-secret-key)

### **Logging Configuration**
- `LOG_LEVEL` - Logging level (error, warn, info, debug, verbose) (default: info)

### **LLM Configuration**
- `ANTHROPIC_API_KEY` - Anthropic API key for Claude access
- `BYPASS_API_KEY` - Enable mock mode for testing (true/false) (default: false)
- `LLM_TIMEOUT_MS` - LLM API request timeout in milliseconds (default: 300000 - 5 minutes)
- `MAX_TOKENS` - Maximum tokens for LLM responses (default: 60000)

### **ECS Workspace Configuration**
- `WORKSPACE_TIMEOUT_MS` - Workspace HTTP request timeout (default: 10000 - 10 seconds)
- `WORKSPACE_RETRY_DELAY_MS` - Delay between retry attempts (default: 3000 - 3 seconds)
- `WORKSPACE_MAX_RETRIES` - Maximum number of retry attempts (default: 10)
- `WORKSPACE_HEALTH_CHECK_TIMEOUT_MS` - Health check timeout (default: 10000 - 10 seconds)
- `WORKSPACE_STATUS_CACHE_TTL_MS` - Status cache TTL (default: 3000 - 3 seconds)
- `PREVIEW_START_TIMEOUT_MS` - Timeout for starting preview inside container (default: 120000 - 2 minutes)
- `PUBLISH_START_TIMEOUT_MS` - Timeout for starting publish (build/preview) inside container (default: 180000 - 3 minutes)

### **Build Configuration**
- `BUILD_TIMEOUT_MS` - Build process timeout (default: 300000 - 5 minutes)
- `BUILD_RETRY_DELAY_MS` - Build retry delay (default: 3000 - 3 seconds)
- `BUILD_MAX_RETRIES` - Build max retries (default: 10)
- `BUILD_SAFETY_TIMEOUT_MS` - Build safety timeout (default: 300000 - 5 minutes)

### **Frontend Configuration**
- `FRONTEND_LIFETIME_EXTENSION_THRESHOLD_MS` - Auto-extension threshold (default: 900000 - 15 minutes)
- `FRONTEND_THROTTLE_INTERVAL_MS` - Throttle interval for extensions (default: 60000 - 1 minute)
- `FRONTEND_CHECK_CADENCE_MS` - Check frequency (default: 60000 - 1 minute)

### **Auto-extension Configuration**
- `AUTO_EXTENSION_THRESHOLD_MINUTES` - Minutes before expiry to auto-extend (default: 15)

## 🏗️ **AWS Configuration (Already Centralized)**

### **AWS Credentials**
- `AWS_REGION` - AWS region (required)
- `AWS_ACCESS_KEY_ID` - AWS access key (required)
- `AWS_SECRET_ACCESS_KEY` - AWS secret key (required)

### **ECS Configuration**
- `ECS_CLUSTER_NAME` - ECS cluster name (required)
- `ECS_TASK_DEFINITION_FAMILY` - Task definition family (default: mergen-workspace-task)
- `ECS_EXECUTION_ROLE_ARN` - ECS execution role ARN (required)
- `ECS_TASK_ROLE_ARN` - ECS task role ARN (required)

### **Networking**
- `VPC_ID` - VPC ID (required)
- `SUBNET_IDS` - Comma-separated subnet IDs (required)
- `SECURITY_GROUP_ID` - Security group ID (required)

### **EFS Configuration (Optional)**
- `EFS_FILE_SYSTEM_ID` - EFS file system ID (optional)
- `EFS_ACCESS_POINT_ID` - EFS access point ID (optional)

### **Container Configuration**
- `CONTAINER_IMAGE` - Container image (default: mergen/workspace:latest)
- `CONTAINER_CPU` - Container CPU units (default: 512)
- `CONTAINER_MEMORY` - Container memory in MB (default: 1024)
- `CONTAINER_PORT` - Container port (default: 3000)
- `PREVIEW_PORT` - Preview server port (default: 8080)

### **Lifecycle Configuration**
- `DEFAULT_LIFETIME_HOURS` - Default workspace lifetime (default: 1)
- `EXTEND_LIFETIME_HOURS` - Extension duration (default: 1)
- `CLEANUP_INTERVAL_MINUTES` - Cleanup interval (default: 15)
- `SYNC_INTERVAL_MINUTES` - Sync interval (default: 15)

### **Route53 Configuration (Optional)**
- `ROUTE53_HOSTED_ZONE_ID` - Route53 hosted zone ID (optional)
- `PREVIEW_BASE_DOMAIN` - Preview domain base (optional)
- `LIVE_BASE_DOMAIN` - Live domain base (optional)

## 📝 **Example .env File**

```bash
# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
PORT=8000
NODE_ENV=development

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MONGODB_URI=mongodb://localhost:27017/mergen_code

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
JWT_SECRET=your-super-secret-jwt-key

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info

# =============================================================================
# LLM CONFIGURATION
# =============================================================================
ANTHROPIC_API_KEY=your-anthropic-api-key
BYPASS_API_KEY=false
LLM_TIMEOUT_MS=300000
MAX_TOKENS=60000

# =============================================================================
# ECS WORKSPACE CONFIGURATION
# =============================================================================
WORKSPACE_TIMEOUT_MS=10000
WORKSPACE_RETRY_DELAY_MS=3000
WORKSPACE_MAX_RETRIES=10
WORKSPACE_HEALTH_CHECK_TIMEOUT_MS=10000
WORKSPACE_STATUS_CACHE_TTL_MS=3000
PREVIEW_START_TIMEOUT_MS=120000
PUBLISH_START_TIMEOUT_MS=180000

# =============================================================================
# BUILD CONFIGURATION
# =============================================================================
BUILD_TIMEOUT_MS=300000
BUILD_RETRY_DELAY_MS=3000
BUILD_MAX_RETRIES=10
BUILD_SAFETY_TIMEOUT_MS=300000

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
FRONTEND_LIFETIME_EXTENSION_THRESHOLD_MS=900000
FRONTEND_THROTTLE_INTERVAL_MS=60000
FRONTEND_CHECK_CADENCE_MS=60000

# =============================================================================
# AUTO-EXTENSION CONFIGURATION
# =============================================================================
AUTO_EXTENSION_THRESHOLD_MINUTES=15

# =============================================================================
# AWS CONFIGURATION
# =============================================================================
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key

# ECS Configuration
ECS_CLUSTER_NAME=mergen-cluster
ECS_TASK_DEFINITION_FAMILY=mergen-workspace-task
ECS_EXECUTION_ROLE_ARN=arn:aws:iam::123456789012:role/ecsTaskExecutionRole
ECS_TASK_ROLE_ARN=arn:aws:iam::123456789012:role/ecsTaskRole

# Networking
VPC_ID=vpc-12345678
SUBNET_IDS=subnet-12345678,subnet-87654321
SECURITY_GROUP_ID=sg-12345678

# Container Configuration
CONTAINER_IMAGE=mergen/workspace:latest
CONTAINER_CPU=512
CONTAINER_MEMORY=1024
CONTAINER_PORT=3000
PREVIEW_PORT=8080

# Lifecycle Configuration
DEFAULT_LIFETIME_HOURS=1
EXTEND_LIFETIME_HOURS=1
CLEANUP_INTERVAL_MINUTES=15
SYNC_INTERVAL_MINUTES=15

# Route53 Configuration (Optional)
ROUTE53_HOSTED_ZONE_ID=Z1234567890ABC
PREVIEW_BASE_DOMAIN=preview.mergenai.io
LIVE_BASE_DOMAIN=live.mergenai.io
```

## 🔄 **Configuration Updates**

### **How to Update Configuration**

1. **Backend Configuration**: All configuration is centralized in `backend/src/config/app.ts` and `backend/src/config/aws.ts`
2. **Frontend Configuration**: Frontend fetches configuration from `/api/config` endpoint
3. **Environment Variables**: Update your `.env` file with the desired values
4. **Restart Services**: Restart the backend service after changing environment variables

### **Configuration Validation**

The application validates configuration on startup:
- Required AWS variables are checked
- Configuration values are parsed and validated
- Default values are applied for missing variables

### **Configuration Access**

- **Backend**: Use `getAppConfig()` and `getAWSConfig()` functions
- **Frontend**: Use the `/api/config` API endpoint
- **Public Config**: Only safe configuration values are exposed to the frontend

## 🚀 **Deployment Notes**

1. **Production**: Set `NODE_ENV=production` and use strong secrets
2. **Development**: Use `NODE_ENV=development` for detailed error messages
3. **Testing**: Use `BYPASS_API_KEY=true` for mock LLM responses
4. **Monitoring**: Adjust `LOG_LEVEL` based on your monitoring needs
5. **Performance**: Tune timeout and retry values based on your infrastructure

## 📊 **Configuration Categories**

### **Required Variables**
These must be set for the application to function:
- `AWS_REGION`, `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`
- `ECS_CLUSTER_NAME`, `ECS_EXECUTION_ROLE_ARN`, `ECS_TASK_ROLE_ARN`
- `VPC_ID`, `SUBNET_IDS`, `SECURITY_GROUP_ID`
- `ANTHROPIC_API_KEY` (unless `BYPASS_API_KEY=true`)

### **Optional Variables**
These have sensible defaults but can be customized:
- All timeout and retry configurations
- All frontend behavior configurations
- Container resource allocations
- Lifecycle management settings

### **Development Variables**
These are useful for development and testing:
- `BYPASS_API_KEY=true` - Enables mock LLM responses
- `LOG_LEVEL=debug` - Enables verbose logging
- `NODE_ENV=development` - Enables development features

## 🔍 **Troubleshooting**

### **Common Issues**

1. **Configuration Not Loading**: Ensure `.env` file is in the correct location
2. **Missing Variables**: Check that all required variables are set
3. **Invalid Values**: Ensure numeric values are valid numbers
4. **Frontend Not Updating**: Clear browser cache and restart frontend

### **Validation Errors**

The application will show clear error messages for:
- Missing required environment variables
- Invalid configuration values
- Configuration parsing errors

### **Debug Configuration**

To debug configuration issues:
1. Set `LOG_LEVEL=debug`
2. Check application startup logs
3. Verify environment variable values
4. Test configuration endpoints 