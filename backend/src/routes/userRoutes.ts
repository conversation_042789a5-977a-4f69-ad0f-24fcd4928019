import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import { getMe, updateMe, updatePassword } from '../controllers/userController';

const router = Router();

// GET /api/user/me - get current user profile
router.get('/me', authenticateToken, getMe);

// PUT /api/user/me - update current user profile
router.put('/me', authenticateToken, updateMe);

// PUT /api/user/password - update password
router.put('/password', authenticateToken, updatePassword);

export default router; 