import express from 'express';
import {
  generateLLMResponse,
  getBuildResult,
  llmHealthCheck,
  getChatHistory,
  updateChatHistory,
  checkBuildExists
} from '../controllers/llmController';
import { authenticateToken } from '../middleware/auth';


const router = express.Router();

// POST /api/llm/generate - Generate mock LLM response
router.post('/generate', authenticateToken, generateLLMResponse);

// GET /api/llm/result/:interviewUuid - Get build result by interview UUID
router.get('/result/:interviewUuid', authenticateToken, getBuildResult);

// GET /api/llm/health - Health check for LLM service
router.get('/health', llmHealthCheck);

// GET /api/llm/chat/:buildResultUuid - Get chat history for a build result
router.get('/chat/:buildResultUuid', authenticateToken, getChatHistory);

// PUT /api/llm/chat/:buildResultUuid - Update chat history for a build result
router.put('/chat/:buildResultUuid', authenticateToken, updateChatHistory);

// GET /api/llm/build-exists/:interviewUuid - Check if build exists for interview (temporarily without auth for testing)
router.get('/build-exists/:interviewUuid', checkBuildExists);

export default router;
