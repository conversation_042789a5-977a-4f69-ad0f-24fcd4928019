import express from 'express';
import {
  saveInterviewConfig,
  getInterviewConfig,
  getUserInterviewConfigs,
  getUserHistory,
  renameInterviewTitle,
  deleteInterview
} from '../controllers/interviewController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// POST /api/interview - Save a new interview configuration
router.post('/', authenticateToken, saveInterviewConfig);

// GET /api/interview/:uuid - Get interview configuration by UUID
router.get('/:uuid', authenticateToken, getInterviewConfig);

// PUT /api/interview/:uuid/title - Rename interview title
router.put('/:uuid/title', authenticateToken, renameInterviewTitle);

// DELETE /api/interview/:uuid - Delete interview and related data
router.delete('/:uuid', authenticateToken, deleteInterview);

// GET /api/interview/user/:userId - Get all interview configurations for a user
router.get('/user/:userId', authenticateToken, getUserInterviewConfigs);

// GET /api/interview/history/:userId - Get simplified history for a user
router.get('/history/:userId', authenticateToken, getUserHistory);

export default router;