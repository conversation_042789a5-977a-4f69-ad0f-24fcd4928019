import express from 'express';
import {
  getWorkspaceLifecycleInfo,
  extendWorkspaceLifetime,
  forceCleanupWorkspace,
  getLifecycleStatistics,
  getCleanupServiceStatus,
  forceRunCleanup,
  performLifecycleManagement
} from '../controllers/lifecycleController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// GET /api/lifecycle/workspace/:workspaceId - Get workspace lifecycle information
router.get('/workspace/:workspaceId', authenticateToken, getWorkspaceLifecycleInfo);

// POST /api/lifecycle/workspace/:workspaceId/extend - Extend workspace lifetime
router.post('/workspace/:workspaceId/extend', authenticateToken, extendWorkspaceLifetime);

// POST /api/lifecycle/workspace/:workspaceId/cleanup - Force cleanup a workspace
router.post('/workspace/:workspaceId/cleanup', authenticateToken, forceCleanupWorkspace);

// GET /api/lifecycle/statistics - Get overall lifecycle statistics
router.get('/statistics', authenticateToken, getLifecycleStatistics);

// GET /api/lifecycle/cleanup/status - Get cleanup service status
router.get('/cleanup/status', authenticateToken, getCleanupServiceStatus);

// POST /api/lifecycle/cleanup/force - Force run cleanup
router.post('/cleanup/force', authenticateToken, forceRunCleanup);

// POST /api/lifecycle/manage - Perform lifecycle management
router.post('/manage', authenticateToken, performLifecycleManagement);

export default router;
