import express from 'express';
import { testPayloadFormat, testResponseProcessing, testEndToEndFlow, testMockChatMessage, testUnifiedBuild, testRefreshScenario, testChatHistoryPersistence, testCompleteRefreshFlow, testInitialBuildMessages } from '../controllers/testController';

const router = express.Router();

// Test payload format generation (no API key required)
router.post('/payload', testPayloadFormat);

// Test response processing with mock data (no API key required)
router.get('/response', testResponseProcessing);

// Test complete end-to-end flow (no API key required)
router.post('/end-to-end', testEndToEndFlow);

// Test mock chat message with conversation context (no API key required)
router.post('/mock-chat-message', testMockChatMessage);

// Test unified build workflow (no API key required)
router.post('/unified-build', testUnifiedBuild);

// Test refresh scenario (check data preservation)
router.post('/refresh-scenario', testRefreshScenario);

// Test chat history persistence
router.post('/chat-history-persistence', testChatHistoryPersistence);

// Test complete refresh flow
router.post('/complete-refresh-flow', testCompleteRefreshFlow);

// Test initial build messages
router.post('/initial-build-messages', testInitialBuildMessages);

export default router;
