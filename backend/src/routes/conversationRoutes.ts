import express from 'express';
import {
  saveConversationMessage,
  saveConversationExchange,
  getConversationContext,
  getCompleteConversationContext,
  getConversationStats,
  clearConversationHistory
} from '../controllers/conversationController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Test routes without authentication for verification
router.get('/test/stats/:interviewUuid', async (req, res) => {
  try {
    const { interviewUuid } = req.params;
    const { ConversationHistoryService } = await import('../services/conversationHistoryService');
    const stats = await ConversationHistoryService.getConversationStats(interviewUuid);
    res.json({ success: true, data: stats });
  } catch (error: any) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get('/test/history/:interviewUuid', async (req, res) => {
  try {
    const { interviewUuid } = req.params;
    const { ConversationHistoryService } = await import('../services/conversationHistoryService');
    const history = await ConversationHistoryService.getConversationHistory(interviewUuid);
    res.json({
      success: true,
      data: {
        messageCount: history.length,
        messages: history.map(m => ({
          messageId: m.messageId,
          messageType: m.messageType,
          messageOrder: m.messageOrder,
          contentLength: m.content.length,
          timestamp: m.timestamp
        }))
      }
    });
  } catch (error: any) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// All other routes require authentication
router.use(authenticateToken);

// POST /api/conversation/message - Save a single conversation message
router.post('/message', saveConversationMessage);

// POST /api/conversation/exchange - Save a conversation exchange (user + assistant)
router.post('/exchange', saveConversationExchange);

// GET /api/conversation/context/:interviewUuid - Get conversation context
router.get('/context/:interviewUuid', getConversationContext);

// POST /api/conversation/context/:interviewUuid/complete - Get complete conversation context for LLM API
router.post('/context/:interviewUuid/complete', getCompleteConversationContext);

// GET /api/conversation/stats/:interviewUuid - Get conversation statistics
router.get('/stats/:interviewUuid', getConversationStats);

// DELETE /api/conversation/:interviewUuid - Clear conversation history
router.delete('/:interviewUuid', clearConversationHistory);

export default router;
