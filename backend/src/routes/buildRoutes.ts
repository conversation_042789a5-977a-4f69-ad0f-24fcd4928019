import express from 'express';
import {
  generateCode,
  handleChat,
  getCodeStructure,
  unifiedInitialBuild
} from '../controllers/buildController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// POST /api/build/unified - Unified initial build (replaces llm/generate + build/archive)
router.post('/unified', authenticateToken, unifiedInitialBuild);

// POST /api/build/code - Get code structure without chat messages
router.post('/code', authenticateToken, getCodeStructure);

// Legacy archive routes removed - use /unified instead

// POST /api/build/generate - Generate code with streaming chat messages
router.post('/generate', authenticateToken, generateCode);

// POST /api/build/chat - Handle chat messages with AI
router.post('/chat', authenticateToken, handleChat);

export default router;