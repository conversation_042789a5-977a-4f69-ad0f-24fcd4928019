import { Schema, model, Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { getDefaultLifetimeMs } from '../config/aws';

// ECS Container lifecycle events
export type ECSContainerEvent = 'created' | 'starting' | 'started' | 'stopping' | 'stopped' | 'failed' | 'expired' | 'cleaned_up';

// ECS Container resource usage
export interface ResourceUsage {
  cpuUtilization?: number;
  memoryUtilization?: number;
  networkIn?: number;
  networkOut?: number;
  timestamp: Date;
}

// ECS Container lifecycle event log
export interface ContainerEvent {
  event: ECSContainerEvent;
  timestamp: Date;
  message?: string;
  metadata?: any;
}

export interface IECSContainer extends Document {
  uuid: string;
  workspaceId: string;
  userId: string;
  interviewUuid: string;
  
  // ECS Configuration
  clusterName: string;
  taskDefinitionArn: string;
  taskArn?: string;
  serviceArn?: string;
  containerName: string;
  
  // Container Status
  status: ECSContainerEvent;
  healthStatus?: 'healthy' | 'unhealthy' | 'unknown';
  
  // Networking
  publicIp?: string;
  privateIp?: string;
  port: number;
  previewUrl?: string;
  previewHostname?: string;
  
  // Lifecycle
  startedAt?: Date;
  stoppedAt?: Date;
  expiresAt: Date;
  lastHealthCheck?: Date;
  
  // Resource Management
  cpu: number;
  memory: number;
  resourceUsage: ResourceUsage[];
  
  // Event Log
  events: ContainerEvent[];
  
  // Error Tracking
  errorCount: number;
  lastError?: string;
  
  createdAt: Date;
  updatedAt: Date;

  // Methods
  addEvent(event: ECSContainerEvent, message?: string, metadata?: any): Promise<void>;
  recordError(error: string): Promise<void>;
  isExpired(): boolean;
  markHealthy(): Promise<void>;
  markUnhealthy(): Promise<void>;
  extendLifetime(hours: number): Promise<void>;
}

const ResourceUsageSchema = new Schema({
  cpuUtilization: { type: Number, required: false },
  memoryUtilization: { type: Number, required: false },
  networkIn: { type: Number, required: false },
  networkOut: { type: Number, required: false },
  timestamp: { type: Date, required: true, default: Date.now }
}, { _id: false });

const ContainerEventSchema = new Schema({
  event: {
    type: String,
    enum: ['created', 'starting', 'started', 'stopping', 'stopped', 'failed', 'expired', 'cleaned_up'],
    required: true
  },
  timestamp: { type: Date, required: true, default: Date.now },
  message: { type: String, required: false },
  metadata: { type: Schema.Types.Mixed, required: false }
}, { _id: false });

const ECSContainerSchema = new Schema<IECSContainer>(
  {
    uuid: {
      type: String,
      required: true,
      unique: true,
      default: uuidv4
    },
    workspaceId: {
      type: String,
      required: true,
      index: true
    },
    userId: {
      type: String,
      required: true,
      index: true
    },
    interviewUuid: {
      type: String,
      required: true,
      index: true
    },
    
    // ECS Configuration
    clusterName: {
      type: String,
      required: true,
      default: 'mergen-workspace-cluster'
    },
    taskDefinitionArn: {
      type: String,
      required: true
    },
    taskArn: {
      type: String,
      required: false,
      index: true
    },
    serviceArn: {
      type: String,
      required: false,
      index: true
    },
    containerName: {
      type: String,
      required: true,
      default: 'workspace-container'
    },
    
    // Container Status
    status: {
      type: String,
      enum: ['created', 'starting', 'started', 'stopping', 'stopped', 'failed', 'expired', 'cleaned_up'],
      required: true,
      default: 'created',
      index: true
    },
    healthStatus: {
      type: String,
      enum: ['healthy', 'unhealthy', 'unknown'],
      required: false,
      default: 'unknown'
    },
    
    // Networking
    publicIp: {
      type: String,
      required: false
    },
    privateIp: {
      type: String,
      required: false
    },
    port: {
      type: Number,
      required: true,
      default: 3000
    },
    previewUrl: {
      type: String,
      required: false
    },
    previewHostname: {
      type: String,
      required: false
    },
    
    // Lifecycle
    startedAt: {
      type: Date,
      required: false
    },
    stoppedAt: {
      type: Date,
      required: false
    },
    expiresAt: {
      type: Date,
      required: true,
      index: true,
      default: () => new Date(Date.now() + getDefaultLifetimeMs()) // Uses centralized config
    },
    lastHealthCheck: {
      type: Date,
      required: false
    },
    
    // Resource Management
    cpu: {
      type: Number,
      required: true,
      default: 256 // 0.25 vCPU
    },
    memory: {
      type: Number,
      required: true,
      default: 512 // 512 MB
    },
    resourceUsage: {
      type: [ResourceUsageSchema],
      default: []
    },
    
    // Event Log
    events: {
      type: [ContainerEventSchema],
      default: []
    },
    
    // Error Tracking
    errorCount: {
      type: Number,
      required: true,
      default: 0
    },
    lastError: {
      type: String,
      required: false
    }
  },
  {
    timestamps: true,
    collection: 'ecs_containers'
  }
);

// Indexes for efficient queries
ECSContainerSchema.index({ userId: 1, interviewUuid: 1 });
ECSContainerSchema.index({ status: 1, expiresAt: 1 });
ECSContainerSchema.index({ taskArn: 1 });
ECSContainerSchema.index({ serviceArn: 1 });
ECSContainerSchema.index({ createdAt: -1 });

// Instance methods for container lifecycle management
ECSContainerSchema.methods.addEvent = function(event: ECSContainerEvent, message?: string, metadata?: any) {
  this.events.push({
    event,
    timestamp: new Date(),
    message,
    metadata
  });
  this.status = event;
  return this.save();
};

ECSContainerSchema.methods.updateResourceUsage = function(usage: Omit<ResourceUsage, 'timestamp'>) {
  this.resourceUsage.push({
    ...usage,
    timestamp: new Date()
  });
  
  // Keep only last 100 resource usage entries
  if (this.resourceUsage.length > 100) {
    this.resourceUsage = this.resourceUsage.slice(-100);
  }
  
  return this.save();
};

ECSContainerSchema.methods.recordError = function(error: string) {
  this.errorCount += 1;
  this.lastError = error;
  return this.addEvent('failed', error);
};

ECSContainerSchema.methods.isExpired = function() {
  return new Date() > this.expiresAt;
};

ECSContainerSchema.methods.extendLifetime = function(hours: number = 1) {
  this.expiresAt = new Date(Date.now() + hours * 60 * 60 * 1000);
  return this.save();
};

ECSContainerSchema.methods.markHealthy = function() {
  this.healthStatus = 'healthy';
  this.lastHealthCheck = new Date();
  return this.save();
};

ECSContainerSchema.methods.markUnhealthy = function() {
  this.healthStatus = 'unhealthy';
  this.lastHealthCheck = new Date();
  return this.save();
};

export const ECSContainer = model<IECSContainer>('ECSContainer', ECSContainerSchema);
