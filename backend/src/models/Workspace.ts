import { Schema, model, Document } from 'mongoose';
import { getDefaultLifetimeMs } from '../config/aws';

// ECS Container status types
export type ECSContainerStatus = 'creating' | 'starting' | 'started' | 'running' | 'stopping' | 'stopped' | 'failed';

// ECS Container configuration
export interface ECSContainerConfig {
  taskDefinitionArn: string;
  clusterName: string;
  serviceName: string;
  containerName: string;
  cpu: number;
  memory: number;
  port: number;
}

// S3 file storage metadata for persistence
export interface S3FileStorage {
  userEmail: string; // User email for S3 path structure
  s3Prefix: string; // S3 prefix: "user_email/interview_uuid/"
  lastSavedAt: Date;
  version: number;
  fileCount: number; // Number of files stored in S3
  totalSize: number; // Total size of files in bytes
  initialFilesUploaded?: boolean; // Track if initial files have been uploaded to container
}

export interface IWorkspace extends Document {
  userId: string;
  interviewUuid: string;

  // ECS Container Information
  ecsTaskArn?: string;
  ecsServiceArn?: string;
  ecsClusterName: string;
  ecsTaskDefinitionArn: string;
  containerStatus: ECSContainerStatus;
  containerConfig: ECSContainerConfig;

  // Container networking
  publicIp?: string;
  privateIp?: string;
  previewUrl?: string;
  previewHostname?: string;

  // Lifecycle management
  expiresAt: Date;
  lastAccessedAt: Date;
  autoCleanup: boolean;

  // S3 file storage persistence
  s3FileStorage?: S3FileStorage;

  // Legacy Sphere Engine fields (for backward compatibility)
  sphereEngineWorkspaceId?: string;
  sphereEngineProjectId?: string;

  createdAt: Date;
  updatedAt: Date;

  // Methods
  isExpired(): boolean;
  extendLifetime(hours: number): Promise<void>;
  saveFilesToS3(files: { [key: string]: string }, userEmail: string, mergeFiles?: boolean): Promise<void>;
  updateLastAccessed(): Promise<void>;
  shouldCleanup(): boolean;
}

const ECSContainerConfigSchema = new Schema({
  taskDefinitionArn: { type: String, required: true },
  clusterName: { type: String, required: true },
  serviceName: { type: String, required: true },
  containerName: { type: String, required: true },
  cpu: { type: Number, required: true },
  memory: { type: Number, required: true },
  port: { type: Number, required: true }
}, { _id: false });

const S3FileStorageSchema = new Schema({
  userEmail: { type: String, required: true },
  s3Prefix: { type: String, required: true },
  lastSavedAt: { type: Date, required: true, default: Date.now },
  version: { type: Number, required: true, default: 1 },
  fileCount: { type: Number, required: true, default: 0 },
  totalSize: { type: Number, required: true, default: 0 },
  initialFilesUploaded: { type: Boolean, required: false, default: false }
}, { _id: false });

const WorkspaceSchema = new Schema<IWorkspace>(
  {
    userId: {
      type: String,
      required: true,
      index: true
    },
    interviewUuid: {
      type: String,
      required: true,
      index: true
    },

    // ECS Container Information
    ecsTaskArn: {
      type: String,
      required: false,
      index: true
    },
    ecsServiceArn: {
      type: String,
      required: false,
      index: true
    },
    ecsClusterName: {
      type: String,
      required: true,
      default: 'mergen-workspace-cluster'
    },
    ecsTaskDefinitionArn: {
      type: String,
      required: true
    },
    containerStatus: {
      type: String,
      enum: ['creating', 'starting', 'started', 'running', 'stopping', 'stopped', 'failed'],
      required: true,
      default: 'creating',
      index: true
    },
    containerConfig: {
      type: ECSContainerConfigSchema,
      required: true
    },

    // Container networking
    publicIp: {
      type: String,
      required: false
    },
    privateIp: {
      type: String,
      required: false
    },
    previewUrl: {
      type: String,
      required: false
    },
    previewHostname: {
      type: String,
      required: false
    },

    // Lifecycle management
    expiresAt: {
      type: Date,
      required: true,
      index: true,
      default: () => new Date(Date.now() + getDefaultLifetimeMs()) // Uses centralized config
    },
    lastAccessedAt: {
      type: Date,
      required: true,
      default: Date.now,
      index: true
    },
    autoCleanup: {
      type: Boolean,
      required: true,
      default: true
    },

    // S3 file storage persistence
    s3FileStorage: {
      type: S3FileStorageSchema,
      required: false
    },

    // Legacy Sphere Engine fields (for backward compatibility)
    sphereEngineWorkspaceId: {
      type: String,
      required: false
    },
    sphereEngineProjectId: {
      type: String,
      required: false
    }
  },
  {
    timestamps: true,
    collection: 'workspaces'
  }
);

// Compound index for efficient lookups by user + interview combination
WorkspaceSchema.index({ userId: 1, interviewUuid: 1 }, { unique: true });

// Index for ECS task and service lookups
WorkspaceSchema.index({ ecsTaskArn: 1 });
WorkspaceSchema.index({ ecsServiceArn: 1 });

// Index for cleanup operations
WorkspaceSchema.index({ expiresAt: 1, autoCleanup: 1 });
WorkspaceSchema.index({ containerStatus: 1, expiresAt: 1 });

// Instance methods for workspace lifecycle management
WorkspaceSchema.methods.updateLastAccessed = function() {
  this.lastAccessedAt = new Date();
  return this.save();
};

WorkspaceSchema.methods.extendLifetime = function(hours: number = 1) {
  this.expiresAt = new Date(Date.now() + hours * 60 * 60 * 1000);
  this.lastAccessedAt = new Date();
  return this.save();
};

WorkspaceSchema.methods.saveFilesToS3 = async function(files: { [key: string]: string }, userEmail: string, mergeFiles: boolean = true) {
  const { S3FileService } = await import('../services/s3FileService');
  const s3Service = new S3FileService();
  
  // If merging is enabled, download existing files first
  let filesToSave = files;
  if (mergeFiles && this.s3FileStorage) {
    const existingFilesResult = await s3Service.downloadWorkspaceFiles(userEmail, this.interviewUuid);
    if (existingFilesResult.success && existingFilesResult.data?.files) {
      filesToSave = { ...existingFilesResult.data.files, ...files };
    }
  }

  // Upload files to S3
  const uploadResult = await s3Service.uploadFiles(userEmail, this.interviewUuid, filesToSave);
  
  if (uploadResult.success) {
    // Calculate total size
    const totalSize = Object.values(filesToSave).reduce((sum, content) => sum + content.length, 0);
    
    // Remove workspace_ prefix if present for S3 prefix
    const cleanWorkspaceName = this.interviewUuid.replace(/^workspace_/, '');
    
    this.s3FileStorage = {
      userEmail,
      s3Prefix: `${userEmail}/${cleanWorkspaceName}/`,
      lastSavedAt: new Date(),
      version: (this.s3FileStorage?.version || 0) + 1,
      fileCount: Object.keys(filesToSave).length,
      totalSize
    };
    
    return this.save();
  } else {
    throw new Error(`Failed to save files to S3: ${uploadResult.message}`);
  }
};

WorkspaceSchema.methods.isExpired = function() {
  return new Date() > this.expiresAt;
};

WorkspaceSchema.methods.shouldCleanup = function() {
  return this.autoCleanup && this.isExpired();
};

WorkspaceSchema.methods.updateLastAccessed = function() {
  this.lastAccessedAt = new Date();
  return this.save();
};

export const Workspace = model<IWorkspace>('Workspace', WorkspaceSchema);
