import { Schema, model, Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

// Interface for individual project files
interface ProjectFile {
  path: string;
  content: string;
  language: string;
  lastModified: Date;
}

// Interface for project structure
interface ProjectStructure {
  [key: string]: any;
}

export interface IProject extends Document {
  uuid: string;
  interviewUuid?: string;
  name: string;
  description?: string;
  originalStructure: ProjectStructure;
  files: ProjectFile[];
  createdAt: Date;
  updatedAt: Date;
}

const ProjectFileSchema = new Schema({
  path: {
    type: String,
    required: true
  },
  content: {
    type: String,
    required: true
  },
  language: {
    type: String,
    required: true
  },
  lastModified: {
    type: Date,
    default: Date.now
  }
}, { _id: false });

const ProjectSchema = new Schema<IProject>(
  {
    uuid: {
      type: String,
      required: true,
      unique: true,
      default: uuidv4
    },
    interviewUuid: {
      type: String,
      required: false
    },
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      required: false,
      trim: true
    },
    originalStructure: {
      type: Schema.Types.Mixed,
      required: true,
      default: {}
    },
    files: {
      type: [ProjectFileSchema],
      default: []
    }
  },
  {
    timestamps: true,
    collection: 'projects'
  }
);

// Index for faster interviewUuid lookups
ProjectSchema.index({ interviewUuid: 1 });

export const Project = model<IProject>('Project', ProjectSchema); 