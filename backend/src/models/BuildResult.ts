import { Schema, model, Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

// Interface for individual code blocks extracted from LLM response
interface CodeBlock {
  language: string;
  filename?: string;
  content: string;
}

// Interface for LLM metadata
interface LLMMetadata {
  prompt: string;
  model: string;
  tokens?: number;
  timestamp: Date;
}

// Interface for chat messages
interface ChatMessage {
  id: string;
  type: 'ai' | 'user';
  content: string;
  timestamp: Date;
}

export interface IBuildResult extends Document {
  uuid: string;
  interviewUuid?: string;
  userId?: string;

  // Raw LLM response
  rawLLMResponse: string;

  // Extracted content
  description?: string;
  codeBlocks: CodeBlock[];
  projectStructure?: any;
  deploymentInstructions?: string;
  additionalSections?: { [key: string]: string };

  // LLM metadata
  llmMetadata: LLMMetadata;

  // Chat history
  chatHistory: ChatMessage[];

  // Processing status
  status: 'processing' | 'completed' | 'failed';
  errorMessage?: string;

  // ECS Workspace Information
  ecsWorkspaceId?: string;
  ecsContainerId?: string;
  workspaceStatus?: 'creating' | 'running' | 'stopped' | 'failed';
  previewUrl?: string;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

const CodeBlockSchema = new Schema({
  language: {
    type: String,
    required: true
  },
  filename: {
    type: String,
    required: false
  },
  content: {
    type: String,
    required: true
  }
}, { _id: false });

const ChatMessageSchema = new Schema({
  id: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['ai', 'user'],
    required: true
  },
  content: {
    type: String,
    required: true
  },
  timestamp: {
    type: Date,
    required: true
  }
}, { _id: false });

const LLMMetadataSchema = new Schema({
  prompt: {
    type: String,
    required: true
  },
  model: {
    type: String,
    required: true
  },
  tokens: {
    type: Number,
    required: false
  },
  timestamp: {
    type: Date,
    required: true
  }
}, { _id: false });

const BuildResultSchema = new Schema<IBuildResult>(
  {
    uuid: {
      type: String,
      required: true,
      unique: true,
      default: uuidv4
    },
    interviewUuid: {
      type: String,
      required: false,
      index: true
    },
    userId: {
      type: String,
      required: false,
      index: true
    },
    rawLLMResponse: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: false,
      default: ''
    },
    codeBlocks: {
      type: [CodeBlockSchema],
      default: []
    },
    projectStructure: {
      type: Schema.Types.Mixed,
      required: false
    },
    deploymentInstructions: {
      type: String,
      required: false,
      default: ''
    },
    additionalSections: {
      type: Schema.Types.Mixed,
      required: false,
      default: {}
    },
    chatHistory: {
      type: [ChatMessageSchema],
      default: []
    },
    llmMetadata: {
      type: LLMMetadataSchema,
      required: true
    },
    status: {
      type: String,
      enum: ['processing', 'completed', 'failed'],
      default: 'processing'
    },
    errorMessage: {
      type: String,
      required: false
    },

    // ECS Workspace Information
    ecsWorkspaceId: {
      type: String,
      required: false,
      index: true
    },
    ecsContainerId: {
      type: String,
      required: false,
      index: true
    },
    workspaceStatus: {
      type: String,
      enum: ['creating', 'running', 'stopped', 'failed'],
      required: false
    },
    previewUrl: {
      type: String,
      required: false
    }
  },
  {
    timestamps: true,
    collection: 'build_results'
  }
);

// Instance methods for chat history management
BuildResultSchema.methods.addChatMessage = function(message: ChatMessage) {
  this.chatHistory.push(message);
  return this.save();
};

BuildResultSchema.methods.updateChatHistory = function(messages: ChatMessage[]) {
  this.chatHistory = messages;
  return this.save();
};

BuildResultSchema.methods.getChatHistory = function() {
  return this.chatHistory;
};

// Indexes for faster queries
BuildResultSchema.index({ interviewUuid: 1, createdAt: -1 });
BuildResultSchema.index({ userId: 1, createdAt: -1 });
BuildResultSchema.index({ status: 1 });
BuildResultSchema.index({ ecsWorkspaceId: 1 });
BuildResultSchema.index({ ecsContainerId: 1 });

export const BuildResult = model<IBuildResult>('BuildResult', BuildResultSchema);
