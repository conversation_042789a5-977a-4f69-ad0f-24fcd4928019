import { Schema, model, Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

// Interface for Anthropic API message format
export interface AnthropicMessage {
  role: 'user' | 'assistant';
  content: string;
}

// Interface for conversation message document
export interface IConversationMessage extends Document {
  messageId: string;
  interviewUuid: string;
  messageType: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  messageOrder: number;
  
  // Optional LLM metadata
  inputTokens?: number;
  outputTokens?: number;
  totalTokens?: number;
  llmModel?: string;
  parentMessageId?: string;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

const ConversationMessageSchema = new Schema<IConversationMessage>(
  {
    messageId: {
      type: String,
      required: true,
      unique: true,
      default: uuidv4
    },
    interviewUuid: {
      type: String,
      required: true,
      index: true
    },
    messageType: {
      type: String,
      enum: ['user', 'assistant'],
      required: true
    },
    content: {
      type: String,
      required: true
    },
    timestamp: {
      type: Date,
      required: true,
      default: Date.now
    },
    messageOrder: {
      type: Number,
      required: true
    },
    inputTokens: {
      type: Number,
      required: false
    },
    outputTokens: {
      type: Number,
      required: false
    },
    totalTokens: {
      type: Number,
      required: false
    },
    llmModel: {
      type: String,
      required: false
    },
    parentMessageId: {
      type: String,
      required: false
    }
  },
  {
    timestamps: true,
    collection: 'conversation_messages'
  }
);

// Compound index for efficient querying by interview and order
ConversationMessageSchema.index({ interviewUuid: 1, messageOrder: 1 });
ConversationMessageSchema.index({ interviewUuid: 1, timestamp: 1 });

// Instance methods
ConversationMessageSchema.methods.toAnthropicFormat = function(): AnthropicMessage {
  return {
    role: this.messageType,
    content: this.content
  };
};

export const ConversationMessage = model<IConversationMessage>('ConversationMessage', ConversationMessageSchema);
