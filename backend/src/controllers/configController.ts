import { Request, Response } from 'express';
import { getAWSConfig } from '../config/aws';
import { getAppConfig } from '../config/app';

// GET /api/config - Get public configuration values
export const getPublicConfig = async (req: Request, res: Response) => {
  try {
    const awsConfig = getAWSConfig();
    const appConfig = getAppConfig();
    
    // Only expose safe configuration values to frontend
    const publicConfig = {
      // AWS/Lifecycle Configuration
      defaultLifetimeHours: awsConfig.defaultLifetimeHours,
      extendLifetimeHours: awsConfig.extendLifetimeHours,
      cleanupIntervalMinutes: awsConfig.cleanupIntervalMinutes,
      syncIntervalMinutes: awsConfig.syncIntervalMinutes,
      
      // Frontend Configuration
      frontendLifetimeExtensionThresholdMs: appConfig.frontendLifetimeExtensionThresholdMs,
      frontendThrottleIntervalMs: appConfig.frontendThrottleIntervalMs,
      frontendCheckCadenceMs: appConfig.frontendCheckCadenceMs,
      autoExtensionThresholdMinutes: appConfig.autoExtensionThresholdMinutes
    };

    res.status(200).json({
      success: true,
      data: publicConfig
    });
  } catch (error: any) {
    console.error('Error fetching public config:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching configuration',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}; 