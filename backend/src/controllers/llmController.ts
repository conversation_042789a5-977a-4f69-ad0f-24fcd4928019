import { Request, Response } from 'express';
import fs from 'fs';
import path from 'path';
import { AuthenticatedRequest } from '../middleware/auth';
import { InterviewConfig } from '../models/InterviewConfig';
import { BuildService } from '../services/buildService';
import { AnthropicService } from '../services/anthropicService';
import { BuildResult } from '../models/BuildResult';
import { CodeExtractor } from '../utils/codeExtractor';
import { ConversationHistoryService } from '../services/conversationHistoryService';

interface LLMRequest {
  interviewUuid?: string;
  prompt?: string;
  projectData?: any;
  userData?: any;
}

interface ChatMessage {
  id: string;
  type: 'ai' | 'user';
  content: string;
  timestamp: Date;
}

interface LLMResponse {
  success: boolean;
  content: string;
  buildResultUuid?: string;
  extractedContent?: {
    description?: string;
    codeBlocks: any[];
    projectStructure?: any;
    deploymentInstructions?: string;
    additionalSections?: { [key: string]: string };
  };
  metadata: {
    prompt: string;
    interviewUuid?: string;
    timestamp: Date;
    model: string;
    tokens?: number;
    inputTokens?: number;
    outputTokens?: number;
  };
}

/**
 * LLM API endpoint that generates responses using Anthropic's Claude API
 * Creates and saves BuildResult to database immediately
 */
export const generateLLMResponse = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid, prompt, projectData, userData }: LLMRequest = req.body;

    // Validate required fields
    if (!prompt && !interviewUuid) {
      return res.status(400).json({
        success: false,
        message: 'Either prompt or interviewUuid is required'
      });
    }

    // Get interview data if UUID provided
    let interviewData = null;
    let effectivePrompt = prompt;
    let effectiveProjectData = projectData;

    if (interviewUuid) {
      try {
        interviewData = await InterviewConfig.findOne({ uuid: interviewUuid });
        if (interviewData?.user?.prompt) {
          effectivePrompt = interviewData.user.prompt;
        }
        if (interviewData?.projectData) {
          effectiveProjectData = interviewData.projectData;
        }
      } catch (error) {
        console.error('Error fetching interview data:', error);
      }
    }

    // Validate that we have the required data
    if (!effectivePrompt) {
      return res.status(400).json({
        success: false,
        message: 'No prompt available from request or interview data'
      });
    }

    if (!effectiveProjectData) {
      return res.status(400).json({
        success: false,
        message: 'No project data available from request or interview data'
      });
    }

    // Skip API configuration check in mock mode
    console.log('🧪 MOCK MODE: Skipping Anthropic API configuration check');

    console.log('🚀 Generating LLM response using Anthropic API for interview:', interviewUuid);

    // Generate response using Anthropic API
    const { content, metadata } = await AnthropicService.generateResponse(
      effectivePrompt,
      effectiveProjectData,
      userData
    );

    console.log('✅ Anthropic API response generated successfully, processing content...');

    // Create BuildResult immediately when llmAPI.generate is called
    console.log('🔥 DEBUG: Creating BuildResult from llmAPI.generate for interview:', interviewUuid);
    const buildResult = await BuildService.processLLMResponse({
      interviewUuid,
      userId: req.user?.userId,
      rawLLMResponse: content,
      llmMetadata: metadata
    });

    if (buildResult.success && buildResult.buildResult) {
      console.log('✅ DEBUG: BuildResult created successfully from llmAPI.generate with UUID:', buildResult.buildResult.uuid);

      // Save the initial project build to conversation history
      if (interviewUuid) {
        try {
          console.log('💾 Saving initial project build to conversation history for interview:', interviewUuid);

          // Format the project data and prompt as the initial user message
          const initialUserMessage = `Project Requirements:\n\nPrompt: ${effectivePrompt}\n\nProject Data:\n${JSON.stringify(effectiveProjectData, null, 2)}`;

          await ConversationHistoryService.saveMessagePair(
            interviewUuid,
            initialUserMessage,
            content,
            {
              inputTokens: metadata.inputTokens,
              outputTokens: metadata.outputTokens,
              totalTokens: metadata.tokens,
              model: metadata.model
            }
          );

          console.log('✅ Initial project build saved to conversation history');
        } catch (conversationError) {
          console.error('❌ Error saving initial project build to conversation history:', conversationError);
          // Don't fail the request if conversation saving fails
        }
      } else {
        console.log('⚠️ No interviewUuid provided, skipping conversation history save');
      }

      const response: LLMResponse = {
        success: true,
        content: content,
        buildResultUuid: buildResult.buildResult.uuid,
        extractedContent: {
          description: buildResult.buildResult.description,
          codeBlocks: buildResult.buildResult.codeBlocks,
          projectStructure: buildResult.buildResult.projectStructure,
          deploymentInstructions: buildResult.buildResult.deploymentInstructions,
          additionalSections: buildResult.buildResult.additionalSections
        },
        metadata: {
          prompt: metadata.prompt,
          interviewUuid,
          timestamp: metadata.timestamp,
          model: metadata.model,
          tokens: metadata.tokens,
          inputTokens: metadata.inputTokens,
          outputTokens: metadata.outputTokens
        }
      };

      res.json(response);
    } else {
      console.error('❌ DEBUG: Failed to create BuildResult from llmAPI.generate:', buildResult.error);
      return res.status(500).json({
        success: false,
        message: 'Failed to create build result',
        error: buildResult.error
      });
    }

  } catch (error: any) {
    console.error('Error generating LLM response:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate LLM response',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get build result by interview UUID
 */
export const getBuildResult = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid } = req.params;

    if (!interviewUuid) {
      return res.status(400).json({
        success: false,
        message: 'Interview UUID is required'
      });
    }

    const buildResult = await BuildService.getBuildResultByInterview(interviewUuid);

    if (!buildResult) {
      return res.status(404).json({
        success: false,
        message: 'Build result not found'
      });
    }

    res.json({
      success: true,
      data: buildResult
    });

  } catch (error: any) {
    console.error('Error fetching build result:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch build result',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Health check endpoint for LLM service
 */
export const llmHealthCheck = async (req: Request, res: Response) => {
  try {
    // Get actual Anthropic API configuration status
    const anthropicConfig = AnthropicService.getConfigStatus();
    const bypassApiKey = process.env.BYPASS_API_KEY === 'true';

    // Check if sample response file exists (for fallback)
    const sampleResponsePath = path.join(process.cwd(), '..', 'doc', 'sample_response');
    const fileExists = await fs.promises.access(sampleResponsePath).then(() => true).catch(() => false);

    res.json({
      success: true,
      status: 'healthy',
      mode: bypassApiKey ? 'mock' : 'production',
      anthropicAPI: {
        configured: anthropicConfig.configured,
        model: anthropicConfig.model,
        maxTokens: anthropicConfig.maxTokens
      },
      fallbackDataAvailable: fileExists,
      bypassApiKey: bypassApiKey,
      timestamp: new Date()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      status: 'unhealthy',
      error: error.message
    });
  }
};

/**
 * Get chat history for a build result
 */
export const getChatHistory = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { buildResultUuid } = req.params;

    const buildResult = await BuildResult.findOne({ uuid: buildResultUuid });

    if (!buildResult) {
      return res.status(404).json({
        success: false,
        message: 'Build result not found'
      });
    }

    res.json({
      success: true,
      chatHistory: buildResult.chatHistory || []
    });
  } catch (error: any) {
    console.error('Error getting chat history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get chat history'
    });
  }
};

/**
 * Update chat history for a build result
 */
export const updateChatHistory = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { buildResultUuid } = req.params;
    const { chatHistory }: { chatHistory: ChatMessage[] } = req.body;

    console.log('💾 Updating chat history for BuildResult:', buildResultUuid, 'with', chatHistory.length, 'messages');

    // Use atomic update to avoid version conflicts
    const result = await BuildResult.findOneAndUpdate(
      { uuid: buildResultUuid },
      { chatHistory: chatHistory },
      { new: true, runValidators: true }
    );

    if (!result) {
      return res.status(404).json({
        success: false,
        message: 'Build result not found'
      });
    }

    console.log('✅ Chat history updated successfully for BuildResult:', buildResultUuid);

    res.json({
      success: true,
      message: 'Chat history updated successfully'
    });
  } catch (error: any) {
    console.error('Error updating chat history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update chat history'
    });
  }
};

/**
 * Check if a build result exists for an interview
 */
export const checkBuildExists = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid } = req.params;

    // Validate interviewUuid parameter
    if (!interviewUuid || typeof interviewUuid !== 'string' || interviewUuid.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid interview UUID is required'
      });
    }

    // FIXED: Prefer BuildResults with chat history, fall back to any completed BuildResult
    // First, try to find a BuildResult with chat history (preferred)
    let buildResult = await BuildResult.findOne({
      interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true },
      status: 'completed',
      chatHistory: { $exists: true, $ne: [], $not: { $size: 0 } }
    }).sort({ createdAt: -1 });

    // If no BuildResult with chat history, fall back to any completed BuildResult
    if (!buildResult) {
      buildResult = await BuildResult.findOne({
        interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true },
        status: 'completed'
      }).sort({ createdAt: -1 });
    }

    if (!buildResult) {
      return res.json({
        success: true,
        exists: false
      });
    }

    res.json({
      success: true,
      exists: true,
      buildResult: {
        uuid: buildResult.uuid,
        description: buildResult.description,
        codeBlocks: buildResult.codeBlocks,
        projectStructure: buildResult.projectStructure,
        deploymentInstructions: buildResult.deploymentInstructions,
        additionalSections: buildResult.additionalSections,
        chatHistory: buildResult.chatHistory,
        createdAt: buildResult.createdAt
      }
    });
  } catch (error: any) {
    console.error('Error checking build exists:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check build existence'
    });
  }
};
