import { Request, Response } from 'express';
import { Project } from '../models/Project';
import { InterviewConfig } from '../models/InterviewConfig';
import { CodeExtractor } from '../utils/codeExtractor';
import { AuthenticatedRequest } from '../middleware/auth';

interface SaveProjectRequest {
  interviewUuid?: string;
  name: string;
  description?: string;
  files?: Array<{
    path: string;
    content: string;
    language: string;
  }>;
}

interface UpdateFileRequest {
  path: string;
  content: string;
  language: string;
}

// Create or update a project
export const saveProject = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid, name, description, files }: SaveProjectRequest = req.body;

    // Get original structure from code extractor
    const originalStructure = await CodeExtractor.getCodeStructureFromFile('doc/generated_code2');

    // Create files array from original structure if not provided
    let projectFiles = files || [];
    if (projectFiles.length === 0 && originalStructure) {
      projectFiles = extractFilesFromStructure(originalStructure);
    }

    const project = new Project({
      interviewUuid,
      name,
      description,
      originalStructure,
      files: projectFiles.map(file => ({
        ...file,
        lastModified: new Date()
      }))
    });

    await project.save();

    res.json({
      success: true,
      data: project,
      message: 'Project saved successfully'
    });

  } catch (error: any) {
    console.error('Error saving project:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving project',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get project by interview UUID
export const getProjectByInterview = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid } = req.params;

    let project = await Project.findOne({ interviewUuid });

    if (!project) {
      // Create a new project from the original structure if it doesn't exist
      const originalStructure = await CodeExtractor.getCodeStructureFromFile('doc/generated_code2');
      const files = extractFilesFromStructure(originalStructure);

      project = new Project({
        interviewUuid,
        name: `Project ${interviewUuid}`,
        originalStructure,
        files: files.map(file => ({
          ...file,
          lastModified: new Date()
        }))
      });

      await project.save();
    }

    res.json({
      success: true,
      data: project,
      message: 'Project retrieved successfully'
    });

  } catch (error: any) {
    console.error('Error getting project:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving project',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update a specific file in the project
export const updateFile = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { projectUuid } = req.params;
    const { path, content, language }: UpdateFileRequest = req.body;

    const project = await Project.findOne({ uuid: projectUuid });

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Find existing file or create new one
    const fileIndex = project.files.findIndex(f => f.path === path);

    if (fileIndex >= 0) {
      // Update existing file
      project.files[fileIndex].content = content;
      project.files[fileIndex].language = language;
      project.files[fileIndex].lastModified = new Date();
    } else {
      // Add new file
      project.files.push({
        path,
        content,
        language,
        lastModified: new Date()
      });
    }

    await project.save();

    res.json({
      success: true,
      data: {
        file: project.files.find(f => f.path === path),
        project: project
      },
      message: 'File updated successfully'
    });

  } catch (error: any) {
    console.error('Error updating file:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating file',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Delete a file from the project
export const deleteFile = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { projectUuid } = req.params;
    const { path } = req.body;

    const project = await Project.findOne({ uuid: projectUuid });

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Remove file from array
    project.files = project.files.filter(f => f.path !== path);

    await project.save();

    res.json({
      success: true,
      data: project,
      message: 'File deleted successfully'
    });

  } catch (error: any) {
    console.error('Error deleting file:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting file',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all projects for a user
export const getUserProjects = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId } = req.params;

    // Find projects through interview configs
    const interviews = await InterviewConfig.find({ 'user.id': userId }).select('uuid');
    const interviewUuids = interviews.map(i => i.uuid);

    const projects = await Project.find({
      interviewUuid: { $in: interviewUuids }
    }).sort({ updatedAt: -1 });

    res.json({
      success: true,
      data: projects,
      message: 'Projects retrieved successfully'
    });

  } catch (error: any) {
    console.error('Error getting user projects:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving projects',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Rename a project by UUID
export const renameProject = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { projectUuid } = req.params;
    const { name } = req.body as { name?: string };

    if (!name || name.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Project name is required'
      });
    }

    const project = await Project.findOne({ uuid: projectUuid });

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    project.name = name.trim();
    await project.save();

    res.json({
      success: true,
      data: project,
      message: 'Project renamed successfully'
    });
  } catch (error: any) {
    console.error('Error renaming project:', error);
    res.status(500).json({
      success: false,
      message: 'Error renaming project',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Delete a project by UUID
export const deleteProject = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { projectUuid } = req.params;

    const project = await Project.findOneAndDelete({ uuid: projectUuid });

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    res.json({
      success: true,
      data: { uuid: projectUuid },
      message: 'Project deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting project:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting project',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Helper function to extract files from project structure
function extractFilesFromStructure(structure: any, basePath: string = ''): Array<{
  path: string;
  content: string;
  language: string;
}> {
  const files: Array<{ path: string; content: string; language: string; }> = [];

  function traverse(obj: any, currentPath: string) {
    for (const [key, value] of Object.entries(obj)) {
      const fullPath = currentPath ? `${currentPath}/${key}` : key;

      if (value && typeof value === 'object') {
        const objValue = value as any;
        if (objValue.type === 'file') {
          files.push({
            path: fullPath,
            content: objValue.content || '',
            language: objValue.language || 'text'
          });
        } else if (objValue.type === 'folder' && objValue.children) {
          traverse(objValue.children, fullPath);
        } else if (!objValue.type) {
          // Handle nested objects without explicit type
          traverse(value, fullPath);
        }
      }
    }
  }

  traverse(structure, basePath);
  return files;
} 