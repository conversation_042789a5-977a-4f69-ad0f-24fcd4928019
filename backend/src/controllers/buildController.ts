import { Request, Response } from 'express';
import { InterviewConfig } from '../models/InterviewConfig';
import { CodeExtractor } from '../utils/codeExtractor';
import { AuthenticatedRequest } from '../middleware/auth';
import { BuildService } from '../services/buildService';
import { AnthropicService } from '../services/anthropicService';
import { BuildResult } from '../models/BuildResult';
import fs from 'fs';
import path from 'path';

// import { Workspace, IWorkspace } from '../models/Workspace'; // COMMENTED OUT FOR MONACO EDITOR
import { ConversationHistoryService } from '../services/conversationHistoryService';
import FormData from 'form-data';
import fetch from 'node-fetch';
import { getAWSConfig } from '../config/aws';
import { getAppConfig } from '../config/app';

// Helper function to get default lifetime from config
const getDefaultLifetime = () => {
  const config = getAWSConfig();
  return config.defaultLifetimeHours;
};

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

/**
 * Create initial chat history from BuildResult extracted content
 */
function createInitialChatHistory(buildResult: any): ChatMessage[] {
  const messages: ChatMessage[] = [];
  let messageId = 1;

  // 1. Initial welcome message
  messages.push({
    id: messageId.toString(),
    type: 'ai',
    content: '🚀 Starting project generation...',
    timestamp: new Date()
  });
  messageId++;

  // 2. Description message (if available)
  if (buildResult.description) {
    messages.push({
      id: messageId.toString(),
      type: 'ai',
      content: `📋 **Project Overview**\n\n${buildResult.description}`,
      timestamp: new Date()
    });
    messageId++;
  }

  // 3. Code generation message
  if (buildResult.codeBlocks && buildResult.codeBlocks.length > 0) {
    messages.push({
      id: messageId.toString(),
      type: 'ai',
      content: `💻 **Code Generation Complete**\n\nI've generated ${buildResult.codeBlocks.length} code files for your project. The code structure is ready for review.`,
      timestamp: new Date()
    });
    messageId++;
  }

  // 4. Deployment instructions (if available)
  if (buildResult.deploymentInstructions) {
    messages.push({
      id: messageId.toString(),
      type: 'ai',
      content: `🚀 **Deployment Instructions**\n\n${buildResult.deploymentInstructions}`,
      timestamp: new Date()
    });
    messageId++;
  }

  // 5. Additional sections (if available)
  if (buildResult.additionalSections && buildResult.additionalSections.length > 0) {
    buildResult.additionalSections.forEach((section: any) => {
      messages.push({
        id: messageId.toString(),
        type: 'ai',
        content: `📝 **${section.title}**\n\n${section.content}`,
        timestamp: new Date()
      });
      messageId++;
    });
  }

  // 6. Final completion message
  messages.push({
    id: messageId.toString(),
    type: 'ai',
    content: '✅ Project generation complete! Your code is ready for review and deployment.',
    timestamp: new Date()
  });

  console.log('✅ Created', messages.length, 'initial chat messages from BuildResult');
  return messages;
}

interface GenerateCodeRequest {
  interviewUuid?: string;
  prompt?: string;
  projectData?: any;
  userData?: any;
}

interface UnifiedBuildResponse {
  success: boolean;
  message: string;
  buildResult: {
    uuid: string;
    description?: string;
    deploymentInstructions?: string;
    additionalSections?: any;
    status: string;
    codeBlocks: any[];
    projectStructure?: any;
    chatHistory?: any[];
  };
  workspace: {
    workspaceId: string;
    workspaceUrl: string | null;
    status: 'ready' | 'pending' | 'error';
    filesUploaded: number;
    ecsReady: boolean;
    uploadSuccess: boolean;
    uploadedFiles: string[];
  };
  metadata: {
    interviewUuid?: string;
    effectivePrompt: string;
    timestamp: Date;
    workflow: string;
  };
}

interface ChatRequest {
  interviewUuid: string;
  message: string;
  conversationHistory?: ChatMessage[];
}

/**
 * Count files in workspace structure recursively
 */
function countFilesInStructure(structure: any): number {
  let count = 0;

  if (!structure || typeof structure !== 'object') {
    return 0;
  }

  for (const [key, value] of Object.entries(structure)) {
    if (value && typeof value === 'object') {
      if ((value as any).type === 'file') {
        count++;
      } else if ((value as any).type === 'folder' && (value as any).children) {
        // Handle children array format
        if (Array.isArray((value as any).children)) {
          const childrenObj: any = {};
          for (const child of (value as any).children) {
            childrenObj[child.name] = child;
          }
          count += countFilesInStructure(childrenObj);
        } else {
          count += countFilesInStructure((value as any).children);
        }
      }
    }
  }

  return count;
}

/**
 * Create a concise summary of the LLM response for chatbox display
 */
function createChatSummary(fullResponse: string, userMessage: string): string {
  try {
    // Extract key information from the response
    const lines = fullResponse.split('\n').filter(line => line.trim());

    // Look for file modifications/creations
    const fileMatches = fullResponse.match(/### ([^\n]+)/g) || [];
    const fileCount = fileMatches.length;

    // Look for key action words
    const hasCreated = /creat(e|ed|ing)/i.test(fullResponse);
    const hasModified = /modif(y|ied|ying)|updat(e|ed|ing)|chang(e|ed|ing)/i.test(fullResponse);
    const hasFixed = /fix(ed|ing)?|resolv(e|ed|ing)|correct(ed|ing)?/i.test(fullResponse);
    const hasAdded = /add(ed|ing)?|implement(ed|ing)?/i.test(fullResponse);

    // Create a concise summary based on detected actions
    let summary = "✅ I've processed your request";

    if (fileCount > 0) {
      summary += ` and updated ${fileCount} file${fileCount > 1 ? 's' : ''}`;
    }

    const actions = [];
    if (hasCreated) actions.push("created new components");
    if (hasModified) actions.push("modified existing code");
    if (hasFixed) actions.push("fixed issues");
    if (hasAdded) actions.push("added new features");

    if (actions.length > 0) {
      summary += `. I've ${actions.join(', ')}.`;
    } else {
      summary += ".";
    }

    // Add specific file mentions if reasonable length
    if (fileCount > 0 && fileCount <= 3) {
      const fileNames = fileMatches.map(match =>
        match.replace('### ', '').split('/').pop() // Get just filename
      ).join(', ');
      summary += ` Files updated: ${fileNames}.`;
    }

    // Add a helpful closing
    summary += " The changes have been applied to your workspace.";

    return summary;

  } catch (error) {
    console.error('❌ Error creating chat summary:', error);
    // Fallback to a generic but helpful message
    return "✅ I've processed your request and made the necessary changes to your codebase. The updates have been applied to your workspace.";
  }
}

// Helper function to wait for file to be ready with retries
const waitForFileReady = async (filePath: string, maxRetries: number = 5, delayMs: number = 1000): Promise<boolean> => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      // Check if file exists and is readable
      await fs.promises.access(filePath, fs.constants.R_OK);

      // Additional check: ensure file has content (not empty)
      const stats = await fs.promises.stat(filePath);
      if (stats.size > 0) {
        console.log(`✅ File ready after ${i + 1} attempts: ${filePath} (${stats.size} bytes)`);
        return true;
      }

      console.log(`⏳ File exists but empty, retrying... (${i + 1}/${maxRetries})`);
    } catch (error) {
      console.log(`⏳ File not ready, retrying... (${i + 1}/${maxRetries}): ${error}`);
    }

    if (i < maxRetries - 1) {
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }

  return false;
};



// New function for the enhanced build process with workspace archiving
/**
 * Unified initial build method that handles everything:
 * 1. Generate LLM response (if not exists)
 * 2. Process and save BuildResult
 * 3. Create workspace archive
 * 4. Upload to Sphere Engine
 * 5. Save to conversation history
 *
 * This replaces both llmController.generateLLMResponse and buildController.buildWithArchive
 * to eliminate race conditions and duplicates.
 */
export const unifiedInitialBuild = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid, prompt, projectData, userData }: GenerateCodeRequest = req.body;

    console.log('🚀 UNIFIED BUILD: Starting complete initial build workflow for:', interviewUuid);

    // Validate required fields
    if (!prompt && !interviewUuid) {
      return res.status(400).json({
        success: false,
        message: 'Either prompt or interviewUuid is required'
      });
    }

    // Get interview data if UUID provided
    let interviewData = null;
    let effectivePrompt = prompt;
    let effectiveProjectData = projectData;
    let effectiveUserData = userData;

    if (interviewUuid) {
      try {
        interviewData = await InterviewConfig.findOne({ uuid: interviewUuid });
        if (interviewData) {
          if (interviewData.user?.prompt) {
            effectivePrompt = interviewData.user.prompt;
          }
          if (interviewData.projectData) {
            effectiveProjectData = interviewData.projectData;
          }
          if (interviewData.user) {
            effectiveUserData = interviewData.user;
          }
        }
      } catch (error) {
        console.error('Error fetching interview data:', error);
      }
    }

    // Validate we have required data
    if (!effectivePrompt) {
      return res.status(400).json({
        success: false,
        message: 'No prompt available from request or interview data'
      });
    }

    if (!effectiveProjectData) {
      return res.status(400).json({
        success: false,
        message: 'No project data available from request or interview data'
      });
    }

    // Check if BuildResult already exists (avoid duplicates)
    let buildResult = null;
    if (interviewUuid) {
      buildResult = await BuildResult.findOne({
        interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true }
      }).sort({ createdAt: -1 });

      if (buildResult) {
        console.log('✅ UNIFIED BUILD: Found existing BuildResult, preserving chat history');
        console.log('✅ UNIFIED BUILD: Existing chat history length:', buildResult.chatHistory?.length || 0);
        console.log('✅ UNIFIED BUILD: BuildResult status:', buildResult.status);
        console.log('✅ UNIFIED BUILD: BuildResult UUID:', buildResult.uuid);

        // If the build is already completed and has chat history, return it immediately
        if (buildResult.status === 'completed' && buildResult.chatHistory && buildResult.chatHistory.length > 0) {
          console.log('🚀 UNIFIED BUILD: Existing completed build found with chat history, returning immediately');

          // Return the existing build result without modification
          return res.json({
            success: true,
            message: 'Existing build result returned',
            buildResult: {
              uuid: buildResult.uuid,
              description: buildResult.description,
              deploymentInstructions: buildResult.deploymentInstructions,
              additionalSections: buildResult.additionalSections,
              status: buildResult.status,
              codeBlocks: buildResult.codeBlocks,
              projectStructure: buildResult.projectStructure,
              chatHistory: buildResult.chatHistory
            },
            workspace: null, // We don't need to recreate workspace for existing builds
            metadata: {
              interviewUuid,
              effectivePrompt,
              timestamp: new Date(),
              workflow: 'existing-build-returned'
            }
          });
        }
      }
    }

    // Step 1: Start parallel operations - LLM generation and ECS workspace creation
    let llmPromise: Promise<any> | null = null;
    let ecsWorkspacePromise: Promise<any> | null = null;

    if (!buildResult) {
      console.log('🚀 UNIFIED BUILD: Starting parallel operations - LLM generation and ECS workspace creation');

      // Log the full initial build payload for debugging
      const initialBuildPayload = {
        prompt: effectivePrompt,
        projectData: effectiveProjectData,
        userData: effectiveUserData,
        interviewUuid,
        userId: req.user?.userId
      };

      console.log('📋 FULL INITIAL BUILD PAYLOAD CONTENT:');
      console.log(JSON.stringify(initialBuildPayload, null, 2));

      // Start LLM generation (async)
      console.log('🤖 UNIFIED BUILD: Step 1a - Starting LLM response generation (parallel)');
      llmPromise = AnthropicService.generateResponse(
        effectivePrompt,
        effectiveProjectData,
        effectiveUserData
      ).then(async ({ content, metadata }) => {
        console.log('✅ UNIFIED BUILD: LLM response generated, processing content...');

        // Process and save BuildResult
        console.log('💾 UNIFIED BUILD: Step 2 - Processing and saving BuildResult');
        const buildServiceResult = await BuildService.processLLMResponse({
          interviewUuid,
          userId: req.user?.userId,
          rawLLMResponse: content,
          llmMetadata: metadata
        });

        if (!buildServiceResult.success || !buildServiceResult.buildResult) {
          throw new Error(`Failed to process LLM response: ${buildServiceResult.error}`);
        }

        return buildServiceResult.buildResult;
      });

      // Start ECS workspace creation (async, without files initially)
      console.log('🏗️ UNIFIED BUILD: Step 1b - Starting ECS workspace creation (parallel)');
      const { ECSWorkspaceService } = await import('../services/ecsWorkspaceService');
      const ecsWorkspaceService = new ECSWorkspaceService();

      ecsWorkspacePromise = ecsWorkspaceService.getOrCreateWorkspace(
        `workspace_${interviewUuid}`,
        req.user?.userId || '',
        req.user?.email || '',
        getDefaultLifetime()
      ).then(result => {
        console.log('✅ UNIFIED BUILD: ECS workspace created/retrieved successfully');
        console.log('🆔 UNIFIED BUILD: ECS Workspace ID:', result?.workspaceId);
        return result;
      });

      // Wait for LLM response to complete first (we need the build result)
      console.log('⏳ UNIFIED BUILD: Waiting for LLM response to complete...');
      buildResult = await llmPromise;

      // Step 3: Save to conversation history (extract metadata from buildResult)
      console.log('💬 UNIFIED BUILD: Step 3 - Saving to conversation history');

      try {
        const initialUserMessage = `Project Requirements:\n\nPrompt: ${effectivePrompt}\n\nProject Data:\n${JSON.stringify(effectiveProjectData, null, 2)}`;

        // Extract metadata from buildResult
        const llmMetadata = buildResult.llmMetadata || {};

        await ConversationHistoryService.saveMessagePair(
          interviewUuid!,
          initialUserMessage,
          buildResult.rawLLMResponse || 'Generated code structure',
          {
            inputTokens: llmMetadata.inputTokens || 0,
            outputTokens: llmMetadata.outputTokens || 0,
            totalTokens: llmMetadata.tokens || 0,
            model: llmMetadata.model || 'claude-sonnet-4'
          }
        );

        console.log('✅ UNIFIED BUILD: Conversation history saved');
      } catch (conversationError) {
        console.error('❌ UNIFIED BUILD: Error saving conversation history:', conversationError);
        // Continue anyway - don't fail the build
      }
    } else {
      // BuildResult already exists, start ECS workspace creation for existing build
      console.log('📦 UNIFIED BUILD: BuildResult already exists, starting ECS workspace creation');

      const { ECSWorkspaceService } = await import('../services/ecsWorkspaceService');
      const ecsWorkspaceService = new ECSWorkspaceService();

      ecsWorkspacePromise = ecsWorkspaceService.getOrCreateWorkspace(
        `workspace_${interviewUuid}`,
        req.user?.userId || '',
        req.user?.email || '',
        getDefaultLifetime()
      ).then(result => {
        console.log('✅ UNIFIED BUILD: ECS workspace created/retrieved for existing build');
        console.log('🆔 UNIFIED BUILD: ECS Workspace ID:', result?.workspaceId);
        return result;
      }).catch(error => {
        console.error('❌ UNIFIED BUILD: Error creating ECS workspace for existing build:', error);
        return null;
      });
    }

    // Step 4: Extract files from project structure for ECS upload
    console.log('📦 UNIFIED BUILD: Step 4 - Extracting files for ECS workspace');

    // Track upload status for accurate response
    // IMPORTANT: These variables track the ACTUAL upload results, not just the extraction
    let actualFilesUploaded = 0;
    let uploadSuccess = false;
    let ecsWorkspaceReady = false;
    let uploadedFilesList: string[] = [];

    const files: { [path: string]: string } = {};

    // Extract files from code blocks
    if (buildResult.codeBlocks && Array.isArray(buildResult.codeBlocks)) {
      for (const block of buildResult.codeBlocks) {
        if (block.filename && block.content) {
          files[block.filename] = block.content;
        }
      }
    }

    console.log('✅ UNIFIED BUILD: Extracted', Object.keys(files).length, 'files for ECS upload');

    // Step 5: Wait for ECS workspace creation and upload files
    console.log('🚀 UNIFIED BUILD: Step 5 - Coordinating ECS workspace and file upload');

    // Declare ECS workspace result outside try block for response access
    let ecsWorkspaceResult = null;

    try {
      // Wait for ECS workspace creation to complete (if it was started)
      if (ecsWorkspacePromise) {
        console.log('⏳ UNIFIED BUILD: Waiting for ECS workspace creation to complete...');
        ecsWorkspaceResult = await ecsWorkspacePromise;
      }

      // Upload files to ECS workspace now that LLM response is ready
      if (buildResult.codeBlocks && buildResult.codeBlocks.length > 0 && ecsWorkspaceResult) {
        console.log('📁 UNIFIED BUILD: Uploading files to ECS workspace');
        console.log('📁 UNIFIED BUILD: Code blocks to process:', buildResult.codeBlocks.length);

        // Convert code blocks to files object for ECS workspace
        const files: { [filePath: string]: string } = {};
        const savedFiles: string[] = [];

        for (const codeBlock of buildResult.codeBlocks) {
          if (codeBlock.filename && codeBlock.content) {
            console.log('📁 UNIFIED BUILD: Preparing file:', codeBlock.filename, 'with content length:', codeBlock.content.length);
            files[codeBlock.filename] = codeBlock.content;
            savedFiles.push(codeBlock.filename);
          } else {
            console.log('⚠️ UNIFIED BUILD: Skipping code block - missing filename or content:', {
              hasFilename: !!codeBlock.filename,
              hasContent: !!codeBlock.content
            });
          }
        }

        // Upload files to the existing ECS workspace with retry mechanism
        if (Object.keys(files).length > 0) {
          const { ECSFileSystemService } = await import('../services/ecsFileSystemService');
          const ecsFileSystemService = new ECSFileSystemService();

          // Retry upload until workspace is ready
          const config = getAppConfig();
          const maxRetries = config.buildMaxRetries;
          const retryDelay = config.buildRetryDelayMs;
          let uploadResult = null;
          let retryCount = 0;

          console.log('📁 UNIFIED BUILD: Starting file upload with retry mechanism');

          while (retryCount < maxRetries) {
            try {
              console.log(`📁 UNIFIED BUILD: Upload attempt ${retryCount + 1}/${maxRetries}`);
              uploadResult = await ecsFileSystemService.saveFiles(ecsWorkspaceResult.workspaceId, files);

              if (uploadResult.success) {
                console.log('✅ UNIFIED BUILD: Files uploaded to ECS workspace, waiting for processing...');

                // Wait a moment for container to process files
                await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay

                console.log('📁 UNIFIED BUILD: Verifying files are available in workspace structure...');

                // Verify files are actually available by getting workspace structure
                try {
                  const { ECSFileSystemService } = await import('../services/ecsFileSystemService');
                  const ecsFileSystemService = new ECSFileSystemService();
                  const structure = await ecsFileSystemService.getWorkspaceStructure(ecsWorkspaceResult.workspaceId);

                  // Count files in structure to verify they're available
                  const availableFiles = countFilesInStructure(structure);
                  console.log(`📁 UNIFIED BUILD: Verification - found ${availableFiles} files in workspace structure`);

                  if (availableFiles >= savedFiles.length) {
                    console.log('✅ UNIFIED BUILD: Upload verified - all files are available in workspace structure');
                    console.log('✅ UNIFIED BUILD: Files confirmed uploaded and accessible:', savedFiles);
                    console.log('⚠️ UNIFIED BUILD: Files uploaded but ECS container may not be accessible yet');
                    actualFilesUploaded = savedFiles.length;
                    uploadSuccess = true;
                    // FIXED: Don't set ecsWorkspaceReady here - let the status endpoint verify container accessibility
                    ecsWorkspaceReady = false; // Container accessibility must be verified separately
                    uploadedFilesList = [...savedFiles];
                    break; // Success! Exit retry loop
                  } else {
                    console.log(`⚠️ UNIFIED BUILD: Upload verification failed - expected ${savedFiles.length} files, found ${availableFiles}`);
                    console.log('⚠️ UNIFIED BUILD: Files may still be processing, will retry...');
                    throw new Error('Files not yet available in workspace structure');
                  }
                } catch (verificationError: any) {
                  console.log('⚠️ UNIFIED BUILD: File verification failed:', verificationError.message);
                  throw new Error('Upload verification failed: ' + verificationError.message);
                }
              } else {
                console.log('⚠️ UNIFIED BUILD: File upload had issues:', uploadResult.message);
                throw new Error(uploadResult.message || 'Upload failed');
              }
            } catch (uploadError: any) {
              retryCount++;
              const isWorkspaceNotReady = uploadError.message?.includes('Workspace container not ready');

              if (isWorkspaceNotReady && retryCount < maxRetries) {
                console.log(`⏳ UNIFIED BUILD: Workspace not ready, retrying in ${retryDelay}ms (attempt ${retryCount}/${maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
              } else if (retryCount >= maxRetries) {
                console.log('❌ UNIFIED BUILD: Max retries reached, upload failed');
                actualFilesUploaded = 0;
                uploadSuccess = false;
                ecsWorkspaceReady = false;
                uploadedFilesList = [];
                break;
              } else {
                console.log('❌ UNIFIED BUILD: Upload failed with non-retryable error:', uploadError.message);
                actualFilesUploaded = 0;
                uploadSuccess = false;
                ecsWorkspaceReady = false;
                uploadedFilesList = [];
                break;
              }
            }
          }
        }

        console.log('🆔 UNIFIED BUILD: ECS Workspace ID:', ecsWorkspaceResult.workspaceId);
      } else if (!ecsWorkspaceResult) {
        console.log('⚠️ UNIFIED BUILD: ECS workspace creation failed or was not started');
        actualFilesUploaded = 0;
        uploadSuccess = false;
        ecsWorkspaceReady = false;
      } else {
        console.log('⚠️ UNIFIED BUILD: No code blocks found to upload to ECS workspace');
        actualFilesUploaded = 0;
        uploadSuccess = false;
        ecsWorkspaceReady = false;
      }
    } catch (workspaceError) {
      console.error('❌ UNIFIED BUILD: Error with ECS workspace coordination:', workspaceError);
      // Set upload status to failed when there's an error
      actualFilesUploaded = 0;
      uploadSuccess = false;
      ecsWorkspaceReady = false;
      uploadedFilesList = [];
      console.log('❌ UNIFIED BUILD: Upload failed due to workspace error, setting status to failed');
    }

    // Step 6: ECS workspace is already created and coordinated above
    console.log('🌐 UNIFIED BUILD: Step 6 - ECS workspace already created and coordinated');

    // No archive cleanup needed for ECS workspace approach

    // Step 7: Create and save initial chat history to BuildResult (only if no existing chat history)
    console.log('💬 UNIFIED BUILD: Step 7 - Creating and saving initial chat history');

    try {
      // Check if BuildResult already has chat history (don't overwrite existing)
      if (!buildResult.chatHistory || buildResult.chatHistory.length === 0) {
        console.log('💬 UNIFIED BUILD: No existing chat history, creating initial messages');

        // Create initial chat messages from the extracted content
        const initialChatHistory = createInitialChatHistory(buildResult);

        // Save chat history to BuildResult using atomic update
        const updatedBuildResult = await BuildResult.findOneAndUpdate(
          { uuid: buildResult.uuid },
          {
            chatHistory: initialChatHistory,
            status: 'completed' // Ensure status is completed
          },
          { new: true, runValidators: true }
        );

        if (!updatedBuildResult) {
          throw new Error('Failed to update BuildResult with chat history');
        }

        console.log('✅ UNIFIED BUILD: Initial chat history saved with', initialChatHistory.length, 'messages');
        buildResult = updatedBuildResult; // Use the updated result
      } else {
        console.log('✅ UNIFIED BUILD: Existing chat history found, preserving it with', buildResult.chatHistory.length, 'messages');

        // Just ensure status is completed
        const updatedBuildResult = await BuildResult.findOneAndUpdate(
          { uuid: buildResult.uuid },
          { status: 'completed' },
          { new: true, runValidators: true }
        );

        if (updatedBuildResult) {
          buildResult = updatedBuildResult;
        }
      }

    } catch (chatHistoryError) {
      console.error('❌ UNIFIED BUILD: Error handling chat history:', chatHistoryError);
      // Don't fail the entire build, but log the error
    }

    // Final coordination: Ensure all parallel operations are complete
    console.log('🔄 UNIFIED BUILD: Final coordination - ensuring all operations complete');

    // If there's still a pending ECS workspace promise, wait for it
    if (ecsWorkspacePromise) {
      try {
        console.log('⏳ UNIFIED BUILD: Waiting for any remaining ECS workspace operations...');
        await ecsWorkspacePromise;
        console.log('✅ UNIFIED BUILD: All ECS workspace operations completed');
      } catch (finalEcsError) {
        console.error('❌ UNIFIED BUILD: Final ECS workspace operation failed:', finalEcsError);
        // Don't fail the build, but log the error
      }
    }

    console.log('🎉 UNIFIED BUILD: Complete workflow finished successfully!');
    console.log('📊 UNIFIED BUILD: Final status:', {
      actualFilesUploaded,
      uploadSuccess,
      ecsWorkspaceReady,
      uploadedFiles: uploadedFilesList,
      workspaceId: `workspace_${interviewUuid}`
    });

    // Return comprehensive response with chat history included
    return res.json({
      success: true,
      message: 'Unified initial build completed successfully',
      buildResult: {
        uuid: buildResult.uuid,
        description: buildResult.description,
        deploymentInstructions: buildResult.deploymentInstructions,
        additionalSections: buildResult.additionalSections,
        status: buildResult.status,
        codeBlocks: buildResult.codeBlocks,
        projectStructure: buildResult.projectStructure,
        chatHistory: buildResult.chatHistory || [] // Include chat history in response
      },
      workspace: {
        workspaceId: `workspace_${interviewUuid}`,
        workspaceUrl: null, // ECS workspace URLs are dynamic
        status: uploadSuccess ? 'ready' : 'pending',
        filesUploaded: actualFilesUploaded, // Use actual uploaded count
        ecsReady: ecsWorkspaceReady, // Use actual ECS status
        uploadSuccess: uploadSuccess, // Explicit upload success flag
        uploadedFiles: uploadedFilesList // List of actually uploaded files
      },
      metadata: {
        interviewUuid,
        effectivePrompt,
        timestamp: new Date(),
        workflow: 'unified-initial-build'
      }
    });

  } catch (error: any) {
    console.error('❌ UNIFIED BUILD: Error in unified initial build:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to complete unified initial build',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Legacy function - redirects to unified build
export const buildProject = async (req: AuthenticatedRequest, res: Response) => {
  console.log('⚠️ buildProject (legacy buildWithArchive) redirecting to unifiedInitialBuild');
  return unifiedInitialBuild(req, res);
};

// Legacy function - ECS workspaces are created automatically
export const createProjectWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  const { interviewUuid } = req.body;
  console.log('⚠️ createProjectWorkspace (legacy createWorkspaceFromArchive) - ECS workspaces are created automatically');

  return res.json({
    success: true,
    workspace: {
      workspaceId: `workspace_${interviewUuid}`,
      status: 'created'
    },
    message: 'ECS workspace created successfully'
  });
};

// New function to get code structure without chat messages
export const getCodeStructure = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid, prompt, projectData, userData }: GenerateCodeRequest = req.body;

    console.log('🔥 DEBUG: getCodeStructure called with:', {
      interviewUuid,
      prompt,
      hasProjectData: !!projectData,
      hasUserData: !!userData,
      userId: req.user?.userId
    });

    // Get interview data if UUID provided
    let interviewData = null;
    let effectivePrompt = prompt;

    if (interviewUuid) {
      try {
        interviewData = await InterviewConfig.findOne({ uuid: interviewUuid });
        if (interviewData?.user?.prompt) {
          effectivePrompt = interviewData.user.prompt;
        }
      } catch (error) {
        console.error('Error fetching interview data:', error);
      }
    }

    // FIXED: Check if BuildResult already exists for this interview
    if (interviewUuid) {
      console.log('🔍 DEBUG: Checking for existing BuildResult for interview:', interviewUuid);

      // First, try to find ANY BuildResult for this interview (to prevent duplicates)
      const existingBuildResult = await BuildResult.findOne({
        interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true }
      }).sort({ createdAt: -1 });

      console.log('🔍 DEBUG: Found existing BuildResult:', !!existingBuildResult);
      if (existingBuildResult) {
        console.log('🔍 DEBUG: Existing BuildResult status:', existingBuildResult.status);
        console.log('🔍 DEBUG: Existing BuildResult UUID:', existingBuildResult.uuid);
      }

      // If we found ANY BuildResult for this interview, return it (even if processing/failed)
      if (existingBuildResult) {
        console.log('✅ DEBUG: Returning existing BuildResult to prevent duplicates');
        return res.json({
          success: true,
          projectStructure: existingBuildResult.projectStructure,
          buildResult: {
            uuid: existingBuildResult.uuid,
            description: existingBuildResult.description,
            deploymentInstructions: existingBuildResult.deploymentInstructions,
            additionalSections: existingBuildResult.additionalSections,
            status: existingBuildResult.status
          }
        });
      }

      // No existing BuildResult found, create one now
      console.log('🆕 No existing BuildResult found, creating new one for interview:', interviewUuid);

      // Validate that we have the required data
      if (!effectivePrompt) {
        return res.status(400).json({
          success: false,
          message: 'No prompt available from request or interview data'
        });
      }

      let effectiveProjectData = projectData;
      if (interviewData?.projectData) {
        effectiveProjectData = interviewData.projectData;
      }

      if (!effectiveProjectData) {
        return res.status(400).json({
          success: false,
          message: 'No project data available from request or interview data'
        });
      }

      // Skip API configuration check in mock mode
      console.log('🧪 MOCK MODE: Skipping Anthropic API configuration check');

      console.log('🚀 Generating LLM response using Anthropic API for getCodeStructure...');

      // Generate response using Anthropic API
      const { content, metadata } = await AnthropicService.generateResponse(
        effectivePrompt,
        effectiveProjectData,
        userData
      );

      console.log('✅ Anthropic API response generated successfully, processing content...');

      // Create BuildResult using BuildService
      const buildResult = await BuildService.processLLMResponse({
        interviewUuid,
        userId: req.user?.userId,
        rawLLMResponse: content,
        llmMetadata: metadata
      });

      if (buildResult.success && buildResult.buildResult) {
        console.log('✅ Created new BuildResult successfully');
        return res.json({
          success: true,
          projectStructure: buildResult.buildResult.projectStructure,
          buildResult: {
            uuid: buildResult.buildResult.uuid,
            description: buildResult.buildResult.description,
            deploymentInstructions: buildResult.buildResult.deploymentInstructions,
            additionalSections: buildResult.buildResult.additionalSections
          }
        });
      } else {
        console.error('❌ Failed to create BuildResult:', buildResult.error);
        return res.status(500).json({
          success: false,
          message: 'Failed to create build result',
          error: buildResult.error
        });
      }
    }

    // Fallback: Generate the project structure using extracted real code (for cases without interviewUuid)
    const projectStructure = await CodeExtractor.getCodeStructureFromFile('doc/generated_code2');

    // Log the data structure being sent to frontend
    // console.log('=== PROJECT STRUCTURE BEING SENT TO FRONTEND ===');
    // console.log('Project structure type:', typeof projectStructure);
    // console.log('Project structure keys:', Object.keys(projectStructure));
    // console.log('=== END PROJECT STRUCTURE ===');

    // Return the code structure directly
    res.json({
      success: true,
      projectStructure,
      message: 'Code structure retrieved successfully',
      metadata: {
        effectivePrompt,
        interviewUuid,
        timestamp: new Date()
      }
    });

  } catch (error: any) {
    console.error('Error getting code structure:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving code structure',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

export const generateCode = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid, prompt, projectData, userData }: GenerateCodeRequest = req.body;

    // Set up SSE headers for streaming response
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // Function to send SSE data
    const sendEvent = (type: string, data: any) => {
      res.write(`event: ${type}\n`);
      res.write(`data: ${JSON.stringify(data)}\n\n`);
    };

    // Send initial message
    sendEvent('message', {
      type: 'ai',
      content: '🚀 Starting code generation based on your requirements...',
      timestamp: new Date()
    });

    await new Promise(resolve => setTimeout(resolve, 1000));

    // Get interview data if UUID provided
    let interviewData = null;
    let effectivePrompt = prompt;

    if (interviewUuid) {
      try {
        interviewData = await InterviewConfig.findOne({ uuid: interviewUuid });
        if (interviewData?.user?.prompt) {
          effectivePrompt = interviewData.user.prompt;
        }
      } catch (error) {
        console.error('Error fetching interview data:', error);
      }
    }

    sendEvent('message', {
      type: 'ai',
      content: '✅ Code generation complete! Your project is ready. Use the separate /api/build/code endpoint to retrieve the generated files and project structure.',
      timestamp: new Date()
    });

    // Send completion signal without code structure
    sendEvent('generationComplete', {
      success: true,
      message: 'Code generation process completed',
      metadata: {
        effectivePrompt,
        interviewUuid,
        timestamp: new Date()
      }
    });

    // End the stream
    sendEvent('end', { message: 'Generation complete' });
    res.end();

  } catch (error: any) {
    console.error('Error generating code:', error);

    // Send error event
    res.write(`event: error\n`);
    res.write(`data: ${JSON.stringify({
      success: false,
      message: 'Error generating code',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })}\n\n`);

    res.end();
  }
};

// Handle chat messages
export const handleChat = async (req: AuthenticatedRequest, res: Response) => {
  console.log('🔥 Chat endpoint hit with:', { interviewUuid: req.body.interviewUuid, message: req.body.message?.substring(0, 50) });
  try {
    const { interviewUuid, message, conversationHistory }: ChatRequest = req.body;

    if (!message.trim()) {
      return res.status(400).json({
        success: false,
        message: 'Message is required'
      });
    }

    // FIXED: Find the BuildResult for this interview to save chat history
    console.log('🔍 Looking for BuildResult with interviewUuid:', interviewUuid);

    let buildResult = await BuildResult.findOne({
      interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true },
      status: 'completed'
    }).sort({ createdAt: -1 });

    console.log('🔍 Found completed BuildResult:', !!buildResult);

    // If no completed build found, try to find any build for this interview
    if (!buildResult) {
      buildResult = await BuildResult.findOne({
        interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true }
      }).sort({ createdAt: -1 });
      console.log('🔍 Found any BuildResult:', !!buildResult);
      if (buildResult) {
        console.log('🔍 BuildResult status:', buildResult.status);
        console.log('🔍 BuildResult uuid:', buildResult.uuid);
      }
    }

    if (!buildResult) {
      console.log('❌ No BuildResult found for interview:', interviewUuid);
      return res.status(404).json({
        success: false,
        message: 'No build found for this interview. Please complete the initial code generation first.'
      });
    }

    // FIXED: Create user message object
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: message,
      timestamp: new Date()
    };

    // Generate AI response based on user message with conversation context
    let aiResponseData: { fullResponse: string; chatSummary: string };
    try {
      aiResponseData = await generateAIResponse(message, interviewUuid, buildResult);
    } catch (aiError: any) {
      console.error('❌ AI response generation failed:', aiError);

      // Return proper error response to user instead of hiding the error
      return res.status(500).json({
        success: false,
        message: 'Failed to generate AI response',
        error: aiError.message || 'Unable to process your request at this time'
      });
    }

    // FIXED: Create AI message object using the chat summary for display
    const aiMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      type: 'ai',
      content: aiResponseData.chatSummary, // Use summary for chatbox display
      timestamp: new Date()
    };

    // Save conversation context for LLM API continuity (use full response for context)
    try {
      console.log('💾 Saving conversation context for interview:', interviewUuid);
      await ConversationHistoryService.saveMessagePair(
        interviewUuid,
        message,
        aiResponseData.fullResponse // Save full response for LLM context
      );
      console.log('✅ Conversation context saved successfully');
    } catch (contextError) {
      console.error('❌ Error saving conversation context:', contextError);
      // Don't fail the request if context saving fails
    }

    // FIXED: Save both user message and AI response to BuildResult using atomic update
    try {
      const result = await BuildResult.findOneAndUpdate(
        { uuid: buildResult.uuid },
        {
          $push: {
            chatHistory: {
              $each: [userMessage, aiMessage]
            }
          }
        },
        { new: true }
      );

      if (result) {
        console.log('💾 Chat messages saved to BuildResult:', buildResult.uuid);
        console.log('💾 Total messages now:', result.chatHistory.length);
      } else {
        console.error('❌ BuildResult not found for atomic update');
      }
    } catch (saveError) {
      console.error('❌ Error saving chat messages:', saveError);
    }

    // FIXED: Return simple JSON response instead of streaming
    res.json({
      success: true,
      message: aiResponseData.chatSummary, // Return summary for frontend display
      aiMessage: aiMessage,
      userMessage: userMessage,
      fullResponse: aiResponseData.fullResponse // Include full response for debugging/logging
    });

  } catch (error: any) {
    console.error('Error handling chat:', error);

    res.write(`event: error\n`);
    res.write(`data: ${JSON.stringify({
      success: false,
      message: 'Error processing chat message',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })}\n\n`);

    res.end();
  }
};

const generateAIResponse = async (userMessage: string, interviewUuid: string, buildResult?: any): Promise<{
  fullResponse: string;
  chatSummary: string;
}> => {
  console.log('🤖 Generating AI response for message:', userMessage.substring(0, 50));

  try {
    // Get complete conversation context for LLM API
    let conversationMessages;
    try {
      conversationMessages = await ConversationHistoryService.getCompleteConversationContext(
        interviewUuid,
        userMessage
      );
      console.log('📖 Retrieved conversation context:', {
        messageCount: conversationMessages.length,
        hasHistory: conversationMessages.length > 1
      });

      // Show what the Anthropic API payload would look like
      const anthropicPayload = {
        model: 'claude-sonnet-4-20250514',
        max_tokens: 64000,
        messages: conversationMessages
      };

      console.log('🚀 ANTHROPIC API PAYLOAD (CHAT - MOCK MODE):', {
        model: anthropicPayload.model,
        max_tokens: anthropicPayload.max_tokens,
        messageCount: anthropicPayload.messages.length,
        totalContextLength: conversationMessages.reduce((sum, m) => sum + m.content.length, 0)
      });

      console.log('📋 FULL CHAT PAYLOAD CONTENT:');
      console.log(JSON.stringify(anthropicPayload, null, 2));

    } catch (contextError) {
      console.error('❌ Error getting conversation context:', contextError);
      // Fallback to just current message
      conversationMessages = [{ role: 'user' as const, content: userMessage }];
    }

    // Check if we should use mock mode for chat messages
    const bypassApiKey = process.env.BYPASS_API_KEY === 'true';

    if (bypassApiKey) {
      // Mock mode: use doc/llm_reply.json
      console.log('🧪 MOCK MODE: Using doc/llm_reply.json for chat response');
      console.log('⏳ Adding 3 second delay to mimic real LLM response time...');

      // Add 3 second delay for chat submissions in bypass mode
      await new Promise(resolve => setTimeout(resolve, 3000));

      const llmReplyPath = path.join(process.cwd(), '..', 'doc', 'llm_reply.json');
      let mockResponse: any;

      try {
        const llmReplyContent = await fs.promises.readFile(llmReplyPath, 'utf-8');
        mockResponse = JSON.parse(llmReplyContent);
        console.log('📄 LLM reply mock loaded from doc/llm_reply.json');
      } catch (error) {
        console.error('❌ Error reading llm_reply.json file:', error);
        const fallbackMessage = "I understand! Let me help you with that. I'll analyze your request and make the appropriate changes to your codebase.";
        return {
          fullResponse: fallbackMessage,
          chatSummary: fallbackMessage
        };
      }

      // Extract the text content from the mock response
      let responseText = '';
      if (mockResponse.content && Array.isArray(mockResponse.content)) {
        responseText = mockResponse.content
          .filter((item: any) => item.type === 'text')
          .map((item: any) => item.text)
          .join('\n');
      }

      if (!responseText) {
        console.error('❌ No text content found in mock response');
        const fallbackMessage = "I understand! Let me help you with that. I'll analyze your request and make the appropriate changes to your codebase.";
        return {
          fullResponse: fallbackMessage,
          chatSummary: fallbackMessage
        };
      }

      // Extract content using CodeExtractor for potential file uploads (same as before)
      try {
        const extractedContent = CodeExtractor.extractFullContent(responseText);
        console.log('🔍 Extracted content from mock response:', {
          hasDescription: !!extractedContent.description,
          codeBlocksCount: extractedContent.codeBlocks.length,
          hasProjectStructure: !!extractedContent.projectStructure,
          hasDeploymentInstructions: !!extractedContent.deploymentInstructions
        });

        // If we have a build result with workspace info, upload the extracted structure
        if (buildResult && extractedContent.projectStructure) {
          await uploadExtractedStructureToWorkspace(buildResult, extractedContent);
        }
      } catch (extractionError) {
        console.error('❌ Error extracting content from mock response:', extractionError);
        // Continue anyway - don't fail the chat response
      }

      // Create a concise summary for the chatbox
      const chatSummary = createChatSummary(responseText, userMessage);

      console.log('📝 Generated chat summary:', chatSummary);
      console.log('📄 Full response length:', responseText.length, 'characters');

      return {
        fullResponse: responseText,
        chatSummary: chatSummary
      };
    } else {
      // Production mode: make real API call with conversation context
      console.log('🚀 PRODUCTION MODE: Making real Anthropic API call with conversation context');

      try {
        // Make real API call with conversation context
        const anthropicPayload = {
          model: 'claude-sonnet-4-20250514',
          max_tokens: 64000,
          messages: conversationMessages
        };

        const apiKey = process.env.ANTHROPIC_API_KEY;
        if (!apiKey || apiKey === 'your_api_key') {
          throw new Error('ANTHROPIC_API_KEY is not properly configured');
        }

        const response = await fetch('https://api.anthropic.com/v1/messages', {
          method: 'POST',
          headers: {
            'x-api-key': apiKey,
            'anthropic-version': '2023-06-01',
            'content-type': 'application/json'
          },
          body: JSON.stringify(anthropicPayload)
        });

        if (!response.ok) {
          throw new Error(`Anthropic API error: ${response.status} ${response.statusText}`);
        }

        const apiResponse = await response.json();

        // Extract text content from response
        const responseText = apiResponse.content
          .filter((item: any) => item.type === 'text')
          .map((item: any) => item.text)
          .join('\n');

        console.log('✅ Real Anthropic API response received:', {
          id: apiResponse.id,
          model: apiResponse.model,
          inputTokens: apiResponse.usage?.input_tokens,
          outputTokens: apiResponse.usage?.output_tokens
        });

        // Extract content using CodeExtractor for potential file uploads (same as mock mode)
        try {
          const extractedContent = CodeExtractor.extractFullContent(responseText);
          console.log('🔍 Extracted content from real API response:', {
            hasDescription: !!extractedContent.description,
            codeBlocksCount: extractedContent.codeBlocks.length,
            hasProjectStructure: !!extractedContent.projectStructure,
            hasDeploymentInstructions: !!extractedContent.deploymentInstructions
          });

          // If we have a build result with workspace info, upload the extracted structure
          if (buildResult && extractedContent.projectStructure) {
            await uploadExtractedStructureToWorkspace(buildResult, extractedContent);
          }
        } catch (extractionError) {
          console.error('❌ Error extracting content from real API response:', extractionError);
          // Continue anyway - don't fail the chat response
        }

        // Create a concise summary for the chatbox
        const chatSummary = createChatSummary(responseText, userMessage);

        console.log('📝 Generated chat summary (production):', chatSummary);
        console.log('📄 Full response length (production):', responseText.length, 'characters');

        return {
          fullResponse: responseText,
          chatSummary: chatSummary
        };

      } catch (apiError: any) {
        console.error('❌ Real API call failed:', apiError);

        // Provide specific error messages instead of generic responses
        if (apiError.message?.includes('401')) {
          throw new Error('Invalid Anthropic API key. Please check your configuration.');
        } else if (apiError.message?.includes('429')) {
          throw new Error('API rate limit exceeded. Please try again later.');
        } else if (apiError.message?.includes('timeout')) {
          throw new Error('Request timed out. Please try again.');
        } else if (apiError.message?.includes('network') || apiError.message?.includes('ENOTFOUND')) {
          throw new Error('Network error. Please check your internet connection.');
        } else {
          throw new Error(`API error: ${apiError.message || 'Unable to process your request'}`);
        }
      }
    }

  } catch (error: any) {
    console.error('❌ Error in generateAIResponse:', error);

    // Re-throw the error to be handled by the calling function
    // This ensures proper error propagation instead of hiding errors with generic responses
    throw error;
  }
};

// Helper function to upload extracted structure to ECS workspace
const uploadExtractedStructureToWorkspace = async (buildResult: any, extractedContent: any) => {
  try {
    console.log('🚀 Uploading extracted structure to ECS workspace');

    if (!extractedContent.codeBlocks || extractedContent.codeBlocks.length === 0) {
      console.log('📁 No code blocks found in extracted content');
      return;
    }

    // Convert code blocks to files
    const files: { [path: string]: string } = {};
    extractedContent.codeBlocks.forEach((block: any) => {
      if (block.filename && block.content) {
        files[block.filename] = block.content;
      }
    });

    if (Object.keys(files).length === 0) {
      console.log('📁 No files to upload to ECS workspace');
      return;
    }

    console.log('📁 Files to upload to ECS workspace:', Object.keys(files));

    // Get workspace name from buildResult
    const workspaceName = `workspace_${buildResult.interviewUuid}`;

    // Import ECS file system service
    const { ECSFileSystemService } = await import('../services/ecsFileSystemService');
    const { ECSWorkspaceService } = await import('../services/ecsWorkspaceService');

    const ecsWorkspaceService = new ECSWorkspaceService();
    const ecsFileSystemService = new ECSFileSystemService();

    // Get user email from userId
    const { User } = await import('../models/User');
    const user = await User.findById(buildResult.userId);
    const userEmail = user?.email || 'unknown@unknown';

    // Get workspace info by name
    const workspaceInfo = await ecsWorkspaceService.getWorkspaceInfoByName(workspaceName, buildResult.userId, userEmail);

    if (!workspaceInfo) {
      console.log('⚠️ ECS workspace not found, cannot upload files');
      return;
    }

    // Save files to ECS workspace
    const result = await ecsFileSystemService.saveFiles(workspaceInfo.workspaceId, files);

    if (result.success) {
      console.log('✅ Successfully uploaded', Object.keys(files).length, 'files to ECS workspace');
    } else {
      console.error('❌ Failed to upload files to ECS workspace:', result.message);
    }

  } catch (error) {
    console.error('❌ Error uploading extracted structure to ECS workspace:', error);
    // Don't throw error - this is not critical for chat functionality
  }
};

