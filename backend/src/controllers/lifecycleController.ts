import { Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { ContainerLifecycleService } from '../services/containerLifecycleService';
import { workspaceCleanupService } from '../services/workspaceCleanupService';

const lifecycleService = new ContainerLifecycleService();

// Get workspace lifecycle information
export const getWorkspaceLifecycleInfo = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    const lifecycleInfo = await lifecycleService.getWorkspaceLifecycleInfo(workspaceId);

    if (!lifecycleInfo) {
      return res.status(404).json({
        success: false,
        message: 'Workspace not found'
      });
    }

    res.json({
      success: true,
      data: lifecycleInfo
    });

  } catch (error: any) {
    console.error('Error getting workspace lifecycle info:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving workspace lifecycle information',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Extend workspace lifetime
export const extendWorkspaceLifetime = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceId } = req.params;
    const { hours } = req.body; // no default here; service will use config
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    const success = await lifecycleService.extendWorkspaceLifetime(workspaceId, hours);

    if (success) {
      res.json({
        success: true,
        message: `Workspace lifetime extended${hours ? ` by ${hours} hour(s)` : ''}`
      });
    } else {
      res.status(404).json({
        success: false,
        message: 'Workspace not found or could not be extended'
      });
    }

  } catch (error: any) {
    console.error('Error extending workspace lifetime:', error);
    res.status(500).json({
      success: false,
      message: 'Error extending workspace lifetime',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Force cleanup a workspace
export const forceCleanupWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    const success = await lifecycleService.forceCleanupWorkspace(workspaceId);

    if (success) {
      res.json({
        success: true,
        message: 'Workspace cleaned up successfully'
      });
    } else {
      res.status(404).json({
        success: false,
        message: 'Workspace not found or could not be cleaned up'
      });
    }

  } catch (error: any) {
    console.error('Error force cleaning up workspace:', error);
    res.status(500).json({
      success: false,
      message: 'Error cleaning up workspace',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get overall lifecycle statistics
export const getLifecycleStatistics = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    const stats = await lifecycleService.getLifecycleStatistics();

    res.json({
      success: true,
      data: stats
    });

  } catch (error: any) {
    console.error('Error getting lifecycle statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving lifecycle statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get cleanup service status
export const getCleanupServiceStatus = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    const status = workspaceCleanupService.getStatus();

    res.json({
      success: true,
      data: status
    });

  } catch (error: any) {
    console.error('Error getting cleanup service status:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving cleanup service status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Force run cleanup (manual trigger)
export const forceRunCleanup = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    // Run cleanup in background
    workspaceCleanupService.forceCleanup().catch(error => {
      console.error('Error during forced cleanup:', error);
    });

    res.json({
      success: true,
      message: 'Cleanup process started'
    });

  } catch (error: any) {
    console.error('Error starting forced cleanup:', error);
    res.status(500).json({
      success: false,
      message: 'Error starting cleanup process',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Perform lifecycle management (manual trigger)
export const performLifecycleManagement = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    const stats = await lifecycleService.performLifecycleManagement();

    res.json({
      success: true,
      data: stats,
      message: 'Lifecycle management completed'
    });

  } catch (error: any) {
    console.error('Error performing lifecycle management:', error);
    res.status(500).json({
      success: false,
      message: 'Error performing lifecycle management',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
