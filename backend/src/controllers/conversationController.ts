import { Request, Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { ConversationHistoryService, SaveMessageRequest } from '../services/conversationHistoryService';

/**
 * Save a conversation message to context storage
 */
export const saveConversationMessage = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid, messageType, content, inputTokens, outputTokens, totalTokens, model, parentMessageId }: SaveMessageRequest = req.body;

    // Validate required fields
    if (!interviewUuid || !messageType || !content) {
      return res.status(400).json({
        success: false,
        message: 'interviewUuid, messageType, and content are required'
      });
    }

    // Validate messageType
    if (!['user', 'assistant'].includes(messageType)) {
      return res.status(400).json({
        success: false,
        message: 'messageType must be either "user" or "assistant"'
      });
    }

    console.log('💾 Saving conversation message:', {
      interviewUuid,
      messageType,
      contentLength: content.length,
      hasTokens: !!(inputTokens || outputTokens || totalTokens)
    });

    // Save the message
    const savedMessage = await ConversationHistoryService.saveMessage({
      interviewUuid,
      messageType,
      content,
      inputTokens,
      outputTokens,
      totalTokens,
      model,
      parentMessageId
    });

    res.json({
      success: true,
      message: 'Conversation message saved successfully',
      data: {
        messageId: savedMessage.messageId,
        messageOrder: savedMessage.messageOrder,
        timestamp: savedMessage.timestamp
      }
    });

  } catch (error: any) {
    console.error('Error saving conversation message:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save conversation message',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Save a conversation exchange (user message + assistant response)
 */
export const saveConversationExchange = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { 
      interviewUuid, 
      userMessage, 
      assistantMessage, 
      metadata 
    }: {
      interviewUuid: string;
      userMessage: string;
      assistantMessage: string;
      metadata?: {
        inputTokens?: number;
        outputTokens?: number;
        totalTokens?: number;
        model?: string;
      };
    } = req.body;

    // Validate required fields
    if (!interviewUuid || !userMessage || !assistantMessage) {
      return res.status(400).json({
        success: false,
        message: 'interviewUuid, userMessage, and assistantMessage are required'
      });
    }

    console.log('💾 Saving conversation exchange:', {
      interviewUuid,
      userMessageLength: userMessage.length,
      assistantMessageLength: assistantMessage.length,
      hasMetadata: !!metadata
    });

    // Save the exchange
    const result = await ConversationHistoryService.saveMessagePair(
      interviewUuid,
      userMessage,
      assistantMessage,
      metadata
    );

    res.json({
      success: true,
      message: 'Conversation exchange saved successfully',
      data: {
        userMessage: {
          messageId: result.userMessage.messageId,
          messageOrder: result.userMessage.messageOrder,
          timestamp: result.userMessage.timestamp
        },
        assistantMessage: {
          messageId: result.assistantMessage.messageId,
          messageOrder: result.assistantMessage.messageOrder,
          timestamp: result.assistantMessage.timestamp
        }
      }
    });

  } catch (error: any) {
    console.error('Error saving conversation exchange:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save conversation exchange',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get conversation context for an interview
 */
export const getConversationContext = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid } = req.params;
    const { maxMessages } = req.query;

    if (!interviewUuid) {
      return res.status(400).json({
        success: false,
        message: 'interviewUuid is required'
      });
    }

    console.log('📖 Getting conversation context for interview:', interviewUuid);

    // Get conversation context
    const context = await ConversationHistoryService.getConversationContext(
      interviewUuid,
      maxMessages ? parseInt(maxMessages as string) : undefined
    );

    res.json({
      success: true,
      data: context
    });

  } catch (error: any) {
    console.error('Error getting conversation context:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get conversation context',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get complete conversation context for LLM API calls
 */
export const getCompleteConversationContext = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid } = req.params;
    const { currentUserMessage } = req.body;

    if (!interviewUuid || !currentUserMessage) {
      return res.status(400).json({
        success: false,
        message: 'interviewUuid and currentUserMessage are required'
      });
    }

    console.log('📖 Getting complete conversation context for LLM API:', {
      interviewUuid,
      currentMessageLength: currentUserMessage.length
    });

    // Get complete conversation context
    const messages = await ConversationHistoryService.getCompleteConversationContext(
      interviewUuid,
      currentUserMessage
    );

    res.json({
      success: true,
      data: {
        messages,
        messageCount: messages.length
      }
    });

  } catch (error: any) {
    console.error('Error getting complete conversation context:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get complete conversation context',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get conversation statistics
 */
export const getConversationStats = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid } = req.params;

    if (!interviewUuid) {
      return res.status(400).json({
        success: false,
        message: 'interviewUuid is required'
      });
    }

    console.log('📊 Getting conversation stats for interview:', interviewUuid);

    // Get conversation stats
    const stats = await ConversationHistoryService.getConversationStats(interviewUuid);

    res.json({
      success: true,
      data: stats
    });

  } catch (error: any) {
    console.error('Error getting conversation stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get conversation stats',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Clear conversation history for an interview
 */
export const clearConversationHistory = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid } = req.params;

    if (!interviewUuid) {
      return res.status(400).json({
        success: false,
        message: 'interviewUuid is required'
      });
    }

    console.log('🗑️ Clearing conversation history for interview:', interviewUuid);

    // Clear conversation history
    const cleared = await ConversationHistoryService.clearConversation(interviewUuid);

    res.json({
      success: true,
      message: cleared ? 'Conversation history cleared successfully' : 'No conversation history found to clear',
      data: { cleared }
    });

  } catch (error: any) {
    console.error('Error clearing conversation history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear conversation history',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
