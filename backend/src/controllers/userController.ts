import { Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { User } from '../models/User';
import { generateToken } from '../utils/jwt';

export const getMe = async (req: AuthenticatedRequest, res: Response) => {
	try {
		const userId = req.user?.userId;
		if (!userId) {
			return res.status(401).json({ success: false, message: 'Unauthorized' });
		}

		const user = await User.findById(userId).select('_id email username firstName lastName createdAt updatedAt');
		if (!user) {
			return res.status(404).json({ success: false, message: 'User not found' });
		}

		return res.json({
			success: true,
			data: {
				id: user._id,
				email: user.email,
				username: user.username,
				firstName: user.firstName,
				lastName: user.lastName,
				createdAt: user.createdAt,
				updatedAt: user.updatedAt,
			},
		});
	} catch (error: any) {
		console.error('getMe error:', error);
		return res.status(500).json({ success: false, message: 'Server error' });
	}
};

export const updateMe = async (req: AuthenticatedRequest, res: Response) => {
	try {
		const userId = req.user?.userId;
		if (!userId) {
			return res.status(401).json({ success: false, message: 'Unauthorized' });
		}

		const { email, firstName, lastName, username } = req.body as {
			email?: string;
			firstName?: string;
			lastName?: string;
			username?: string;
		};

		const user = await User.findById(userId);
		if (!user) {
			return res.status(404).json({ success: false, message: 'User not found' });
		}

		// If email is changing, ensure it's unique
		if (email && email !== user.email) {
			const existing = await User.findOne({ email });
			if (existing) {
				return res.status(400).json({ success: false, message: 'Email is already in use' });
			}
			user.email = email;
		}

		if (typeof firstName !== 'undefined') user.firstName = firstName;
		if (typeof lastName !== 'undefined') user.lastName = lastName;
		if (typeof username !== 'undefined') user.username = username;

		await user.save();

		// Issue a new token if email changed so the payload stays consistent
		const token = generateToken((user._id as any).toString(), user.email);

		return res.json({
			success: true,
			message: 'Profile updated successfully',
			data: {
				id: user._id,
				email: user.email,
				username: user.username,
				firstName: user.firstName,
				lastName: user.lastName,
			},
			token,
		});
	} catch (error: any) {
		console.error('updateMe error:', error);
		return res.status(500).json({ success: false, message: 'Server error' });
	}
};

export const updatePassword = async (req: AuthenticatedRequest, res: Response) => {
	try {
		const userId = req.user?.userId;
		if (!userId) {
			return res.status(401).json({ success: false, message: 'Unauthorized' });
		}

		const { oldPassword, newPassword } = req.body as { oldPassword?: string; newPassword?: string };
		if (!oldPassword || !newPassword) {
			return res.status(400).json({ success: false, message: 'Both old and new passwords are required' });
		}
		if (newPassword.length < 6) {
			return res.status(400).json({ success: false, message: 'New password must be at least 6 characters long' });
		}

		const user = await User.findById(userId);
		if (!user) {
			return res.status(404).json({ success: false, message: 'User not found' });
		}

		const isMatch = await user.comparePassword(oldPassword);
		if (!isMatch) {
			return res.status(400).json({ success: false, message: 'Current password is incorrect' });
		}

		user.password = newPassword;
		await user.save();

		return res.json({ success: true, message: 'Password updated successfully' });
	} catch (error: any) {
		console.error('updatePassword error:', error);
		return res.status(500).json({ success: false, message: 'Server error' });
	}
}; 