import { Request, Response } from 'express';
import { AnthropicService } from '../services/anthropicService';
import { BuildService } from '../services/buildService';
import { ConversationHistoryService } from '../services/conversationHistoryService';
import fs from 'fs';
import path from 'path';

/**
 * Test controller for debugging Anthropic API integration without real API key
 */

/**
 * Test payload format generation without making actual API calls
 */
export const testPayloadFormat = async (req: Request, res: Response) => {
  try {
    const { prompt, projectData } = req.body;

    // Use sample data if not provided
    const testPrompt = prompt || 'create a website to introduce an AI company';
    const testProjectData = projectData || {
      "technical": {
        "Architecture & System Impact": [
          {
            "Is this a new module, a feature within an existing module, or a refactor?": "new "
          },
          {
            "Will it require changes to backend, frontend, or both?": "both"
          },
          {
            "Does it align with existing system architecture?": "use vite+react+express and make sure it can run after simple npm install. And put backend in folder backend/ and put frontend in folder frontend."
          }
        ],
        "Interfaces, APIs & Integrations": [
          {
            "Should we build or consume REST, GraphQL, gRPC, or something else?": "build rest api for save contact us form and save the page access by user"
          }
        ],
        "Data Requirements": [
          {
            "Are there new tables, fields, or indexes to be created?": "a table about contact us form and a table about user acitivity on the page"
          }
        ],
        "Logging, Monitoring, & Observability": [
          {
            "Are alerts or dashboards required?": "we want to monitor the number of access to each page"
          }
        ]
      },
      "functional": {
        "Understanding the Goal / Intent": [
          {
            "What problem are we trying to solve?": "we want to have marketing landing page to inform customers about company activities"
          },
          {
            "What would success look like?": "a modern looking that attract user attention"
          }
        ],
        "User & Use Case Context": [
          {
            "Who are the end users?": "customers"
          },
          {
            "What are they trying to do when using this feature?": "to learn about company activities"
          }
        ],
        "Functionality & Requirements": [
          {
            "What are the specific features or actions the system should support?": "have home page, services page, about us page, contact us form"
          },
          {
            "What inputs are expected? What outputs are required?": "contact us form: name, email, phone number, reason of request"
          },
          {
            "Should this work across mobile, desktop, API, etc.?": "desktop main, make it compatible with mobile"
          }
        ],
        "Data & Integration": [
          {
            "What data is involved? Where does it come from?": "brief information about AI agents"
          }
        ]
      }
    };

    // Generate the exact payload that would be sent to Anthropic API
    const contextData = {
      objective: testPrompt,
      requirement: testProjectData
    };
    
    const contextString = JSON.stringify(contextData, null, 2);
    const formattedContext = `generate code for context: ${contextString}`;
    
    const requestPayload = {
      model: "claude-sonnet-4-20250514",
      max_tokens: 64000,
      messages: [
        {
          role: "user",
          content: formattedContext
        }
      ]
    };

    // Generate the exact curl command
    const curlCommand = `curl https://api.anthropic.com/v1/messages \\
  --header "x-api-key: <your_api_key>" \\
  --header "anthropic-version: 2023-06-01" \\
  --header "content-type: application/json" \\
  --data '${JSON.stringify(requestPayload)}'`;

    res.json({
      success: true,
      message: "Payload format test completed successfully",
      payload: {
        url: "https://api.anthropic.com/v1/messages",
        method: "POST",
        headers: {
          "x-api-key": "<your_api_key>",
          "anthropic-version": "2023-06-01",
          "content-type": "application/json"
        },
        body: requestPayload
      },
      analysis: {
        model: requestPayload.model,
        maxTokens: requestPayload.max_tokens,
        messageRole: requestPayload.messages[0].role,
        contentLength: formattedContext.length,
        startsWithPrefix: formattedContext.startsWith("generate code for context: "),
        hasObjectiveField: contextData.objective !== undefined,
        hasRequirementField: contextData.requirement !== undefined,
        contextPreview: formattedContext.substring(0, 200) + "..."
      },
      curlCommand: curlCommand,
      verification: {
        matchesYourExample: true,
        correctModel: requestPayload.model === "claude-sonnet-4-20250514",
        correctMaxTokens: requestPayload.max_tokens === 64000,
        correctPrefix: formattedContext.startsWith("generate code for context: "),
        correctStructure: contextData.objective && contextData.requirement
      }
    });

  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Error testing payload format',
      error: error.message
    });
  }
};

/**
 * Test response processing with mock Anthropic API response
 */
export const testResponseProcessing = async (req: Request, res: Response) => {
  try {
    // Mock Anthropic API response (exactly as you provided in your example)
    const mockAnthropicResponse = {
      "content": [
        {
          "text": "I'll create a modern, responsive AI agent company website with the requested features. Here's the complete implementation:\n\n## Project Structure\n\n```\nai-agent-website/\n├── frontend/\n│   ├── public/\n│   ├── src/\n│   │   ├── components/\n│   │   ├── pages/\n│   │   ├── styles/\n│   │   └── App.js\n│   └── package.json\n├── backend/\n│   ├── src/\n│   │   ├── controllers/\n│   │   ├── models/\n│   │   ├── routes/\n│   │   └── index.js\n│   └── package.json\n└── docker-compose.yml\n```\n\n## Frontend Implementation\n\n### package.json\n```json\n{\n  \"name\": \"ai-agent-website\",\n  \"version\": \"1.0.0\",\n  \"scripts\": {\n    \"start\": \"react-scripts start\",\n    \"build\": \"react-scripts build\"\n  },\n  \"dependencies\": {\n    \"react\": \"^18.2.0\",\n    \"react-dom\": \"^18.2.0\",\n    \"react-router-dom\": \"^6.8.0\",\n    \"axios\": \"^1.3.0\"\n  }\n}\n```\n\nThis website will be fully responsive and include all the requested features.",
          "type": "text"
        }
      ],
      "id": "msg_013Zva2CMHLNnXjNJJKqJ2EF",
      "model": "claude-sonnet-4-20250514",
      "role": "assistant",
      "stop_reason": "end_turn",
      "stop_sequence": null,
      "type": "message",
      "usage": {
        "input_tokens": 2095,
        "output_tokens": 503
      }
    };

    // Process the response exactly as our service does
    const extractedContent = mockAnthropicResponse.content
      .filter(item => item.type === 'text')
      .map(item => item.text)
      .join('\n');

    const metadata = {
      prompt: "generate code for context: {...}",
      model: mockAnthropicResponse.model,
      tokens: mockAnthropicResponse.usage.input_tokens + mockAnthropicResponse.usage.output_tokens,
      inputTokens: mockAnthropicResponse.usage.input_tokens,
      outputTokens: mockAnthropicResponse.usage.output_tokens,
      timestamp: new Date()
    };

    const processedResult = {
      content: extractedContent,
      metadata: metadata
    };

    res.json({
      success: true,
      message: "Response processing test completed successfully",
      rawApiResponse: mockAnthropicResponse,
      processing: {
        totalContentItems: mockAnthropicResponse.content.length,
        textItemsFound: mockAnthropicResponse.content.filter(item => item.type === 'text').length,
        extractedContentLength: extractedContent.length,
        contentPreview: extractedContent.substring(0, 300) + "..."
      },
      processedResult: processedResult,
      verification: {
        contentExtracted: extractedContent.length > 0,
        correctModel: metadata.model === "claude-sonnet-4-20250514",
        tokensCalculated: metadata.tokens === 2598,
        inputTokensPreserved: metadata.inputTokens === 2095,
        outputTokensPreserved: metadata.outputTokens === 503,
        timestampGenerated: metadata.timestamp !== undefined
      }
    });

  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Error testing response processing',
      error: error.message
    });
  }
};

/**
 * Test the complete end-to-end flow without real API key
 */
export const testEndToEndFlow = async (req: Request, res: Response) => {
  try {
    const { interviewUuid, prompt, projectData } = req.body;

    // Use sample data if not provided
    const testPrompt = prompt || 'create a website to introduce an AI company';
    const testProjectData = projectData || {
      "technical": {
        "Architecture & System Impact": [
          {"Is this a new module, a feature within an existing module, or a refactor?": "new"}
        ]
      },
      "functional": {
        "Understanding the Goal / Intent": [
          {"What problem are we trying to solve?": "create a marketing website"}
        ]
      }
    };

    console.log('🧪 TESTING END-TO-END FLOW WITHOUT REAL API KEY');
    console.log('================================================');

    // Step 1: Show payload format
    const contextData = { objective: testPrompt, requirement: testProjectData };
    const contextString = JSON.stringify(contextData, null, 2);
    const formattedContext = `generate code for context: ${contextString}`;

    const requestPayload = {
      model: "claude-sonnet-4-20250514",
      max_tokens: 64000,
      messages: [{ role: "user", content: formattedContext }]
    };

    console.log('📋 STEP 1: PAYLOAD GENERATION');
    console.log('Model:', requestPayload.model);
    console.log('Max Tokens:', requestPayload.max_tokens);
    console.log('Content Length:', formattedContext.length);
    console.log('Starts with prefix:', formattedContext.startsWith("generate code for context: "));

    // Step 2: Simulate API call (will use mock mode if BYPASS_API_KEY=true)
    console.log('\n🚀 STEP 2: API CALL SIMULATION');
    const result = await AnthropicService.generateResponse(
      testPrompt,
      testProjectData,
      undefined
    );

    console.log('✅ Response received from:', result.metadata.model);
    console.log('Content length:', result.content.length);
    console.log('Tokens:', result.metadata.tokens);

    // Step 3: Process through BuildService
    console.log('\n🔄 STEP 3: PROCESSING THROUGH BUILD SERVICE');
    const testInterviewUuid = interviewUuid || 'test-uuid-' + Date.now();
    const buildResult = await BuildService.processLLMResponse({
      interviewUuid: testInterviewUuid,
      userId: 'test-user',
      rawLLMResponse: result.content,
      llmMetadata: result.metadata
    });

    console.log('✅ BuildService processing:', buildResult.success ? 'SUCCESS' : 'FAILED');
    if (buildResult.buildResult) {
      console.log('Code blocks extracted:', buildResult.buildResult.codeBlocks.length);
      console.log('Has project structure:', !!buildResult.buildResult.projectStructure);
      console.log('Has description:', !!buildResult.buildResult.description);
    }

    // Step 4: Save to conversation history (like the real endpoints do)
    if (buildResult.success && buildResult.buildResult) {
      try {
        console.log('\n💾 STEP 4: SAVING TO CONVERSATION HISTORY');

        // Format the project data and prompt as the initial user message
        const initialUserMessage = `Project Requirements:\n\nPrompt: ${testPrompt}\n\nProject Data:\n${JSON.stringify(testProjectData, null, 2)}`;

        await ConversationHistoryService.saveMessagePair(
          testInterviewUuid,
          initialUserMessage,
          result.content,
          {
            inputTokens: result.metadata.inputTokens,
            outputTokens: result.metadata.outputTokens,
            totalTokens: result.metadata.tokens,
            model: result.metadata.model
          }
        );

        console.log('✅ STEP 4: Conversation history saved successfully');
      } catch (conversationError) {
        console.error('❌ STEP 4: Error saving to conversation history:', conversationError);
      }
    }

    res.json({
      success: true,
      message: "End-to-end flow test completed successfully",
      steps: {
        step1_payload: {
          description: "Payload format generation",
          payload: requestPayload,
          verification: {
            correctModel: requestPayload.model === "claude-sonnet-4-20250514",
            correctMaxTokens: requestPayload.max_tokens === 64000,
            hasPrefix: formattedContext.startsWith("generate code for context: "),
            correctStructure: contextData.objective && contextData.requirement
          }
        },
        step2_apiCall: {
          description: "API call simulation with fallback",
          result: {
            contentLength: result.content.length,
            model: result.metadata.model,
            tokens: result.metadata.tokens,
            inputTokens: result.metadata.inputTokens,
            outputTokens: result.metadata.outputTokens
          },
          verification: {
            contentReceived: result.content.length > 0,
            metadataComplete: !!result.metadata.model && !!result.metadata.timestamp
          }
        },
        step3_processing: {
          description: "BuildService processing",
          result: buildResult.success ? {
            uuid: buildResult.buildResult?.uuid,
            codeBlocksCount: buildResult.buildResult?.codeBlocks.length,
            hasProjectStructure: !!buildResult.buildResult?.projectStructure,
            hasDescription: !!buildResult.buildResult?.description,
            status: buildResult.buildResult?.status
          } : { error: buildResult.error },
          verification: {
            processingSuccessful: buildResult.success,
            dataExtracted: (buildResult.buildResult?.codeBlocks?.length || 0) > 0
          }
        }
      },
      summary: {
        payloadFormatCorrect: true,
        apiCallWorks: true,
        fallbackWorks: true,
        responseProcessingWorks: buildResult.success,
        endToEndComplete: buildResult.success,
        readyForRealApiKey: buildResult.success
      }
    });

  } catch (error: any) {
    console.error('❌ End-to-end test failed:', error);
    res.status(500).json({
      success: false,
      message: 'Error in end-to-end flow test',
      error: error.message
    });
  }
};

/**
 * Test conversation context with mock chat message
 */
export const testInitialBuildMessages = async (req: Request, res: Response) => {
  try {
    console.log('\n🧪 TESTING INITIAL BUILD MESSAGE PERSISTENCE');

    const { BuildResult } = require('../models/BuildResult');

    // Create a test interview UUID for new build
    const testUuid = `test-initial-build-${Date.now()}`;
    console.log('Test UUID:', testUuid);

    // Step 1: Verify no existing build
    const existingBuild = await BuildResult.findOne({
      interviewUuid: { $eq: testUuid, $ne: null, $exists: true }
    });

    if (existingBuild) {
      return res.json({
        success: false,
        message: 'Test UUID already exists, please try again'
      });
    }

    console.log('✅ Confirmed no existing build for test UUID');

    // Step 2: Simulate initial build process
    console.log('🔄 Simulating initial build process...');

    // This would normally be done by the unified build API
    const mockBuildResult = {
      uuid: `build-${Date.now()}`,
      interviewUuid: testUuid,
      description: 'Test AI company website',
      projectStructure: { 'package.json': { type: 'file', content: '{}' } },
      status: 'completed',
      llmMetadata: {
        model: 'claude-3-sonnet-20240229',
        promptTokens: 1000,
        completionTokens: 2000,
        totalTokens: 3000,
        requestId: 'test-request-id',
        timestamp: new Date(),
        prompt: 'Create an AI company website'
      },
      rawLLMResponse: 'Test LLM response for initial build',
      chatHistory: [
        {
          id: 'initial-1',
          type: 'ai',
          content: 'Welcome! I\'m creating your AI company website...',
          timestamp: new Date()
        },
        {
          id: 'initial-2',
          type: 'ai',
          content: 'Setting up the project structure...',
          timestamp: new Date()
        },
        {
          id: 'initial-3',
          type: 'ai',
          content: '✅ Request completed successfully! Your files have been updated.',
          timestamp: new Date()
        }
      ],
      createdAt: new Date()
    };

    const savedBuild = await BuildResult.create(mockBuildResult);
    console.log('💾 Created mock BuildResult with initial messages');

    // Step 3: Test the checkBuildExists API
    console.log('🧪 Testing checkBuildExists API...');

    const checkResponse = await fetch(`http://localhost:3001/api/llm/build-exists/${testUuid}`);
    const checkData = await checkResponse.json() as any;

    console.log('🔍 checkBuildExists response:', {
      success: checkData.success,
      exists: checkData.exists,
      chatHistoryLength: checkData.buildResult?.chatHistory?.length || 0
    });

    res.json({
      success: true,
      message: 'Initial build message persistence test completed',
      results: {
        testUuid: testUuid,
        buildResultUuid: savedBuild.uuid,
        initialMessagesCount: mockBuildResult.chatHistory.length,
        checkBuildExistsResponse: {
          success: checkData.success,
          exists: checkData.exists,
          chatHistoryLength: checkData.buildResult?.chatHistory?.length || 0,
          messagesPreserved: checkData.buildResult?.chatHistory?.length === mockBuildResult.chatHistory.length
        },
        testUrl: `http://localhost:5174/build/${testUuid}`
      }
    });

  } catch (error: any) {
    console.error('❌ Error in initial build message test:', error);
    res.status(500).json({
      success: false,
      message: 'Initial build message test failed',
      error: error.message
    });
  }
};

export const testCompleteRefreshFlow = async (req: Request, res: Response) => {
  try {
    const { interviewUuid } = req.body;

    console.log('\n🔄 TESTING COMPLETE REFRESH FLOW');
    console.log('Interview UUID:', interviewUuid);

    const { BuildResult } = require('../models/BuildResult');

    // Step 1: Find existing BuildResult
    const existingBuild = await BuildResult.findOne({
      interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true }
    }).sort({ createdAt: -1 });

    if (!existingBuild) {
      return res.json({
        success: false,
        message: 'No existing BuildResult found for testing'
      });
    }

    console.log('✅ Found existing BuildResult:', {
      uuid: existingBuild.uuid,
      chatHistoryLength: existingBuild.chatHistory?.length || 0,
      createdAt: existingBuild.createdAt
    });

    // Step 2: Add comprehensive test chat messages
    const testMessages = [
      {
        id: `msg_${Date.now()}_user_1`,
        type: 'user',
        content: 'Can you change the header color to blue?',
        timestamp: new Date()
      },
      {
        id: `msg_${Date.now()}_ai_1`,
        type: 'assistant',
        content: 'I\'ll help you change the header color to blue. Let me update the CSS for you.',
        timestamp: new Date()
      },
      {
        id: `msg_${Date.now()}_user_2`,
        type: 'user',
        content: 'Also add a footer with contact information',
        timestamp: new Date()
      },
      {
        id: `msg_${Date.now()}_ai_2`,
        type: 'assistant',
        content: 'Perfect! I\'ve added a footer with contact information including email and phone number.',
        timestamp: new Date()
      }
    ];

    // Add messages to chat history
    const updatedBuild = await BuildResult.findOneAndUpdate(
      { uuid: existingBuild.uuid },
      {
        $push: {
          chatHistory: {
            $each: testMessages
          }
        }
      },
      { new: true }
    );

    console.log('💬 Added test messages, new chat history length:', updatedBuild.chatHistory.length);

    // Step 3: Test the checkBuildExists API (simulates frontend refresh)
    console.log('🧪 Testing checkBuildExists API...');

    const checkResponse = await fetch(`http://localhost:3001/api/llm/build-exists/${interviewUuid}`);
    const checkData = await checkResponse.json() as any;

    console.log('🔍 checkBuildExists response:', {
      success: checkData.success,
      exists: checkData.exists,
      chatHistoryLength: checkData.buildResult?.chatHistory?.length || 0
    });

    res.json({
      success: true,
      message: 'Complete refresh flow test completed',
      results: {
        initialChatLength: existingBuild.chatHistory?.length || 0,
        afterAddingMessages: updatedBuild.chatHistory.length,
        checkBuildExistsResponse: {
          success: checkData.success,
          exists: checkData.exists,
          chatHistoryLength: checkData.buildResult?.chatHistory?.length || 0,
          chatHistoryPreserved: checkData.buildResult?.chatHistory?.length === updatedBuild.chatHistory.length
        },
        testMessages: testMessages.map(msg => ({
          type: msg.type,
          content: msg.content.substring(0, 50) + '...'
        }))
      }
    });

  } catch (error: any) {
    console.error('❌ Error in complete refresh flow test:', error);
    res.status(500).json({
      success: false,
      message: 'Complete refresh flow test failed',
      error: error.message
    });
  }
};

export const testChatHistoryPersistence = async (req: Request, res: Response) => {
  try {
    const { interviewUuid } = req.body;

    console.log('\n💬 TESTING CHAT HISTORY PERSISTENCE');
    console.log('Interview UUID:', interviewUuid);

    const { BuildResult } = require('../models/BuildResult');

    // Step 1: Find existing BuildResult
    const existingBuild = await BuildResult.findOne({
      interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true }
    }).sort({ createdAt: -1 });

    if (!existingBuild) {
      return res.json({
        success: false,
        message: 'No existing BuildResult found for testing'
      });
    }

    console.log('✅ Found existing BuildResult:', {
      uuid: existingBuild.uuid,
      chatHistoryLength: existingBuild.chatHistory?.length || 0,
      createdAt: existingBuild.createdAt
    });

    // Step 2: Add some test chat messages
    const testUserMessage = {
      id: `msg_${Date.now()}_user`,
      type: 'user',
      content: 'Test user message for persistence',
      timestamp: new Date()
    };

    const testAiMessage = {
      id: `msg_${Date.now()}_ai`,
      type: 'assistant',
      content: 'Test AI response for persistence',
      timestamp: new Date()
    };

    // Add messages to chat history
    const updatedBuild = await BuildResult.findOneAndUpdate(
      { uuid: existingBuild.uuid },
      {
        $push: {
          chatHistory: {
            $each: [testUserMessage, testAiMessage]
          }
        }
      },
      { new: true }
    );

    console.log('💬 Added test messages, new chat history length:', updatedBuild.chatHistory.length);

    // Step 3: Test unified build call (should preserve chat history)
    console.log('🧪 Testing unified build call...');

    const mockReq = {
      ...req,
      user: { userId: 'test-user-123' }
    } as any;

    const { unifiedInitialBuild } = require('./buildController');

    let responseData: any = null;
    const mockRes = {
      json: (data: any) => {
        responseData = data;
        return mockRes;
      },
      status: (code: number) => {
        responseData = { ...responseData, statusCode: code };
        return mockRes;
      }
    } as any;

    await unifiedInitialBuild(mockReq, mockRes);

    // Step 4: Check if chat history was preserved
    const afterUnifiedBuild = await BuildResult.findOne({
      interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true }
    }).sort({ createdAt: -1 });

    const chatHistoryPreserved = {
      beforeLength: updatedBuild.chatHistory.length,
      afterLength: afterUnifiedBuild.chatHistory.length,
      preserved: updatedBuild.chatHistory.length === afterUnifiedBuild.chatHistory.length,
      sameUuid: updatedBuild.uuid === afterUnifiedBuild.uuid
    };

    console.log('🔍 Chat history preservation check:', chatHistoryPreserved);

    res.json({
      success: true,
      message: 'Chat history persistence test completed',
      results: {
        initialChatLength: existingBuild.chatHistory?.length || 0,
        afterAddingMessages: updatedBuild.chatHistory.length,
        afterUnifiedBuild: afterUnifiedBuild.chatHistory.length,
        chatHistoryPreserved,
        unifiedBuildResponse: responseData
      }
    });

  } catch (error: any) {
    console.error('❌ Error in chat history persistence test:', error);
    res.status(500).json({
      success: false,
      message: 'Chat history persistence test failed',
      error: error.message
    });
  }
};

export const testRefreshScenario = async (req: Request, res: Response) => {
  try {
    const { interviewUuid } = req.body;

    console.log('\n🔄 TESTING REFRESH SCENARIO');
    console.log('Interview UUID:', interviewUuid);

    // Step 1: Check if BuildResult exists
    const { BuildResult } = require('../models/BuildResult');
    const existingBuild = await BuildResult.findOne({
      interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true }
    }).sort({ createdAt: -1 });

    if (existingBuild) {
      console.log('✅ Found existing BuildResult:', {
        uuid: existingBuild.uuid,
        hasDescription: !!existingBuild.description,
        codeBlocksCount: existingBuild.codeBlocks?.length || 0,
        hasChatHistory: !!existingBuild.chatHistory && existingBuild.chatHistory.length > 0,
        chatHistoryLength: existingBuild.chatHistory?.length || 0,
        createdAt: existingBuild.createdAt
      });

      // Step 2: Test unified build call (should NOT overwrite)
      console.log('🧪 Testing unified build call on existing data...');

      const mockReq = {
        ...req,
        user: { userId: 'test-user-123' }
      } as any;

      const { unifiedInitialBuild } = require('./buildController');

      let responseData: any = null;
      const mockRes = {
        json: (data: any) => {
          responseData = data;
          return mockRes;
        },
        status: (code: number) => {
          responseData = { ...responseData, statusCode: code };
          return mockRes;
        }
      } as any;

      await unifiedInitialBuild(mockReq, mockRes);

      // Step 3: Check if data was preserved
      const afterBuild = await BuildResult.findOne({
        interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true }
      }).sort({ createdAt: -1 });

      const dataPreserved = {
        sameUuid: existingBuild.uuid === afterBuild.uuid,
        sameChatHistoryLength: existingBuild.chatHistory?.length === afterBuild.chatHistory?.length,
        sameCreatedAt: existingBuild.createdAt.getTime() === afterBuild.createdAt.getTime()
      };

      console.log('🔍 Data preservation check:', dataPreserved);

      res.json({
        success: true,
        message: 'Refresh scenario test completed',
        results: {
          existingBuildFound: true,
          existingBuild: {
            uuid: existingBuild.uuid,
            chatHistoryLength: existingBuild.chatHistory?.length || 0,
            createdAt: existingBuild.createdAt
          },
          afterUnifiedBuild: {
            uuid: afterBuild.uuid,
            chatHistoryLength: afterBuild.chatHistory?.length || 0,
            createdAt: afterBuild.createdAt
          },
          dataPreserved,
          unifiedBuildResponse: responseData
        }
      });
    } else {
      res.json({
        success: false,
        message: 'No existing BuildResult found for testing',
        interviewUuid
      });
    }

  } catch (error: any) {
    console.error('❌ Error in refresh scenario test:', error);
    res.status(500).json({
      success: false,
      message: 'Refresh scenario test failed',
      error: error.message
    });
  }
};

export const testUnifiedBuild = async (req: Request, res: Response) => {
  try {
    const { interviewUuid, prompt, projectData } = req.body;

    console.log('\n🚀 TESTING UNIFIED BUILD');
    console.log('Interview UUID:', interviewUuid);
    console.log('Prompt:', prompt);
    console.log('Project Data:', projectData);

    // Create a mock authenticated request
    const mockReq = {
      ...req,
      user: { userId: 'test-user-123' }
    } as any;

    // Import the unified build function
    const { unifiedInitialBuild } = require('./buildController');

    // Call the unified build method
    let responseData: any = null;
    const mockRes = {
      json: (data: any) => {
        responseData = data;
        return mockRes;
      },
      status: (code: number) => {
        responseData = { ...responseData, statusCode: code };
        return mockRes;
      }
    } as any;

    await unifiedInitialBuild(mockReq, mockRes);

    console.log('✅ UNIFIED BUILD TEST COMPLETED');

    res.json({
      success: true,
      message: 'Unified build test completed successfully',
      result: responseData
    });

  } catch (error: any) {
    console.error('❌ Error in unified build test:', error);
    res.status(500).json({
      success: false,
      message: 'Unified build test failed',
      error: error.message
    });
  }
};

export const testMockChatMessage = async (req: Request, res: Response) => {
  try {
    const { interviewUuid, message } = req.body;

    if (!interviewUuid || !message) {
      return res.status(400).json({
        success: false,
        message: 'interviewUuid and message are required'
      });
    }

    console.log('🧪 Testing mock chat message for interview:', interviewUuid);

    // Get conversation context (previous messages)
    const conversationContext = await ConversationHistoryService.getCompleteConversationContext(
      interviewUuid,
      message
    );

    console.log('📖 Conversation context retrieved:', {
      messageCount: conversationContext.length,
      messages: conversationContext.map(m => ({ role: m.role, contentLength: m.content.length }))
    });

    // Show what the Anthropic API payload would look like for chat
    const chatAnthropicPayload = {
      model: 'claude-sonnet-4-20250514',
      max_tokens: 64000,
      messages: conversationContext
    };

    console.log('🚀 ANTHROPIC API PAYLOAD (CHAT - MOCK MODE):', {
      model: chatAnthropicPayload.model,
      max_tokens: chatAnthropicPayload.max_tokens,
      messageCount: chatAnthropicPayload.messages.length,
      totalContextLength: conversationContext.reduce((sum, m) => sum + m.content.length, 0)
    });

    console.log('📋 FULL CHAT PAYLOAD CONTENT:');
    console.log(JSON.stringify(chatAnthropicPayload, null, 2));

    // Read mock response from doc/llm_reply.json
    const llmReplyPath = path.join(process.cwd(), '..', 'doc', 'llm_reply.json');
    const llmReplyContent = await fs.promises.readFile(llmReplyPath, 'utf-8');
    const mockResponse = JSON.parse(llmReplyContent);

    // Extract text content
    const responseText = mockResponse.content
      .filter((item: any) => item.type === 'text')
      .map((item: any) => item.text)
      .join('\n');

    // Save the conversation exchange
    const savedMessages = await ConversationHistoryService.saveMessagePair(
      interviewUuid,
      message,
      responseText,
      {
        inputTokens: mockResponse.usage?.input_tokens,
        outputTokens: mockResponse.usage?.output_tokens,
        totalTokens: (mockResponse.usage?.input_tokens || 0) + (mockResponse.usage?.output_tokens || 0),
        model: mockResponse.model
      }
    );

    console.log('💾 Conversation exchange saved:', {
      userMessageId: savedMessages.userMessage.messageId,
      assistantMessageId: savedMessages.assistantMessage.messageId
    });

    // Get updated conversation stats
    const stats = await ConversationHistoryService.getConversationStats(interviewUuid);

    res.json({
      success: true,
      message: 'Mock chat message processed successfully',
      data: {
        conversationContext: {
          previousMessageCount: conversationContext.length - 1, // Exclude current message
          totalContextLength: conversationContext.reduce((sum, m) => sum + m.content.length, 0)
        },
        mockResponse: {
          model: mockResponse.model,
          responseLength: responseText.length,
          usage: mockResponse.usage
        },
        savedMessages: {
          userMessage: {
            messageId: savedMessages.userMessage.messageId,
            messageOrder: savedMessages.userMessage.messageOrder
          },
          assistantMessage: {
            messageId: savedMessages.assistantMessage.messageId,
            messageOrder: savedMessages.assistantMessage.messageOrder
          }
        },
        conversationStats: stats
      }
    });

  } catch (error: any) {
    console.error('❌ Mock chat message test failed:', error);
    res.status(500).json({
      success: false,
      message: 'Error in mock chat message test',
      error: error.message
    });
  }
};
