{"family": "mergen-workspace-task", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "executionRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskRole", "containerDefinitions": [{"name": "workspace-container", "image": "mergen/workspace:latest", "essential": true, "portMappings": [{"containerPort": 3000, "protocol": "tcp"}, {"containerPort": 8080, "protocol": "tcp"}], "environment": [{"name": "NODE_ENV", "value": "production"}, {"name": "WORKSPACE_ID", "value": "PLACEHOLDER_WORKSPACE_ID"}, {"name": "USER_ID", "value": "PLACEHOLDER_USER_ID"}, {"name": "INTERVIEW_UUID", "value": "PLACEHOLDER_INTERVIEW_UUID"}], "mountPoints": [{"sourceVolume": "workspace-storage", "containerPath": "/workspace", "readOnly": false}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/mergen-workspace", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}, "stopTimeout": 30}], "volumes": [{"name": "workspace-storage", "efsVolumeConfiguration": {"fileSystemId": "PLACEHOLDER_EFS_ID", "rootDirectory": "/workspaces", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "PLACEHOLDER_ACCESS_POINT_ID", "iam": "ENABLED"}}}], "placementConstraints": [], "tags": [{"key": "Project", "value": "<PERSON><PERSON>"}, {"key": "Component", "value": "Workspace"}]}