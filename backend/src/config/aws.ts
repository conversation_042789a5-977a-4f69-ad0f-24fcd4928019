import * as dotenv from 'dotenv';
import {
  RegisterTaskDefinitionCommandInput,
  CreateServiceCommandInput,
  ContainerDefinition,
  Volume,
  PortMapping,
  LogConfiguration,
  HealthCheck,
  MountPoint,
  KeyValuePair
} from '@aws-sdk/client-ecs';

dotenv.config();

export interface AWSConfig {
  region: string;
  accessKeyId: string;
  secretAccessKey: string;
  
  // ECS Configuration
  ecsClusterName: string;
  ecsTaskDefinitionFamily: string;
  ecsExecutionRoleArn: string;
  ecsTaskRoleArn: string;
  
  // Networking
  vpcId: string;
  subnetIds: string[];
  securityGroupId: string;
  
  // EFS Configuration
  efsFileSystemId: string;
  efsAccessPointId: string;
  
  // Container Configuration
  containerImage: string;
  containerCpu: number;
  containerMemory: number;
  containerPort: number;
  previewPort: number;
  
  // Lifecycle
  defaultLifetimeHours: number;
  cleanupIntervalMinutes: number;
  syncIntervalMinutes: number;
  extendLifetimeHours: number;

  // Optional Route53 preview DNS
  route53HostedZoneId?: string;
  previewBaseDomain?: string;
  
  // Optional Route53 live DNS
  liveBaseDomain?: string;
}

export const getAWSConfig = (): AWSConfig => {
  const requiredEnvVars = [
    'AWS_REGION',
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'ECS_CLUSTER_NAME',
    'ECS_EXECUTION_ROLE_ARN',
    'ECS_TASK_ROLE_ARN',
    'VPC_ID',
    'SUBNET_IDS',
    'SECURITY_GROUP_ID'
    // Temporarily removed EFS requirements
    // 'EFS_FILE_SYSTEM_ID',
    // 'EFS_ACCESS_POINT_ID'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  return {
    region: process.env.AWS_REGION!,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
    
    // ECS Configuration
    ecsClusterName: process.env.ECS_CLUSTER_NAME!,
    ecsTaskDefinitionFamily: process.env.ECS_TASK_DEFINITION_FAMILY || 'mergen-workspace-task',
    ecsExecutionRoleArn: process.env.ECS_EXECUTION_ROLE_ARN!,
    ecsTaskRoleArn: process.env.ECS_TASK_ROLE_ARN!,
    
    // Networking
    vpcId: process.env.VPC_ID!,
    subnetIds: process.env.SUBNET_IDS!.split(',').map(id => id.trim()),
    securityGroupId: process.env.SECURITY_GROUP_ID!,
    
    // EFS Configuration (optional for now)
    efsFileSystemId: process.env.EFS_FILE_SYSTEM_ID || '',
    efsAccessPointId: process.env.EFS_ACCESS_POINT_ID || '',
    
    // Container Configuration
    containerImage: process.env.CONTAINER_IMAGE || 'mergen/workspace:latest',
    containerCpu: parseInt(process.env.CONTAINER_CPU || '512'),
    containerMemory: parseInt(process.env.CONTAINER_MEMORY || '1024'),
    containerPort: parseInt(process.env.CONTAINER_PORT || '3000'),
    previewPort: parseInt(process.env.PREVIEW_PORT || '8080'),
    
    // Lifecycle
    defaultLifetimeHours: parseInt(process.env.DEFAULT_LIFETIME_HOURS || '1'),
    cleanupIntervalMinutes: parseInt(process.env.CLEANUP_INTERVAL_MINUTES || '15'),
    syncIntervalMinutes: parseInt(process.env.SYNC_INTERVAL_MINUTES || '15'),
    extendLifetimeHours: parseInt(process.env.EXTEND_LIFETIME_HOURS || '1'),

    // Optional Route53 preview DNS
    route53HostedZoneId: process.env.ROUTE53_HOSTED_ZONE_ID || undefined,
    previewBaseDomain: process.env.PREVIEW_BASE_DOMAIN || undefined,

    // Optional Route53 live DNS
    liveBaseDomain: process.env.LIVE_BASE_DOMAIN || process.env.PREVIEW_BASE_DOMAIN || undefined,
  };
};

// Validate AWS configuration on module load
export const validateAWSConfig = (): boolean => {
  try {
    getAWSConfig();
    return true;
  } catch (error) {
    console.error('AWS Configuration validation failed:', error);
    return false;
  }
};

// Utility function to get default lifetime in milliseconds
export const getDefaultLifetimeMs = (): number => {
  const config = getAWSConfig();
  return config.defaultLifetimeHours * 60 * 60 * 1000;
};

// Default ECS task definition template
export const getDefaultTaskDefinition = (config: AWSConfig): RegisterTaskDefinitionCommandInput => {
  const portMappings: PortMapping[] = [
    {
      containerPort: config.containerPort,
      protocol: 'tcp'
    },
    {
      containerPort: config.previewPort,
      protocol: 'tcp'
    }
  ];

  const environment: KeyValuePair[] = [
    {
      name: 'NODE_ENV',
      value: 'production'
    }
  ];

  // Temporarily disable EFS mounting - use container local storage
  const mountPoints: MountPoint[] = [];

  const logConfiguration: LogConfiguration = {
    logDriver: 'awslogs',
    options: {
      'awslogs-group': '/ecs/mergen-workspace',
      'awslogs-region': config.region,
      'awslogs-stream-prefix': 'ecs'
    }
  };

  const healthCheck: HealthCheck = {
    command: [
      'CMD-SHELL',
      `curl -f http://localhost:${config.containerPort}/health || exit 1`
    ],
    interval: 30,
    timeout: 5,
    retries: 3,
    startPeriod: 60
  };

  const containerDefinitions: ContainerDefinition[] = [
    {
      name: 'workspace-container',
      image: config.containerImage,
      essential: true,
      portMappings,
      environment,
      mountPoints,
      logConfiguration,
      healthCheck,
      stopTimeout: 30
    }
  ];

  // Temporarily disable EFS volumes - use container local storage
  const volumes: Volume[] = [];

  return {
    family: config.ecsTaskDefinitionFamily,
    networkMode: 'awsvpc',
    requiresCompatibilities: ['FARGATE'],
    cpu: config.containerCpu.toString(),
    memory: config.containerMemory.toString(),
    executionRoleArn: config.ecsExecutionRoleArn,
    taskRoleArn: config.ecsTaskRoleArn,
    runtimePlatform: {
      cpuArchitecture: 'X86_64',
      operatingSystemFamily: 'LINUX'
    },
    containerDefinitions,
    volumes,
    tags: [
      {
        key: 'Project',
        value: 'Mergen'
      },
      {
        key: 'Component',
        value: 'Workspace'
      }
    ]
  };
};

// Default ECS service configuration
export const getDefaultServiceConfig = (config: AWSConfig, taskDefinitionArn: string, serviceName: string): CreateServiceCommandInput => ({
  serviceName,
  cluster: config.ecsClusterName,
  taskDefinition: taskDefinitionArn,
  desiredCount: 1,
  launchType: 'FARGATE',
  platformVersion: 'LATEST',
  networkConfiguration: {
    awsvpcConfiguration: {
      subnets: config.subnetIds,
      securityGroups: [config.securityGroupId],
      assignPublicIp: 'ENABLED'
    }
  },
  // Attach to ALB target group if configured
  // ...(config.albTargetGroupArn
  //   ? {
  //       loadBalancers: [
  //         {
  //           targetGroupArn: config.albTargetGroupArn,
  //           containerName: 'workspace-container',
  //           containerPort: config.previewPort
  //         }
  //       ],
  //       healthCheckGracePeriodSeconds: config.healthCheckGraceSeconds ?? 300
  //     }
  //   : {}),
  deploymentConfiguration: {
  maximumPercent: 200,
  minimumHealthyPercent: 0,
  deploymentCircuitBreaker: {
    enable: true,
    rollback: true
  }
  },
  enableExecuteCommand: true
});
