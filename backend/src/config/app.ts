import * as dotenv from 'dotenv';

dotenv.config();

export interface AppConfig {
  // Server Configuration
  port: number;
  nodeEnv: string;
  
  // Database Configuration
  mongodbUri: string;
  
  // JWT Configuration
  jwtSecret: string;
  
  // Logging Configuration
  logLevel: string;
  
  // LLM Configuration
  anthropicApiKey: string;
  bypassApiKey: boolean;
  llmTimeoutMs: number;
  maxTokens: number;
  
  // ECS Workspace Configuration
  workspaceTimeoutMs: number;
  workspaceRetryDelayMs: number;
  workspaceMaxRetries: number;
  workspaceHealthCheckTimeoutMs: number;
  workspaceStatusCacheTtlMs: number;
  workspaceCheckIntervalsMs: number[];
  
  // Build Configuration
  buildTimeoutMs: number;
  buildRetryDelayMs: number;
  buildMaxRetries: number;
  buildSafetyTimeoutMs: number;
  
  // Frontend Configuration
  frontendLifetimeExtensionThresholdMs: number;
  frontendThrottleIntervalMs: number;
  frontendCheckCadenceMs: number;
  
  // Auto-extension Configuration
  autoExtensionThresholdMinutes: number;

  // Dedicated Timeouts
  previewStartTimeoutMs: number;
  publishStartTimeoutMs: number;
}

export const getAppConfig = (): AppConfig => {
  return {
    // Server Configuration
    port: parseInt(process.env.PORT || '8000'),
    nodeEnv: process.env.NODE_ENV || 'development',
    
    // Database Configuration
    mongodbUri: process.env.MONGODB_URI || 'mongodb://localhost:27017/mergen_code',
    
    // JWT Configuration
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
    
    // Logging Configuration
    logLevel: process.env.LOG_LEVEL?.toLowerCase() || 'info',
    
    // LLM Configuration
    anthropicApiKey: process.env.ANTHROPIC_API_KEY || '',
    bypassApiKey: process.env.BYPASS_API_KEY === 'true',
    llmTimeoutMs: parseInt(process.env.LLM_TIMEOUT_MS || '300000'), // 5 minutes
    maxTokens: parseInt(process.env.MAX_TOKENS || '60000'),
    
    // ECS Workspace Configuration
    workspaceTimeoutMs: parseInt(process.env.WORKSPACE_TIMEOUT_MS || '10000'), // 10 seconds
    workspaceRetryDelayMs: parseInt(process.env.WORKSPACE_RETRY_DELAY_MS || '3000'), // 3 seconds
    workspaceMaxRetries: parseInt(process.env.WORKSPACE_MAX_RETRIES || '10'),
    workspaceHealthCheckTimeoutMs: parseInt(process.env.WORKSPACE_HEALTH_CHECK_TIMEOUT_MS || '10000'), // 10 seconds
    workspaceStatusCacheTtlMs: parseInt(process.env.WORKSPACE_STATUS_CACHE_TTL_MS || '3000'), // 3 seconds
    workspaceCheckIntervalsMs: [30000, 60000, 120000], // 30s, 1m, 2m - could be made configurable
    
    // Build Configuration
    buildTimeoutMs: parseInt(process.env.BUILD_TIMEOUT_MS || '300000'), // 5 minutes
    buildRetryDelayMs: parseInt(process.env.BUILD_RETRY_DELAY_MS || '3000'), // 3 seconds
    buildMaxRetries: parseInt(process.env.BUILD_MAX_RETRIES || '10'),
    buildSafetyTimeoutMs: parseInt(process.env.BUILD_SAFETY_TIMEOUT_MS || '300000'), // 5 minutes
    
    // Frontend Configuration
    frontendLifetimeExtensionThresholdMs: parseInt(process.env.FRONTEND_LIFETIME_EXTENSION_THRESHOLD_MS || '900000'), // 15 minutes
    frontendThrottleIntervalMs: parseInt(process.env.FRONTEND_THROTTLE_INTERVAL_MS || '60000'), // 1 minute
    frontendCheckCadenceMs: parseInt(process.env.FRONTEND_CHECK_CADENCE_MS || '60000'), // 1 minute
    
    // Auto-extension Configuration
    autoExtensionThresholdMinutes: parseInt(process.env.AUTO_EXTENSION_THRESHOLD_MINUTES || '15'),

    // Dedicated Timeouts
    previewStartTimeoutMs: parseInt(process.env.PREVIEW_START_TIMEOUT_MS || '120000'), // 2 minutes (container returns 202 at 120s)
    publishStartTimeoutMs: parseInt(process.env.PUBLISH_START_TIMEOUT_MS || '180000'), // 3 minutes (publish path allows 3 min)
  };
};

// Validate app configuration on module load
export const validateAppConfig = (): boolean => {
  try {
    getAppConfig();
    return true;
  } catch (error) {
    console.error('App Configuration validation failed:', error);
    return false;
  }
}; 