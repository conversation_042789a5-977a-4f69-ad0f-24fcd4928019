{"serviceName": "PLACEHOLDER_SERVICE_NAME", "cluster": "mergen-workspace-cluster", "taskDefinition": "PLACEHOLDER_TASK_DEFINITION_ARN", "desiredCount": 1, "launchType": "FARGATE", "platformVersion": "LATEST", "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["PLACEHOLDER_SUBNET_1", "PLACEHOLDER_SUBNET_2"], "securityGroups": ["PLACEHOLDER_SECURITY_GROUP"], "assignPublicIp": "ENABLED"}}, "loadBalancers": [], "serviceRegistries": [], "placementConstraints": [], "placementStrategy": [], "deploymentConfiguration": {"maximumPercent": 200, "minimumHealthyPercent": 0, "deploymentCircuitBreaker": {"enable": true, "rollback": true}}, "serviceTags": [{"key": "Project", "value": "<PERSON><PERSON>"}, {"key": "Component", "value": "Workspace"}, {"key": "WorkspaceId", "value": "PLACEHOLDER_WORKSPACE_ID"}, {"key": "UserId", "value": "PLACEHOLDER_USER_ID"}, {"key": "InterviewUuid", "value": "PLACEHOLDER_INTERVIEW_UUID"}], "enableExecuteCommand": true, "enableLogging": true}