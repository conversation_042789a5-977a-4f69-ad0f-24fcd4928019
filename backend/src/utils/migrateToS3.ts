import { Workspace } from '../models/Workspace';
import { S3FileService } from '../services/s3FileService';
import { User } from '../models/User';

export class S3MigrationService {
  private s3Service: S3FileService;

  constructor() {
    this.s3Service = new S3FileService();
  }

  /**
   * Migrate all workspaces from fileSystemSnapshot to S3
   */
  async migrateAllWorkspaces(): Promise<void> {
    console.log('🚀 Starting migration from fileSystemSnapshot to S3...');

    try {
      // Find all workspaces that have fileSystemSnapshot but no s3FileStorage
      const workspacesToMigrate = await Workspace.find({
        fileSystemSnapshot: { $exists: true },
        s3FileStorage: { $exists: false }
      });

      console.log(`📊 Found ${workspacesToMigrate.length} workspaces to migrate`);

      let migratedCount = 0;
      let errorCount = 0;

      for (const workspace of workspacesToMigrate) {
        try {
          await this.migrateWorkspace(workspace);
          migratedCount++;
          console.log(`✅ Migrated workspace ${workspace._id} (${migratedCount}/${workspacesToMigrate.length})`);
        } catch (error) {
          errorCount++;
          console.error(`❌ Failed to migrate workspace ${workspace._id}:`, error);
        }
      }

      console.log(`🎉 Migration completed: ${migratedCount} successful, ${errorCount} errors`);
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Migrate a single workspace from fileSystemSnapshot to S3
   */
  async migrateWorkspace(workspace: any): Promise<void> {
    if (!workspace.fileSystemSnapshot?.files || Object.keys(workspace.fileSystemSnapshot.files).length === 0) {
      console.log(`⏭️ Skipping workspace ${workspace._id} - no files to migrate`);
      return;
    }

    // Get user email from userId
    const user = await User.findById(workspace.userId);
    if (!user) {
      throw new Error(`User not found for workspace ${workspace._id}`);
    }

    const userEmail = user.email;
    const files = workspace.fileSystemSnapshot.files;

    console.log(`📤 Migrating ${Object.keys(files).length} files for workspace ${workspace._id} to S3...`);

    // Upload files to S3
    const uploadResult = await this.s3Service.uploadFiles(userEmail, workspace.interviewUuid, files);

    if (!uploadResult.success) {
      throw new Error(`Failed to upload files to S3: ${uploadResult.message}`);
    }

    // Calculate total size
    const totalSize = Object.values(files as { [key: string]: string }).reduce((sum: number, content: string) => sum + content.length, 0);
    
    // Remove workspace_ prefix if present for S3 prefix
    const cleanWorkspaceName = workspace.interviewUuid.replace(/^workspace_/, '');

    // Update workspace with S3 storage info
    workspace.s3FileStorage = {
      userEmail,
      s3Prefix: `${userEmail}/${cleanWorkspaceName}/`,
      lastSavedAt: workspace.fileSystemSnapshot.lastSavedAt || new Date(),
      version: workspace.fileSystemSnapshot.version || 1,
      fileCount: Object.keys(files).length,
      totalSize,
      initialFilesUploaded: workspace.fileSystemSnapshot.initialFilesUploaded || false
    };

    // Remove the old fileSystemSnapshot (cast to any to allow setting the old property)
    (workspace as any).fileSystemSnapshot = undefined;

    await workspace.save();

    console.log(`✅ Successfully migrated workspace ${workspace._id} to S3`);
  }

  /**
   * Verify migration by checking if files exist in S3
   */
  async verifyMigration(workspaceId: string): Promise<boolean> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      if (!workspace?.s3FileStorage) {
        console.log(`❌ Workspace ${workspaceId} has no S3 storage configured`);
        return false;
      }

      const s3FilesResult = await this.s3Service.listWorkspaceFiles(
        workspace.s3FileStorage.userEmail,
        workspace.interviewUuid
      );

      if (!s3FilesResult.success) {
        console.log(`❌ Failed to list S3 files for workspace ${workspaceId}: ${s3FilesResult.message}`);
        return false;
      }

      const fileCount = s3FilesResult.data?.files?.length || 0;
      const expectedCount = workspace.s3FileStorage.fileCount;

      if (fileCount !== expectedCount) {
        console.log(`❌ File count mismatch for workspace ${workspaceId}: found ${fileCount}, expected ${expectedCount}`);
        return false;
      }

      console.log(`✅ Verification successful for workspace ${workspaceId}: ${fileCount} files in S3`);
      return true;
    } catch (error) {
      console.error(`❌ Verification failed for workspace ${workspaceId}:`, error);
      return false;
    }
  }

  /**
   * Rollback migration for a workspace (restore from S3 to fileSystemSnapshot)
   */
  async rollbackWorkspace(workspaceId: string): Promise<void> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      if (!workspace?.s3FileStorage) {
        throw new Error(`Workspace ${workspaceId} has no S3 storage to rollback from`);
      }

      console.log(`🔄 Rolling back workspace ${workspaceId} from S3 to fileSystemSnapshot...`);

      // Download files from S3
      const s3FilesResult = await this.s3Service.downloadWorkspaceFiles(
        workspace.s3FileStorage.userEmail,
        workspace.interviewUuid
      );

      if (!s3FilesResult.success || !s3FilesResult.data?.files) {
        throw new Error(`Failed to download files from S3: ${s3FilesResult.message}`);
      }

      // Restore fileSystemSnapshot (cast to any to allow setting the old property)
      (workspace as any).fileSystemSnapshot = {
        files: s3FilesResult.data.files,
        structure: {}, // Structure will be rebuilt when needed
        lastSavedAt: workspace.s3FileStorage.lastSavedAt,
        version: workspace.s3FileStorage.version,
        initialFilesUploaded: workspace.s3FileStorage.initialFilesUploaded || false
      };

      // Remove S3 storage info
      workspace.s3FileStorage = undefined;

      await workspace.save();

      console.log(`✅ Successfully rolled back workspace ${workspaceId} to fileSystemSnapshot`);
    } catch (error) {
      console.error(`❌ Rollback failed for workspace ${workspaceId}:`, error);
      throw error;
    }
  }
}

// CLI script for running migration
if (require.main === module) {
  const migrationService = new S3MigrationService();
  
  const command = process.argv[2];
  const workspaceId = process.argv[3];

  switch (command) {
    case 'migrate-all':
      migrationService.migrateAllWorkspaces()
        .then(() => {
          console.log('✅ Migration completed successfully');
          process.exit(0);
        })
        .catch((error) => {
          console.error('❌ Migration failed:', error);
          process.exit(1);
        });
      break;

    case 'verify':
      if (!workspaceId) {
        console.error('❌ Please provide a workspace ID');
        process.exit(1);
      }
      migrationService.verifyMigration(workspaceId)
        .then((success) => {
          if (success) {
            console.log('✅ Verification successful');
            process.exit(0);
          } else {
            console.log('❌ Verification failed');
            process.exit(1);
          }
        })
        .catch((error) => {
          console.error('❌ Verification error:', error);
          process.exit(1);
        });
      break;

    case 'rollback':
      if (!workspaceId) {
        console.error('❌ Please provide a workspace ID');
        process.exit(1);
      }
      migrationService.rollbackWorkspace(workspaceId)
        .then(() => {
          console.log('✅ Rollback completed successfully');
          process.exit(0);
        })
        .catch((error) => {
          console.error('❌ Rollback failed:', error);
          process.exit(1);
        });
      break;

    default:
      console.log('Usage:');
      console.log('  npm run migrate-s3 migrate-all    # Migrate all workspaces to S3');
      console.log('  npm run migrate-s3 verify <id>    # Verify migration for a workspace');
      console.log('  npm run migrate-s3 rollback <id>  # Rollback a workspace from S3');
      process.exit(1);
  }
}