import fs from 'fs';

interface CodeBlock {
  language: string;
  filename?: string;
  content: string;
}

interface ExtractedCode {
  description: string;
  codeBlocks: CodeBlock[];
  projectStructure?: any;
  deploymentInstructions?: string;
  additionalSections?: { [key: string]: string };
}

export class CodeExtractor {
  

  
  /**
   * Read file content
   */
  private static async readFile(filePath: string): Promise<string> {
    try {
      return await fs.promises.readFile(filePath, 'utf-8');
    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error);
      return '';
    }
  }
  
  /**
   * Parse code content and extract code blocks with flexible section detection
   */
  private static parseCodeContent(content: string): ExtractedCode {
    // Remove the outer quotes if present and decode JSON string
    const cleanContent = this.decodeJsonString(content);

    const codeBlocks: CodeBlock[] = [];
    let description = '';
    let deploymentInstructions = '';
    const additionalSections: { [key: string]: string } = {};

    // Extract all sections with flexible header detection
    const sections = this.extractSections(cleanContent);

    // Process sections based on their type
    for (const section of sections) {
      const headerLower = section.header.toLowerCase();

      if (this.isDeploymentSection(headerLower)) {
        deploymentInstructions = section.content.trim();
      } else if (this.isDescriptiveSection(headerLower)) {
        // Store other descriptive sections in additionalSections
        additionalSections[section.header] = section.content.trim();
      }
    }

    // Extract description - everything before the first code block or section
    description = this.extractDescription(cleanContent, sections);

    // If no description found, try to use the first descriptive section
    if (!description && Object.keys(additionalSections).length > 0) {
      const firstSection = Object.keys(additionalSections)[0];
      description = additionalSections[firstSection];
      delete additionalSections[firstSection];
    }
    
    // Pattern for file headers: ### `path/to/file.ext` or ### `file.ext` (description) followed by code block
    // This handles both simple patterns and patterns with parenthetical descriptions
    const filePattern = /###\s*`([^`]+)`(?:\s*\(([^)]+)\))?\s*\n```(\w+)?\n(.*?)\n```/gs;
    let match;
    
    while ((match = filePattern.exec(cleanContent)) !== null) {
      let filepath = match[1];
      const description = match[2]; // Extract the description from parentheses
      const language = match[3] || this.getLanguageFromFilename(filepath);
      const code = match[4].trim();
      
      // If there's a description and it's a generic filename like .env, place it in the appropriate directory
      if (description && filepath === '.env') {
        const cleanDesc = description.toLowerCase().trim();
        
        // Check if the description refers to a specific folder
        // Look for common folder indicators or treat the description as a folder name
        if (cleanDesc && cleanDesc !== 'root' && cleanDesc !== 'main' && cleanDesc !== 'project') {
          // Use the description as the folder name for .env placement
          filepath = `${cleanDesc}/.env`;
        }
        // If description indicates root or is empty/generic, keep .env in root
        // (filepath stays as '.env')
      }
      
      if (code) {
        codeBlocks.push({
          language,
          filename: filepath,
          content: code
        });
      }
    }
    
    // If no matches with the primary pattern, try alternative patterns
    if (codeBlocks.length === 0) {
      // Try pattern without backticks around filename
      const altPattern = /###\s+([^\n]+)\s*\n```(\w+)?\n(.*?)\n```/gs;
      let altMatch;
      
      while ((altMatch = altPattern.exec(cleanContent)) !== null) {
        let filepath = altMatch[1].trim();
        // Clean up the filename (remove extra formatting)
        filepath = filepath.replace(/^`|`$/g, '').replace(/\s*\([^)]+\)$/, '');
        const language = altMatch[2] || this.getLanguageFromFilename(filepath);
        const code = altMatch[3].trim();
        
        if (code && filepath) {
          codeBlocks.push({
            language,
            filename: filepath,
            content: code
          });
        }
      }
    }
    
    // Try to build project structure from code blocks
    const projectStructure = this.buildProjectStructure(codeBlocks);
    
    return {
      description,
      codeBlocks,
      projectStructure,
      deploymentInstructions,
      additionalSections
    };
  }

  /**
   * Extract sections from content with flexible header detection
   * Only matches ## and # headers, excludes ### which are code block headers
   */
  private static extractSections(content: string): Array<{ header: string; content: string }> {
    const sections: Array<{ header: string; content: string }> = [];

    // Match section header patterns (## Header, # Header) but NOT ### which are code blocks
    const sectionPattern = /^(#{1,2})\s+(.+?)$/gm;
    const matches = Array.from(content.matchAll(sectionPattern));

    for (let i = 0; i < matches.length; i++) {
      const match = matches[i];
      const header = match[2].trim();

      // Skip headers that look like code block filenames (contain backticks or file extensions)
      if (header.includes('`') || this.looksLikeFilename(header)) {
        continue;
      }

      const startIndex = match.index! + match[0].length;

      // Find the end of this section (next header or end of content)
      let endIndex = content.length;
      if (i < matches.length - 1) {
        endIndex = matches[i + 1].index!;
      }

      const sectionContent = content.substring(startIndex, endIndex).trim();

      // Only include sections that have meaningful content and aren't code blocks
      if (sectionContent && sectionContent.length > 10 && !this.isCodeBlockContent(sectionContent)) {
        sections.push({ header, content: sectionContent });
      }
    }

    return sections;
  }

  /**
   * Check if a section header indicates deployment instructions
   */
  private static isDeploymentSection(headerLower: string): boolean {
    const deploymentKeywords = [
      'deployment', 'deploy', 'installation', 'install', 'setup',
      'getting started', 'quick start', 'running', 'build'
    ];

    return deploymentKeywords.some(keyword => headerLower.includes(keyword));
  }

  /**
   * Check if a section header indicates descriptive content
   */
  private static isDescriptiveSection(headerLower: string): boolean {
    const descriptiveKeywords = [
      'overview', 'introduction', 'about', 'description', 'features',
      'configuration', 'usage', 'guide', 'tutorial', 'examples',
      'architecture', 'structure', 'requirements', 'prerequisites'
    ];

    return descriptiveKeywords.some(keyword => headerLower.includes(keyword));
  }

  /**
   * Check if a header looks like a filename
   */
  private static looksLikeFilename(header: string): boolean {
    // Common file extensions and patterns
    const filePatterns = [
      /\.(js|ts|jsx|tsx|html|css|json|yml|yaml|md|txt|py|java|cpp|c|php|rb|go|rs)$/i,
      /\/[^\/]+\.[a-zA-Z0-9]+$/,  // path/filename.ext
      /^[a-zA-Z0-9_-]+\.[a-zA-Z0-9]+$/  // filename.ext
    ];

    return filePatterns.some(pattern => pattern.test(header));
  }

  /**
   * Check if content looks like a code block
   */
  private static isCodeBlockContent(content: string): boolean {
    // Check for code block indicators
    const codeIndicators = [
      /^```/m,  // Starts with code fence
      /^{[\s\S]*}$/,  // JSON-like content
      /^<[^>]+>/m,  // HTML-like content
      /^\s*(import|export|const|let|var|function|class)\s/m,  // JavaScript keywords
      /^\s*(def|class|import|from)\s/m,  // Python keywords
      /^\s*(<\?php|namespace|use)\s/m  // PHP keywords
    ];

    return codeIndicators.some(pattern => pattern.test(content.trim()));
  }

  /**
   * Extract main description from content
   */
  private static extractDescription(content: string, sections: Array<{ header: string; content: string }>): string {
    // Try to find content before the first section or code block
    let description = '';

    // Find the first section or code block
    const firstSectionIndex = sections.length > 0 ? content.indexOf(`## ${sections[0].header}`) : -1;
    const firstCodeBlockIndex = content.indexOf('###');

    let cutoffIndex = content.length;
    if (firstSectionIndex !== -1 && firstCodeBlockIndex !== -1) {
      cutoffIndex = Math.min(firstSectionIndex, firstCodeBlockIndex);
    } else if (firstSectionIndex !== -1) {
      cutoffIndex = firstSectionIndex;
    } else if (firstCodeBlockIndex !== -1) {
      cutoffIndex = firstCodeBlockIndex;
    }

    if (cutoffIndex > 0) {
      description = content.substring(0, cutoffIndex).trim();
    }

    // Clean up the description
    if (description) {
      // Remove any leading/trailing quotes or formatting
      description = description.replace(/^["'`]+|["'`]+$/g, '').trim();
    }

    return description;
  }

  /**
   * Decode JSON-escaped string (similar to quick_extract.py)
   */
  private static decodeJsonString(content: string): string {
    let cleanContent = content.trim();
    if (cleanContent.startsWith('"') && cleanContent.endsWith('"')) {
      cleanContent = cleanContent.slice(1, -1);
      cleanContent = cleanContent
        .replace(/\\"/g, '"')
        .replace(/\\n/g, '\n')
        .replace(/\\t/g, '\t')
        .replace(/\\r/g, '\r')
        .replace(/\\\\/g, '\\');
    }
    return cleanContent;
  }

  /**
   * Get language from filename extension
   */
  private static getLanguageFromFilename(filename: string): string {
    // Handle special cases first
    const basename = filename.toLowerCase();
    if (basename === '.env' || basename.endsWith('.env') || basename.includes('.env.')) {
      return 'bash';
    }
    if (basename === 'dockerfile' || basename.endsWith('dockerfile')) {
      return 'dockerfile';
    }
    if (basename === 'nginx.conf' || basename.includes('nginx') && basename.includes('.conf')) {
      return 'nginx';
    }
    
    const ext = filename.split('.').pop()?.toLowerCase();
    const langMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'jsx',
      'ts': 'typescript',
      'tsx': 'tsx',
      'json': 'json',
      'html': 'html',
      'css': 'css',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'yml': 'yaml',
      'yaml': 'yaml',
      'xml': 'xml',
      'md': 'markdown',
      'txt': 'text',
      'env': 'bash',
      'conf': 'nginx',
      'sh': 'bash',
      'bash': 'bash'
    };
    return langMap[ext || ''] || 'text';
  }
  

  
  /**
   * Build project structure from code blocks
   */
  private static buildProjectStructure(codeBlocks: CodeBlock[]): any {
    const structure: any = {};
    
    for (const block of codeBlocks) {
      if (!block.filename) continue;
      
      const parts = block.filename.split('/');
      let current = structure;
      
      // Build nested structure
      for (let i = 0; i < parts.length - 1; i++) {
        const part = parts[i];
        if (!current[part]) {
          current[part] = {
            type: 'folder',
            children: {}
          };
        }
        current = current[part].children;
      }
      
      // Add file
      const filename = parts[parts.length - 1];
      current[filename] = {
        type: 'file',
        content: block.content,
        language: block.language
      };
    }
    
    return Object.keys(structure).length > 0 ? structure : null;
  }
  
  /**
   * Extract code structure from content string (generic method for LLM responses)
   */
  static extractCodeStructure(content: string): any {
    try {
      const extracted = this.parseCodeContent(content);

      if (extracted.projectStructure) {
        return extracted.projectStructure;
      }

      if (extracted.codeBlocks.length > 0) {
        return this.buildSimpleStructure(extracted.codeBlocks);
      }

      // Return empty structure if no content
      return {};
    } catch (error) {
      console.error('Error extracting code structure from content:', error);
      // Return empty structure on error
      return {};
    }
  }

  /**
   * Extract full parsed content from content string (includes description and code blocks)
   */
  static extractFullContent(content: string): ExtractedCode {
    try {
      return this.parseCodeContent(content);
    } catch (error) {
      console.error('Error extracting full content:', error);
      return {
        description: '',
        codeBlocks: [],
        projectStructure: null,
        deploymentInstructions: '',
        additionalSections: {}
      };
    }
  }

  /**
   * Get code structure from a local file (for testing with local files)
   * @param filePath - File path (absolute or relative to current working directory)
   */
  static async getCodeStructureFromFile(filePath: string): Promise<any> {
    try {
      if (await this.fileExists(filePath)) {
        const content = await this.readFile(filePath);
        return this.extractCodeStructure(content);
      }

      // Return empty structure if file doesn't exist
      console.warn(`File not found: ${filePath}`);
      return {};
    } catch (error) {
      console.error(`Error getting code structure from file: ${filePath}`, error);
      // Return empty structure on error
      return {};
    }
  }

  /**
   * Get full content (description + code blocks + structure) from a local file
   * @param filePath - File path (absolute or relative to current working directory)
   */
  static async getFullContentFromFile(filePath: string): Promise<ExtractedCode> {
    try {
      if (await this.fileExists(filePath)) {
        const content = await this.readFile(filePath);
        return this.extractFullContent(content);
      }

      // Return empty content if file doesn't exist
      console.warn(`File not found: ${filePath}`);
      return {
        description: '',
        codeBlocks: [],
        projectStructure: null,
        deploymentInstructions: '',
        additionalSections: {}
      };
    } catch (error) {
      console.error(`Error getting full content from file: ${filePath}`, error);
      return {
        description: '',
        codeBlocks: [],
        projectStructure: null,
        deploymentInstructions: '',
        additionalSections: {}
      };
    }
  }



  /**
   * Check if file exists
   */
  private static async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.promises.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
  
  /**
   * Build a simple project structure when no clear structure is found
   */
  private static buildSimpleStructure(codeBlocks: CodeBlock[]): any {
    const structure: any = {};
    
    for (const block of codeBlocks) {
      const filename = block.filename || `file_${Date.now()}.${this.getExtensionForLanguage(block.language)}`;
      
      structure[filename] = {
        type: 'file',
        content: block.content,
        language: block.language
      };
    }
    
    return structure;
  }
  
  /**
   * Get file extension for programming language
   */
  private static getExtensionForLanguage(language: string): string {
    const extensions: { [key: string]: string } = {
      'javascript': 'js',
      'typescript': 'ts',
      'jsx': 'jsx',
      'tsx': 'tsx',
      'json': 'json',
      'html': 'html',
      'css': 'css',
      'python': 'py',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'yaml': 'yml',
      'yml': 'yml',
      'xml': 'xml',
      'markdown': 'md',
      'text': 'txt'
    };
    
    return extensions[language.toLowerCase()] || 'txt';
  }
} 