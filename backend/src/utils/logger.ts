import dotenv from 'dotenv';
import { getAppConfig } from '../config/app';

dotenv.config();

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  VERBOSE = 4
}

export class Logger {
  private static instance: Logger;
  private logLevel: LogLevel;

  private constructor() {
    const envLogLevel = getAppConfig().logLevel;
    this.logLevel = this.parseLogLevel(envLogLevel);
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private parseLogLevel(level: string): LogLevel {
    switch (level) {
      case 'error': return LogLevel.ERROR;
      case 'warn': return LogLevel.WARN;
      case 'info': return LogLevel.INFO;
      case 'debug': return LogLevel.DEBUG;
      case 'verbose': return LogLevel.VERBOSE;
      default: return LogLevel.INFO;
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return level <= this.logLevel;
  }

  // Serialize args more helpfully than JSON.stringify on Error objects
  private serializeArg(arg: any): string {
    try {
      if (arg instanceof Error) {
        const name = (arg as any).name || 'Error';
        const code = (arg as any).code;
        const msg = arg.message;
        return code ? `${name}[${code}]: ${msg}` : `${name}: ${msg}`;
      }
      if (arg && typeof arg === 'object') {
        const name = (arg as any).__type || (arg as any).name;
        const code = (arg as any).code;
        const fault = (arg as any).$fault;
        const status = (arg as any).$metadata?.httpStatusCode;
        const reqId = (arg as any).$metadata?.requestId;
        const message = (arg as any).message || (arg as any).toString?.();
        const parts: string[] = [];
        if (name) parts.push(String(name));
        if (code) parts.push(String(code));
        if (fault) parts.push(String(fault));
        if (typeof status !== 'undefined') parts.push(`HTTP ${status}`);
        if (reqId) parts.push(`reqId ${reqId}`);
        if (message && (!name || !String(message).includes(String(name)))) {
          parts.push(String(message));
        }
        if (parts.length > 0) return parts.join(' | ');
        // Fallback to JSON stringification
        return JSON.stringify(arg);
      }
      return String(arg);
    } catch {
      return String(arg);
    }
  }

  private formatMessage(level: string, message: string, ...args: any[]): string {
    const timestamp = new Date().toISOString();
    const formattedArgs = args.length > 0 ? ' ' + args.map(arg => this.serializeArg(arg)).join(' ') : '';
    return `[${timestamp}] ${level}: ${message}${formattedArgs}`;
  }

  public error(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      console.error(this.formatMessage('ERROR', message, ...args));
    }
  }

  public warn(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.WARN)) {
      console.warn(this.formatMessage('WARN', message, ...args));
    }
  }

  public info(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.INFO)) {
      console.log(this.formatMessage('INFO', message, ...args));
    }
  }

  public debug(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.log(this.formatMessage('DEBUG', message, ...args));
    }
  }

  public verbose(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.VERBOSE)) {
      console.log(this.formatMessage('VERBOSE', message, ...args));
    }
  }

  // Convenience methods for common patterns
  public lifecycle(message: string, ...args: any[]): void {
    this.debug(`🔄 ${message}`, ...args);
  }

  public cleanup(message: string, ...args: any[]): void {
    this.debug(`🧹 ${message}`, ...args);
  }

  public health(message: string, ...args: any[]): void {
    this.verbose(`🏥 ${message}`, ...args);
  }

  public sync(message: string, ...args: any[]): void {
    this.debug(`💾 ${message}`, ...args);
  }

  public workspace(message: string, ...args: any[]): void {
    this.debug(`📁 ${message}`, ...args);
  }

  public container(message: string, ...args: any[]): void {
    this.debug(`🐳 ${message}`, ...args);
  }

  public success(message: string, ...args: any[]): void {
    this.info(`✅ ${message}`, ...args);
  }

  public failure(message: string, ...args: any[]): void {
    this.error(`❌ ${message}`, ...args);
  }

  public warning(message: string, ...args: any[]): void {
    this.warn(`⚠️ ${message}`, ...args);
  }

  // Method to change log level at runtime
  public setLogLevel(level: LogLevel | string): void {
    if (typeof level === 'string') {
      this.logLevel = this.parseLogLevel(level);
    } else {
      this.logLevel = level;
    }
  }

  public getLogLevel(): LogLevel {
    return this.logLevel;
  }

  public getLogLevelName(): string {
    switch (this.logLevel) {
      case LogLevel.ERROR: return 'error';
      case LogLevel.WARN: return 'warn';
      case LogLevel.INFO: return 'info';
      case LogLevel.DEBUG: return 'debug';
      case LogLevel.VERBOSE: return 'verbose';
      default: return 'unknown';
    }
  }
}

// Export singleton instance
export const logger = Logger.getInstance();

// Export convenience functions
export const log = {
  error: (message: string, ...args: any[]) => logger.error(message, ...args),
  warn: (message: string, ...args: any[]) => logger.warn(message, ...args),
  info: (message: string, ...args: any[]) => logger.info(message, ...args),
  debug: (message: string, ...args: any[]) => logger.debug(message, ...args),
  verbose: (message: string, ...args: any[]) => logger.verbose(message, ...args),
  
  // Convenience methods
  lifecycle: (message: string, ...args: any[]) => logger.lifecycle(message, ...args),
  cleanup: (message: string, ...args: any[]) => logger.cleanup(message, ...args),
  health: (message: string, ...args: any[]) => logger.health(message, ...args),
  sync: (message: string, ...args: any[]) => logger.sync(message, ...args),
  workspace: (message: string, ...args: any[]) => logger.workspace(message, ...args),
  container: (message: string, ...args: any[]) => logger.container(message, ...args),
  success: (message: string, ...args: any[]) => logger.success(message, ...args),
  failure: (message: string, ...args: any[]) => logger.failure(message, ...args),
  warning: (message: string, ...args: any[]) => logger.warning(message, ...args)
};
