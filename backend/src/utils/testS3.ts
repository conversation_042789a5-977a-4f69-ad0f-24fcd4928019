import { S3FileService } from '../services/s3FileService';

async function testS3Service() {
  console.log('🧪 Testing S3 File Service...');
  
  const s3Service = new S3FileService();
  const testUserEmail = '<EMAIL>';
  const testWorkspace = 'test_workspace';
  const testFiles = {
    'test.txt': 'Hello, S3!',
    'src/index.js': 'console.log("Hello from S3");',
    'package.json': '{"name": "test", "version": "1.0.0"}'
  };

  try {
    // Test upload
    console.log('📤 Testing file upload...');
    const uploadResult = await s3Service.uploadFiles(testUserEmail, testWorkspace, testFiles);
    console.log('Upload result:', uploadResult);

    if (!uploadResult.success) {
      throw new Error(`Upload failed: ${uploadResult.message}`);
    }

    // Test list files
    console.log('📋 Testing file listing...');
    const listResult = await s3Service.listWorkspaceFiles(testUserEmail, testWorkspace);
    console.log('List result:', listResult);

    // Test download single file
    console.log('📥 Testing single file download...');
    const downloadResult = await s3Service.downloadFile(testUserEmail, testWorkspace, 'test.txt');
    console.log('Download result:', downloadResult);

    // Test download all files
    console.log('📥 Testing all files download...');
    const downloadAllResult = await s3Service.downloadWorkspaceFiles(testUserEmail, testWorkspace);
    console.log('Download all result:', downloadAllResult);

    // Test file exists
    console.log('🔍 Testing file exists...');
    const existsResult = await s3Service.fileExists(testUserEmail, testWorkspace, 'test.txt');
    console.log('File exists:', existsResult);

    // Test cleanup
    console.log('🗑️ Testing file cleanup...');
    const deleteResult = await s3Service.deleteWorkspaceFiles(testUserEmail, testWorkspace);
    console.log('Delete result:', deleteResult);

    console.log('✅ All S3 tests passed!');
  } catch (error) {
    console.error('❌ S3 test failed:', error);
    process.exit(1);
  }
}

// Run test if called directly
if (require.main === module) {
  testS3Service()
    .then(() => {
      console.log('🎉 S3 service test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 S3 service test failed:', error);
      process.exit(1);
    });
}

export { testS3Service };