import jwt from 'jsonwebtoken';
import { getAppConfig } from '../config/app';

const JWT_SECRET = getAppConfig().jwtSecret;

export const generateToken = (userId: string, email: string): string => {
  const payload = { 
    userId,
    email 
  };
  
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });
};

export const verifyToken = (token: string): any => {
  return jwt.verify(token, JWT_SECRET);
}; 