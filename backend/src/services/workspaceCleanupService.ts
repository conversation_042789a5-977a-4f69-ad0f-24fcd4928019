import { ECSWorkspaceService } from './ecsWorkspaceService';
import { ContainerLifecycleService } from './containerLifecycleService';
import { getAWSConfig } from '../config/aws';
import { log } from '../utils/logger';
import mongoose from 'mongoose';

export class WorkspaceCleanupService {
  private ecsWorkspaceService: ECSWorkspaceService;
  private lifecycleService: ContainerLifecycleService;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private syncInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  constructor() {
    this.ecsWorkspaceService = new ECSWorkspaceService();
    this.lifecycleService = new ContainerLifecycleService();
  }

  /**
   * Start the cleanup scheduler
   */
  start(): void {
    if (this.isRunning) {
      log.cleanup('Workspace cleanup service is already running');
      return;
    }

    const config = getAWSConfig();
    const intervalMs = config.cleanupIntervalMinutes * 60 * 1000;

    log.info(`Starting workspace cleanup service (cleanup: ${config.cleanupIntervalMinutes}min, sync: ${config.syncIntervalMinutes}min)`);

    // Run cleanup immediately with a small delay to ensure database is ready
    setTimeout(() => {
      this.runCleanup().catch(error => {
        log.failure('Error during initial cleanup:', error);
      });
    }, 10000); // 10 second delay to ensure database connection is fully established

    // Schedule periodic cleanup
    this.cleanupInterval = setInterval(() => {
      this.runCleanup().catch(error => {
        log.failure('Error during scheduled cleanup:', error);
      });
    }, intervalMs);

    // Schedule periodic database sync
    const syncIntervalMs = config.syncIntervalMinutes * 60 * 1000;
    this.syncInterval = setInterval(() => {
      this.runPeriodicSync().catch(error => {
        log.failure('Error during periodic sync:', error);
      });
    }, syncIntervalMs);

    this.isRunning = true;
    log.success('Workspace cleanup service started successfully');
  }

  /**
   * Stop the cleanup scheduler
   */
  stop(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    this.isRunning = false;
    log.cleanup('Workspace cleanup service stopped');
  }

  /**
   * Run cleanup process
   */
  private async runCleanup(): Promise<void> {
    try {
      console.log('Running comprehensive workspace lifecycle management...');
      log.lifecycle('Running comprehensive workspace lifecycle management...');
      const startTime = Date.now();

      // Check if we can connect to the database
      if (mongoose.connection.readyState !== 1) {
        log.warning('Database not ready, skipping cleanup');
        return;
      }

      const stats = await this.lifecycleService.performLifecycleManagement();

      const duration = Date.now() - startTime;
      log.success(`Lifecycle management completed in ${duration}ms`);
      log.info(`Stats: ${stats.cleanedUpWorkspaces} cleaned up, ${stats.failedCleanups} failed, ${stats.runningWorkspaces} still running, ${stats.expiredWorkspaces} expired found`);
      
      if (stats.cleanedUpWorkspaces > 0) {
        log.success(`Successfully cleaned up ${stats.cleanedUpWorkspaces} expired workspaces`);
      }
      
    } catch (error) {
      log.failure('Error during workspace lifecycle management:', error);
      // Don't throw the error to prevent the service from stopping
      // but log it for debugging
      console.error('Cleanup error details:', error);
    }
  }

  /**
   * Run periodic database sync
   */
  private async runPeriodicSync(): Promise<void> {
    try {
      log.sync('Running periodic database sync...');
      const startTime = Date.now();

      await this.lifecycleService.runPeriodicDatabaseSync();

      const duration = Date.now() - startTime;
      log.success(`Periodic database sync completed in ${duration}ms`);
    } catch (error) {
      log.failure('Error during periodic database sync:', error);
    }
  }

  /**
   * Get cleanup service status
   */
  getStatus(): { isRunning: boolean; intervalMinutes: number } {
    const config = getAWSConfig();
    return {
      isRunning: this.isRunning,
      intervalMinutes: config.cleanupIntervalMinutes
    };
  }

  /**
   * Force run cleanup (for manual triggers)
   */
  async forceCleanup(): Promise<void> {
    log.cleanup('Force running workspace cleanup...');
    console.log('Force running workspace cleanup...');
    await this.runCleanup();
  }
}

// Singleton instance
export const workspaceCleanupService = new WorkspaceCleanupService();
