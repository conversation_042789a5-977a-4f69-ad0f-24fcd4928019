import { Workspace, IWorkspace } from '../models/Workspace';
import { ECSContainer, IECSContainer } from '../models/ECSContainer';
import { ECSWorkspaceService } from './ecsWorkspaceService';
import { ECSFileSystemService } from './ecsFileSystemService';
import { getAWSConfig } from '../config/aws';
import { log } from '../utils/logger';

export interface LifecycleStats {
  totalWorkspaces: number;
  runningWorkspaces: number;
  expiredWorkspaces: number;
  cleanedUpWorkspaces: number;
  failedCleanups: number;
}

export class ContainerLifecycleService {
  private ecsWorkspaceService: ECSWorkspaceService;
  private ecsFileSystemService: ECSFileSystemService;
  private config: ReturnType<typeof getAWSConfig>;

  constructor() {
    this.ecsWorkspaceService = new ECSWorkspaceService();
    this.ecsFileSystemService = new ECSFileSystemService();
    this.config = getAWSConfig();
  }

  /**
   * Perform comprehensive lifecycle management
   */
  async performLifecycleManagement(): Promise<LifecycleStats> {
    log.lifecycle('Starting container lifecycle management...');

    const stats: LifecycleStats = {
      totalWorkspaces: 0,
      runningWorkspaces: 0,
      expiredWorkspaces: 0,
      cleanedUpWorkspaces: 0,
      failedCleanups: 0
    };

    try {
      // Get all workspaces
      const allWorkspaces = await Workspace.find({});
      stats.totalWorkspaces = allWorkspaces.length;

      // Count running workspaces
      stats.runningWorkspaces = await Workspace.countDocuments({
        containerStatus: { $in: ['running', 'starting', 'creating', 'started'] }
      });

      // Find expired workspaces
      const expiredWorkspaces = await Workspace.find({
        expiresAt: { $lt: new Date() },
        // autoCleanup: true,
        containerStatus: { $in: ['running', 'starting', 'creating', 'started'] }
      });

      // console.log('Expired workspaces:', expiredWorkspaces);
      if (expiredWorkspaces.length > 0) {
        console.log("length of expired workspaces:", expiredWorkspaces.length);
        const expiredUuids = expiredWorkspaces.map(w => w.interviewUuid);
        console.log('Expired workspace interviewUuids:', expiredUuids);
      } else {
        console.log('No expired workspaces found');
      }

      stats.expiredWorkspaces = expiredWorkspaces.length;

      log.verbose(`Lifecycle stats: ${stats.totalWorkspaces} total, ${stats.runningWorkspaces} running, ${stats.expiredWorkspaces} expired`);

      // Process expired workspaces
      for (const workspace of expiredWorkspaces) {
        try {
          await this.processExpiredWorkspace(workspace);
          stats.cleanedUpWorkspaces++;
        } catch (error) {
          log.failure(`Failed to cleanup workspace ${workspace._id}:`, error);
          stats.failedCleanups++;
        }
      }

      // Update container health status
      await this.updateContainerHealthStatus();

      // Cleanup orphaned containers
      await this.cleanupOrphanedContainers();

      log.success(`Lifecycle management completed: ${stats.cleanedUpWorkspaces} cleaned up, ${stats.failedCleanups} failed`);

    } catch (error) {
      log.failure('Error during lifecycle management:', error);
    }

    return stats;
  }

  /**
   * Process an expired workspace
   */
  private async processExpiredWorkspace(workspace: IWorkspace): Promise<void> {
    log.cleanup(`Processing expired workspace: ${workspace._id}`);
    console.log('Processing expired workspace:', workspace._id);
    try {
      // Save current files to snapshot before cleanup (do not auto-extend lifetime)
      await this.saveWorkspaceSnapshot(workspace);

      // Stop the ECS service/task
      await this.ecsWorkspaceService.stopWorkspace((workspace._id as any).toString());

      // Update workspace status
      workspace.containerStatus = 'stopped';
      await workspace.save();

      // Update container record
      const container = await ECSContainer.findOne({ workspaceId: (workspace._id as any).toString() });
      if (container) {
        await container.addEvent('expired', 'Workspace expired and cleaned up');
      }

      log.success(`Expired workspace cleaned up: ${workspace._id}`);

    } catch (error) {
      log.failure(`Error processing expired workspace ${workspace._id}:`, error);
      throw error;
    }
  }

  /**
   * Extract all file paths from workspace structure recursively
   */
  private extractFilePathsFromStructure(structure: any, basePath: string = ''): string[] {
    const filePaths: string[] = [];

    for (const [name, item] of Object.entries(structure)) {
      const currentPath = basePath ? `${basePath}/${name}` : name;

      // Skip node_modules and other large directories to reduce backup size and logging
      if (this.shouldSkipPath(currentPath)) {
        continue;
      }

      if ((item as any).type === 'file') {
        filePaths.push(currentPath);
      } else if ((item as any).type === 'folder' && (item as any).children) {
        // Handle children array format
        if (Array.isArray((item as any).children)) {
          const childrenObj: any = {};
          for (const child of (item as any).children) {
            childrenObj[child.name] = child;
          }
          filePaths.push(...this.extractFilePathsFromStructure(childrenObj, currentPath));
        } else {
          // Handle children object format
          filePaths.push(...this.extractFilePathsFromStructure((item as any).children, currentPath));
        }
      }
    }

    return filePaths;
  }

  /**
   * Check if a path should be skipped during backup
   */
  private shouldSkipPath(path: string): boolean {
    const skipPatterns = [
      // Dependencies and package managers
      'node_modules',
      'package-lock.json',
      'yarn.lock',
      'pnpm-lock.yaml',
      'composer.lock',
      'Pipfile.lock',
      'poetry.lock',
      'Cargo.lock',
      'go.sum',

      // Version control
      '.git',
      '.gitignore',
      '.gitattributes',
      '.svn',
      '.hg',

      // Build outputs and caches
      'dist',
      'build',
      'out',
      '.next',
      '.nuxt',
      '.cache',
      '.parcel-cache',
      '.webpack',
      '.rollup.cache',
      'coverage',
      '.nyc_output',
      '.pytest_cache',
      '__pycache__',
      '*.pyc',
      '*.pyo',
      '*.pyd',
      '.tox',
      'target', // Rust/Java build directory
      'bin',
      'obj',

      // Logs and temporary files
      'logs',
      '*.log',
      '*.tmp',
      '*.temp',
      '.DS_Store',
      'Thumbs.db',
      'desktop.ini',
      '.env.local',
      '.env.*.local',

      // IDE and editor files
      '.vscode',
      '.idea',
      '*.swp',
      '*.swo',
      '*~',
      '.sublime-*',

      // OS files
      '.Trash',
      '.Spotlight-V100',
      '.fseventsd'
    ];

    return skipPatterns.some(pattern => {
      if (pattern.includes('*')) {
        // Simple wildcard matching
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(path);
      }
      return path.includes(pattern);
    });
  }

  /**
   * Save workspace files to snapshot before cleanup
   */
  private async saveWorkspaceSnapshot(workspace: IWorkspace): Promise<void> {
    try {
      // Only save snapshot if container is still accessible
      if (workspace.containerStatus === 'running' && workspace.publicIp) {
        log.sync(`Saving complete workspace snapshot: ${workspace._id}`);

        // Get current file structure
        const structure = await this.ecsFileSystemService.getWorkspaceStructure((workspace._id as any).toString(), { skipAutoExtend: true });

        // Get all files from the container
        const files: { [key: string]: string } = {};

        try {
          // Extract all file paths from the structure and read their content
          const filePaths = this.extractFilePathsFromStructure(structure);
          log.verbose(`Found ${filePaths.length} files to backup`);

          // Read each file's content
          for (const filePath of filePaths) {
            try {
              const fileData = await this.ecsFileSystemService.readFile((workspace._id as any).toString(), filePath, { skipAutoExtend: true });
              files[filePath] = fileData.content;
              log.verbose(`Backed up file: ${filePath} (${fileData.content.length} chars)`);
            } catch (fileError) {
              log.warning(`Failed to backup file ${filePath}:`, fileError);
              // Continue with other files even if one fails
            }
          }

          log.debug(`Successfully backed up ${Object.keys(files).length}/${filePaths.length} files`);
        } catch (structureError) {
          log.failure(`Error reading files from structure:`, structureError);
          // Fall back to existing S3 files if available
          if (workspace.s3FileStorage?.userEmail) {
            log.debug(`Using existing S3 files as fallback`);
            const { S3FileService } = await import('./s3FileService');
            const s3Service = new S3FileService();
            const s3FilesResult = await s3Service.downloadWorkspaceFiles(
              workspace.s3FileStorage.userEmail,
              workspace.interviewUuid
            );
            if (s3FilesResult.success && s3FilesResult.data?.files) {
              Object.assign(files, s3FilesResult.data.files);
            }
          }
        }

        // Save the complete backup to S3 (don't merge - this is a complete backup)
        if (workspace.s3FileStorage?.userEmail) {
          await workspace.saveFilesToS3(files, workspace.s3FileStorage.userEmail, false);
        } else {
          log.warning(`No S3 storage configured for workspace ${workspace._id}, skipping backup`);
        }

        log.success(`Complete workspace snapshot saved: ${workspace._id} (${Object.keys(files).length} files)`);
      } else {
        log.warning(`Skipping snapshot for inaccessible workspace: ${workspace._id}`);
      }
    } catch (error) {
      log.failure(`Error saving workspace snapshot ${workspace._id}:`, error);
      // Don't throw - cleanup should continue even if snapshot fails
    }
  }

  /**
   * Update health status for all running containers
   */
  private async updateContainerHealthStatus(): Promise<void> {
    try {
      const runningWorkspaces = await Workspace.find({
        containerStatus: 'running',
        publicIp: { $exists: true, $ne: null }
      });

      log.health(`Checking health for ${runningWorkspaces.length} running containers`);

      for (const workspace of runningWorkspaces) {
        try {
          const isHealthy = await this.ecsFileSystemService.checkHealth((workspace._id as any).toString());

          const container = await ECSContainer.findOne({ workspaceId: (workspace._id as any).toString() });
          if (container) {
            if (isHealthy) {
              await container.markHealthy();
            } else {
              await container.markUnhealthy();
              log.warning(`Unhealthy container detected: ${workspace._id}`);
            }
          }
        } catch (error) {
          log.failure(`Error checking health for workspace ${workspace._id}:`, error);
        }
      }
    } catch (error) {
      log.failure('Error updating container health status:', error);
    }
  }

  /**
   * Cleanup orphaned containers (containers without workspace records)
   */
  private async cleanupOrphanedContainers(): Promise<void> {
    try {
      // Find containers that don't have corresponding workspace records
      const allContainers = await ECSContainer.find({
        status: { $in: ['running', 'starting', 'started'] }
      });

      for (const container of allContainers) {
        const workspace = await Workspace.findById(container.workspaceId);
        
        if (!workspace) {
          log.cleanup(`Found orphaned container: ${container._id}`);
          
          try {
            // Try to stop the ECS task if it exists
            if (container.taskArn) {
              await this.ecsWorkspaceService.stopWorkspace(container.workspaceId);
            }
            
            await container.addEvent('cleaned_up', 'Orphaned container cleaned up');
          } catch (error) {
            log.failure(`Error cleaning up orphaned container ${container._id}:`, error);
          }
        }
      }
    } catch (error) {
      log.failure('Error cleaning up orphaned containers:', error);
    }
  }

  /**
   * Periodic database sync for running workspaces
   */
  async runPeriodicDatabaseSync(): Promise<void> {
    try {
      log.sync('Starting periodic database sync...');

      // Get all running workspaces
      const runningWorkspaces = await Workspace.find({
        containerStatus: 'running',
        publicIp: { $exists: true, $ne: null }
      });

      log.verbose(`Found ${runningWorkspaces.length} running workspaces for sync`);

      let syncedCount = 0;
      let failedCount = 0;

      for (const workspace of runningWorkspaces) {
        try {
          // Check if workspace was recently accessed (within last 2 hours)
          const twoHoursAgo = new Date(Date.now() - 2 * 60 * 60 * 1000);
          if (workspace.lastAccessedAt < twoHoursAgo) {
            log.verbose(`Skipping inactive workspace: ${workspace._id}`);
            continue;
          }

          log.debug('[LIFETIME][SYSTEM] Periodic sync: skipping auto-extend during structure and file reads');

          // Get current file structure (avoid extending lifetime during background sync)
          const structure = await this.ecsFileSystemService.getWorkspaceStructure((workspace._id as any).toString(), { skipAutoExtend: true });

          // Get all files from the container
          const files: { [key: string]: string } = {};
          const filePaths = this.extractFilePathsFromStructure(structure);

          // Read each file's content (limit to prevent overload)
          const maxFiles = 50; // Limit to prevent overload
          const limitedFilePaths = filePaths.slice(0, maxFiles);

          for (const filePath of limitedFilePaths) {
            try {
              const fileData = await this.ecsFileSystemService.readFile((workspace._id as any).toString(), filePath, { skipAutoExtend: true });
              files[filePath] = fileData.content;
            } catch (fileError) {
              log.warning(`Failed to sync file ${filePath}:`, fileError);
            }
          }

          // Save to S3 (don't merge - this is a complete sync)
          if (workspace.s3FileStorage?.userEmail) {
            await workspace.saveFilesToS3(files, workspace.s3FileStorage.userEmail, false);
          } else {
            log.warning(`No S3 storage configured for workspace ${workspace._id}, skipping sync`);
          }

          log.debug(`Synced ${Object.keys(files).length} files for workspace: ${workspace._id}`);
          syncedCount++;

        } catch (error) {
          log.failure(`Failed to sync workspace ${workspace._id}:`, error);
          failedCount++;
        }
      }

      log.success(`Periodic sync completed: ${syncedCount} synced, ${failedCount} failed`);

    } catch (error) {
      log.failure('Error in periodic database sync:', error);
    }
  }

  /**
   * Extend workspace lifetime
   */
  async extendWorkspaceLifetime(workspaceId: string, hours?: number): Promise<boolean> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      if (!workspace) {
        return false;
      }

      const extendHours = hours ?? this.config.extendLifetimeHours;

      await workspace.extendLifetime(extendHours);

      const container = await ECSContainer.findOne({ workspaceId });
      if (container) {
        await container.extendLifetime(extendHours);
        await container.addEvent('started', `Lifetime extended by ${extendHours} hours`);
      }

      log.info(`[LIFETIME][USER] Extended workspace lifetime: ${workspaceId} (+${extendHours}h)`);
      return true;
    } catch (error) {
      log.failure(`Error extending workspace lifetime ${workspaceId}:`, error);
      return false;
    }
  }

  /**
   * Get workspace lifecycle info
   */
  async getWorkspaceLifecycleInfo(workspaceId: string): Promise<any> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      const container = await ECSContainer.findOne({ workspaceId });

      if (!workspace) {
        return null;
      }

      const now = new Date();
      const timeUntilExpiry = workspace.expiresAt.getTime() - now.getTime();
      const minutesUntilExpiry = Math.floor(timeUntilExpiry / (1000 * 60));

      return {
        workspaceId,
        status: workspace.containerStatus,
        createdAt: workspace.createdAt,
        expiresAt: workspace.expiresAt,
        lastAccessedAt: workspace.lastAccessedAt,
        minutesUntilExpiry,
        isExpired: workspace.isExpired(),
        shouldCleanup: workspace.shouldCleanup(),
        healthStatus: container?.healthStatus || 'unknown',
        events: container?.events || [],
        resourceUsage: container?.resourceUsage || []
      };
    } catch (error) {
      log.failure(`Error getting workspace lifecycle info ${workspaceId}:`, error);
      return null;
    }
  }

  /**
   * Force cleanup a specific workspace
   */
  async forceCleanupWorkspace(workspaceId: string): Promise<boolean> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      if (!workspace) {
        return false;
      }

      await this.processExpiredWorkspace(workspace);
      log.cleanup(`Force cleaned up workspace: ${workspaceId}`);
      return true;
    } catch (error) {
      log.failure(`Error force cleaning up workspace ${workspaceId}:`, error);
      return false;
    }
  }

  /**
   * Get overall lifecycle statistics
   */
  async getLifecycleStatistics(): Promise<LifecycleStats> {
    try {
      const totalWorkspaces = await Workspace.countDocuments({});
      const runningWorkspaces = await Workspace.countDocuments({
        containerStatus: { $in: ['running', 'starting', 'creating'] }
      });
      const expiredWorkspaces = await Workspace.countDocuments({
        expiresAt: { $lt: new Date() },
        containerStatus: { $in: ['running', 'starting', 'creating'] }
      });

      return {
        totalWorkspaces,
        runningWorkspaces,
        expiredWorkspaces,
        cleanedUpWorkspaces: 0, // This would be tracked over time
        failedCleanups: 0 // This would be tracked over time
      };
    } catch (error) {
      log.failure('Error getting lifecycle statistics:', error);
      return {
        totalWorkspaces: 0,
        runningWorkspaces: 0,
        expiredWorkspaces: 0,
        cleanedUpWorkspaces: 0,
        failedCleanups: 0
      };
    }
  }
}
