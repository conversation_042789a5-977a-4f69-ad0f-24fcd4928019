import { S3<PERSON>lient, PutO<PERSON><PERSON>ommand, GetO<PERSON><PERSON>ommand, DeleteObjectCommand, ListObjectsV2Command, HeadObjectCommand } from '@aws-sdk/client-s3';
import { getAWSConfig } from '../config/aws';
import { log } from '../utils/logger';

export interface S3FileInfo {
  key: string;
  size: number;
  lastModified: Date;
  etag: string;
}

export interface S3FileOperationResult {
  success: boolean;
  message: string;
  data?: any;
}

export class S3FileService {
  private s3Client: S3Client;
  private bucketName: string;

  constructor() {
    const awsConfig = getAWSConfig();
    this.s3Client = new S3Client({
      region: awsConfig.region,
      credentials: {
        accessKeyId: awsConfig.accessKeyId,
        secretAccessKey: awsConfig.secretAccessKey
      }
    });
    this.bucketName = 'mergen-code';
  }

  /**
   * Generate S3 key for workspace file
   */
  private generateS3Key(userEmail: string, workspaceName: string, filePath: string): string {
    // Validate userEmail is not empty
    if (!userEmail || userEmail.trim() === '') {
      throw new Error('User email cannot be empty for S3 key generation');
    }
    
    // Remove workspace_ prefix if present
    const cleanWorkspaceName = workspaceName.replace(/^workspace_/, '');
    // Ensure filePath doesn't start with /
    const cleanFilePath = filePath.startsWith('/') ? filePath.substring(1) : filePath;
    return `${userEmail}/${cleanWorkspaceName}/${cleanFilePath}`;
  }

  // Helper: concise AWS SDK error summary
  private summarizeAwsError(error: any): string {
    try {
      const type = error?.__type || error?.name || 'AWSServiceError';
      const fault = error?.$fault;
      const status = error?.$metadata?.httpStatusCode;
      const requestId = error?.$metadata?.requestId;
      const code = error?.code;
      const message = error?.message;
      const parts: string[] = [String(type)];
      if (code) parts.push(String(code));
      if (fault) parts.push(String(fault));
      if (typeof status !== 'undefined') parts.push(`HTTP ${status}`);
      if (requestId) parts.push(`reqId ${requestId}`);
      if (message) parts.push(String(message));
      return parts.join(' | ');
    } catch {
      return String(error?.message || error);
    }
  }

  /**
   * Upload a single file to S3
   */
  async uploadFile(userEmail: string, workspaceName: string, filePath: string, content: string): Promise<S3FileOperationResult> {
    try {
      const key = this.generateS3Key(userEmail, workspaceName, filePath);

      // Ensure non-stream body to avoid hashing issues in SDK
      const bodyBuffer = Buffer.from(content, 'utf-8');
      const contentLength = Buffer.byteLength(content, 'utf-8');
      
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: bodyBuffer,
        ContentType: this.getContentType(filePath),
        ContentLength: contentLength
      });

      await this.s3Client.send(command);

      return {
        success: true,
        message: `File uploaded successfully: ${filePath}`,
        data: { key, size: contentLength }
      };
    } catch (error: any) {
      log.failure('Error uploading file to S3:', this.summarizeAwsError(error));
      return {
        success: false,
        message: `Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Upload multiple files to S3
   */
  async uploadFiles(userEmail: string, workspaceName: string, files: { [filePath: string]: string }): Promise<S3FileOperationResult> {
    try {
      const uploadPromises = Object.entries(files).map(([filePath, content]) =>
        this.uploadFile(userEmail, workspaceName, filePath, content)
      );

      const results = await Promise.all(uploadPromises);
      const failedUploads = results
        .map((result, idx) => ({ result, filePath: Object.keys(files)[idx] }))
        .filter(x => !x.result.success);

      if (failedUploads.length > 0) {
        const failedCount = failedUploads.length;
        const failedList = failedUploads.slice(0, 5).map(x => x.filePath); // cap for readability
        log.warning(`Some files failed to upload to S3 (${failedCount} of ${results.length})`, failedList);
        return {
          success: false,
          message: `Failed to upload ${failedCount} files`,
          data: { failedCount, failedFiles: failedList }
        };
      }

      return {
        success: true,
        message: `Successfully uploaded ${Object.keys(files).length} files`,
        data: { uploadedCount: Object.keys(files).length }
      };
    } catch (error: any) {
      log.failure('Error uploading files to S3:', this.summarizeAwsError(error));
      return {
        success: false,
        message: `Failed to upload files: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Download a single file from S3
   */
  async downloadFile(userEmail: string, workspaceName: string, filePath: string): Promise<S3FileOperationResult> {
    try {
      const key = this.generateS3Key(userEmail, workspaceName, filePath);
      
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key
      });

      const response = await this.s3Client.send(command);
      const content = await response.Body?.transformToString();

      if (!content) {
        return {
          success: false,
          message: `File not found or empty: ${filePath}`
        };
      }

      return {
        success: true,
        message: `File downloaded successfully: ${filePath}`,
        data: {
          content,
          lastModified: response.LastModified,
          size: response.ContentLength
        }
      };
    } catch (error: any) {
      log.failure('Error downloading file from S3:', this.summarizeAwsError(error));
      return {
        success: false,
        message: `Failed to download file: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Download all files for a workspace
   */
  async downloadWorkspaceFiles(userEmail: string, workspaceName: string): Promise<S3FileOperationResult> {
    try {
      const cleanWorkspaceName = workspaceName.replace(/^workspace_/, '');
      const prefix = `${userEmail}/${cleanWorkspaceName}/`;

      const listCommand = new ListObjectsV2Command({
        Bucket: this.bucketName,
        Prefix: prefix
      });

      const listResponse = await this.s3Client.send(listCommand);
      
      if (!listResponse.Contents || listResponse.Contents.length === 0) {
        return {
          success: true,
          message: 'No files found in workspace',
          data: { files: {} }
        };
      }

      const files: { [filePath: string]: string } = {};
      
      for (const object of listResponse.Contents) {
        if (!object.Key) continue;
        
        // Extract relative file path
        const filePath = object.Key.replace(prefix, '');
        if (!filePath) continue; // Skip directory markers
        
        const downloadResult = await this.downloadFile(userEmail, workspaceName, filePath);
        if (downloadResult.success && downloadResult.data?.content) {
          files[filePath] = downloadResult.data.content;
        }
      }

      return {
        success: true,
        message: `Downloaded ${Object.keys(files).length} files`,
        data: { files }
      };
    } catch (error: any) {
      log.failure('Error downloading workspace files from S3:', this.summarizeAwsError(error));
      return {
        success: false,
        message: `Failed to download workspace files: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Delete a single file from S3
   */
  async deleteFile(userEmail: string, workspaceName: string, filePath: string): Promise<S3FileOperationResult> {
    try {
      const key = this.generateS3Key(userEmail, workspaceName, filePath);
      
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key
      });

      await this.s3Client.send(command);

      return {
        success: true,
        message: `File deleted successfully: ${filePath}`
      };
    } catch (error: any) {
      log.failure('Error deleting file from S3:', this.summarizeAwsError(error));
      return {
        success: false,
        message: `Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Delete all files for a workspace
   */
  async deleteWorkspaceFiles(userEmail: string, workspaceName: string): Promise<S3FileOperationResult> {
    try {
      const cleanWorkspaceName = workspaceName.replace(/^workspace_/, '');
      const prefix = `${userEmail}/${cleanWorkspaceName}/`;

      const listCommand = new ListObjectsV2Command({
        Bucket: this.bucketName,
        Prefix: prefix
      });

      const listResponse = await this.s3Client.send(listCommand);
      
      if (!listResponse.Contents || listResponse.Contents.length === 0) {
        return {
          success: true,
          message: 'No files to delete'
        };
      }

      const deletePromises = listResponse.Contents.map(object => {
        if (!object.Key) return Promise.resolve({ success: true, message: 'Skipped empty key' });
        
        const filePath = object.Key.replace(prefix, '');
        return this.deleteFile(userEmail, workspaceName, filePath);
      });

      const results = await Promise.all(deletePromises);
      const failedDeletes = results.filter(result => !result.success);

      if (failedDeletes.length > 0) {
        return {
          success: false,
          message: `Failed to delete ${failedDeletes.length} files`,
          data: { failedDeletes }
        };
      }

      return {
        success: true,
        message: `Successfully deleted ${results.length} files`
      };
    } catch (error: any) {
      log.failure('Error deleting workspace files from S3:', this.summarizeAwsError(error));
      return {
        success: false,
        message: `Failed to delete workspace files: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Check if file exists in S3
   */
  async fileExists(userEmail: string, workspaceName: string, filePath: string): Promise<boolean> {
    try {
      const key = this.generateS3Key(userEmail, workspaceName, filePath);
      
      const command = new HeadObjectCommand({
        Bucket: this.bucketName,
        Key: key
      });

      await this.s3Client.send(command);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * List files in workspace
   */
  async listWorkspaceFiles(userEmail: string, workspaceName: string): Promise<S3FileOperationResult> {
    try {
      const cleanWorkspaceName = workspaceName.replace(/^workspace_/, '');
      const prefix = `${userEmail}/${cleanWorkspaceName}/`;

      const command = new ListObjectsV2Command({
        Bucket: this.bucketName,
        Prefix: prefix
      });

      const response = await this.s3Client.send(command);
      
      if (!response.Contents) {
        return {
          success: true,
          message: 'No files found',
          data: { files: [] }
        };
      }

      const files: S3FileInfo[] = response.Contents
        .filter(object => object.Key && object.Key !== prefix) // Filter out directory markers
        .map(object => ({
          key: object.Key!.replace(prefix, ''), // Remove prefix to get relative path
          size: object.Size || 0,
          lastModified: object.LastModified || new Date(),
          etag: object.ETag || ''
        }));

      return {
        success: true,
        message: `Found ${files.length} files`,
        data: { files }
      };
    } catch (error: any) {
      log.failure('Error listing workspace files from S3:', this.summarizeAwsError(error));
      return {
        success: false,
        message: `Failed to list workspace files: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get content type based on file extension
   */
  private getContentType(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase();
    
    const contentTypes: { [key: string]: string } = {
      'js': 'application/javascript',
      'jsx': 'application/javascript',
      'ts': 'application/typescript',
      'tsx': 'application/typescript',
      'json': 'application/json',
      'html': 'text/html',
      'css': 'text/css',
      'md': 'text/markdown',
      'txt': 'text/plain',
      'py': 'text/x-python',
      'java': 'text/x-java-source',
      'cpp': 'text/x-c++src',
      'c': 'text/x-csrc',
      'php': 'text/x-php',
      'rb': 'text/x-ruby',
      'go': 'text/x-go',
      'rs': 'text/x-rust',
      'xml': 'application/xml',
      'yaml': 'application/x-yaml',
      'yml': 'application/x-yaml'
    };

    return contentTypes[extension || ''] || 'text/plain';
  }
}