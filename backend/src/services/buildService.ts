import { CodeExtractor } from '../utils/codeExtractor';
import { BuildResult, IBuildResult } from '../models/BuildResult';
import { InterviewConfig } from '../models/InterviewConfig';

interface ProcessLLMResponseRequest {
  interviewUuid?: string;
  userId?: string;
  rawLLMResponse: string;
  llmMetadata: {
    prompt: string;
    model: string;
    tokens?: number;
    timestamp: Date;
  };
}

interface ProcessLLMResponseResult {
  success: boolean;
  buildResult?: IBuildResult;
  error?: string;
}

/**
 * Service for processing LLM responses and storing build results
 */
export class BuildService {
  
  /**
   * Process LLM response, extract content, and store in database
   */
  static async processLLMResponse(request: ProcessLLMResponseRequest): Promise<ProcessLLMResponseResult> {
    try {
      console.log('🔥 DEBUG: BuildService.processLLMResponse called with:', {
        interviewUuid: request.interviewUuid,
        userId: request.userId,
        hasRawResponse: !!request.rawLLMResponse,
        responseLength: request.rawLLMResponse?.length
      });

      // Check if there's an existing BuildResult to preserve chat history
      let existingChatHistory: any[] = [];
      if (request.interviewUuid) {
        const existingBuildResult = await BuildResult.findOne({
          interviewUuid: { $eq: request.interviewUuid, $ne: null, $exists: true }
        }).sort({ createdAt: -1 });

        if (existingBuildResult && existingBuildResult.chatHistory) {
          existingChatHistory = existingBuildResult.chatHistory;
          console.log('💬 PRESERVE: Found existing chat history with', existingChatHistory.length, 'messages');
        }
      }

      // Create initial build result record
      const buildResult = new BuildResult({
        interviewUuid: request.interviewUuid,
        userId: request.userId,
        rawLLMResponse: request.rawLLMResponse,
        llmMetadata: request.llmMetadata,
        status: 'processing'
      });

      console.log('🔥 DEBUG: About to save initial BuildResult with status "processing"');
      await buildResult.save();
      console.log('✅ DEBUG: Initial BuildResult saved with UUID:', buildResult.uuid);

      try {
        // Extract content using CodeExtractor
        const extractedContent = CodeExtractor.extractFullContent(request.rawLLMResponse);

        // Update build result with extracted content
        buildResult.description = extractedContent.description || '';
        buildResult.codeBlocks = extractedContent.codeBlocks;
        buildResult.projectStructure = extractedContent.projectStructure;
        buildResult.deploymentInstructions = extractedContent.deploymentInstructions || '';
        buildResult.additionalSections = extractedContent.additionalSections || {};
        buildResult.chatHistory = existingChatHistory; // Preserve existing chat history
        buildResult.status = 'completed';

        console.log('💬 PRESERVE: Set chat history with', buildResult.chatHistory.length, 'messages');

        console.log('🔥 DEBUG: About to save final BuildResult with status "completed"');
        await buildResult.save();
        console.log('✅ DEBUG: Final BuildResult saved successfully with UUID:', buildResult.uuid);

        return {
          success: true,
          buildResult
        };

      } catch (extractionError: any) {
        // Update build result with error
        buildResult.status = 'failed';
        buildResult.errorMessage = `Content extraction failed: ${extractionError.message}`;
        await buildResult.save();

        return {
          success: false,
          error: `Content extraction failed: ${extractionError.message}`
        };
      }

    } catch (error: any) {
      console.error('Error processing LLM response:', error);
      return {
        success: false,
        error: `Database error: ${error.message}`
      };
    }
  }

  /**
   * Get build result by interview UUID
   */
  static async getBuildResultByInterview(interviewUuid: string): Promise<IBuildResult | null> {
    try {
      return await BuildResult.findOne({ interviewUuid }).sort({ createdAt: -1 });
    } catch (error) {
      console.error('Error fetching build result:', error);
      return null;
    }
  }

  /**
   * Get build result by UUID
   */
  static async getBuildResultByUuid(uuid: string): Promise<IBuildResult | null> {
    try {
      return await BuildResult.findOne({ uuid });
    } catch (error) {
      console.error('Error fetching build result:', error);
      return null;
    }
  }

  /**
   * Get build results by user ID
   */
  static async getBuildResultsByUser(userId: string, limit: number = 10): Promise<IBuildResult[]> {
    try {
      return await BuildResult.find({ userId })
        .sort({ createdAt: -1 })
        .limit(limit);
    } catch (error) {
      console.error('Error fetching user build results:', error);
      return [];
    }
  }

  /**
   * Delete build result
   */
  static async deleteBuildResult(uuid: string): Promise<boolean> {
    try {
      const result = await BuildResult.deleteOne({ uuid });
      return result.deletedCount > 0;
    } catch (error) {
      console.error('Error deleting build result:', error);
      return false;
    }
  }

  /**
   * Get build statistics
   */
  static async getBuildStatistics(userId?: string) {
    try {
      const filter = userId ? { userId } : {};
      
      const [total, completed, failed, processing] = await Promise.all([
        BuildResult.countDocuments(filter),
        BuildResult.countDocuments({ ...filter, status: 'completed' }),
        BuildResult.countDocuments({ ...filter, status: 'failed' }),
        BuildResult.countDocuments({ ...filter, status: 'processing' })
      ]);

      return {
        total,
        completed,
        failed,
        processing,
        successRate: total > 0 ? (completed / total) * 100 : 0
      };
    } catch (error) {
      console.error('Error fetching build statistics:', error);
      return {
        total: 0,
        completed: 0,
        failed: 0,
        processing: 0,
        successRate: 0
      };
    }
  }
}
