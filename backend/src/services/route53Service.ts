import { Route53Client, ChangeResourceRecordSetsCommand, ChangeBatch, ResourceRecordSet } from '@aws-sdk/client-route-53';
import { getAWSConfig } from '../config/aws';

export class Route53PreviewDNSService {
  private client: Route53Client;
  private hostedZoneId?: string;
  private previewBaseDomain?: string;

  constructor() {
    const config = getAWSConfig();
    this.hostedZoneId = (config as any).route53HostedZoneId;
    this.previewBaseDomain = (config as any).previewBaseDomain;

    this.client = new Route53Client({
      region: config.region,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey
      }
    });
  }

  isEnabled(): boolean {
    return Boolean(this.hostedZoneId && this.previewBaseDomain);
  }

  getRecordName(interviewUuid: string): string {
    if (!this.previewBaseDomain) {
      throw new Error('previewBaseDomain not configured');
    }
    return `preview--${interviewUuid}.${this.previewBaseDomain}`;
  }

  async upsertARecord(recordName: string, ipAddress: string, ttlSeconds: number = 60): Promise<void> {
    if (!this.hostedZoneId) {
      throw new Error('Route53 hosted zone id not configured');
    }

    const recordSet: ResourceRecordSet = {
      Name: recordName,
      Type: 'A',
      TTL: ttlSeconds,
      ResourceRecords: [{ Value: ipAddress }]
    };

    const changeBatch: ChangeBatch = {
      Changes: [
        {
          Action: 'UPSERT',
          ResourceRecordSet: recordSet
        }
      ]
    };

    const command = new ChangeResourceRecordSetsCommand({
      HostedZoneId: this.hostedZoneId,
      ChangeBatch: changeBatch
    });

    await this.client.send(command);
  }

  async deleteARecord(recordName: string, ipAddress: string, ttlSeconds: number = 60): Promise<void> {
    if (!this.hostedZoneId) {
      throw new Error('Route53 hosted zone id not configured');
    }

    const recordSet: ResourceRecordSet = {
      Name: recordName,
      Type: 'A',
      TTL: ttlSeconds,
      ResourceRecords: [{ Value: ipAddress }]
    };

    const command = new ChangeResourceRecordSetsCommand({
      HostedZoneId: this.hostedZoneId,
      ChangeBatch: {
        Changes: [
          {
            Action: 'DELETE',
            ResourceRecordSet: recordSet
          }
        ]
      }
    });

    try {
      await this.client.send(command);
    } catch (err) {
      // Best-effort cleanup
      console.warn('Route53 delete failed (may not exist):', (err as any)?.message || err);
    }
  }
}

export class Route53LiveDNSService {
  private client: Route53Client;
  private hostedZoneId?: string;
  private liveBaseDomain?: string;

  constructor() {
    const config = getAWSConfig();
    this.hostedZoneId = (config as any).route53HostedZoneId;
    this.liveBaseDomain = (config as any).liveBaseDomain || (config as any).previewBaseDomain; // fallback if same domain

    this.client = new Route53Client({
      region: config.region,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey
      }
    });
  }

  isEnabled(): boolean {
    return Boolean(this.hostedZoneId && this.liveBaseDomain);
  }

  getRecordName(interviewUuid: string): string {
    if (!this.liveBaseDomain) {
      throw new Error('liveBaseDomain not configured');
    }
    return `live-${interviewUuid}.${this.liveBaseDomain}`;
  }

  async upsertARecord(recordName: string, ipAddress: string, ttlSeconds: number = 60): Promise<void> {
    if (!this.hostedZoneId) {
      throw new Error('Route53 hosted zone id not configured');
    }

    const recordSet: ResourceRecordSet = {
      Name: recordName,
      Type: 'A',
      TTL: ttlSeconds,
      ResourceRecords: [{ Value: ipAddress }]
    };

    const changeBatch: ChangeBatch = {
      Changes: [
        {
          Action: 'UPSERT',
          ResourceRecordSet: recordSet
        }
      ]
    };

    const command = new ChangeResourceRecordSetsCommand({
      HostedZoneId: this.hostedZoneId,
      ChangeBatch: changeBatch
    });

    await this.client.send(command);
  }

  async deleteARecord(recordName: string, ipAddress: string, ttlSeconds: number = 60): Promise<void> {
    if (!this.hostedZoneId) {
      throw new Error('Route53 hosted zone id not configured');
    }

    const recordSet: ResourceRecordSet = {
      Name: recordName,
      Type: 'A',
      TTL: ttlSeconds,
      ResourceRecords: [{ Value: ipAddress }]
    };

    const command = new ChangeResourceRecordSetsCommand({
      HostedZoneId: this.hostedZoneId,
      ChangeBatch: {
        Changes: [
          {
            Action: 'DELETE',
            ResourceRecordSet: recordSet
          }
        ]
      }
    });

    try {
      await this.client.send(command);
    } catch (err) {
      console.warn('Route53 live delete failed (may not exist):', (err as any)?.message || err);
    }
  }
} 