import { <PERSON><PERSON><PERSON>, CreateServiceCommand, DeleteServiceCommand, DescribeServicesCommand, DescribeTasksCommand, RegisterTaskDefinitionCommand, StopTaskCommand, ListTasksCommand, NetworkMode, LaunchType } from '@aws-sdk/client-ecs';
import { EC2Client, DescribeNetworkInterfacesCommand } from '@aws-sdk/client-ec2';
import { Workspace, IWorkspace, ECSContainerStatus } from '../models/Workspace';
import { ECSContainer, IECSContainer } from '../models/ECSContainer';
import { getAWSConfig, getDefaultTaskDefinition, getDefaultServiceConfig } from '../config/aws';
import { getAppConfig } from '../config/app';
import { v4 as uuidv4 } from 'uuid';
import { log } from '../utils/logger';

export interface CreateWorkspaceRequest {
  userId: string;
  userEmail: string;
  interviewUuid: string;
  files?: { [filePath: string]: string };
  lifetimeHours?: number;
}

export interface WorkspaceInfo {
  workspaceId: string;
  containerId: string;
  status: ECSContainerStatus;
  previewUrl?: string;
  publicIp?: string;
  expiresAt: Date;
}

export class ECSWorkspaceService {
  private ecsClient: ECSClient;
  private ec2Client: EC2Client;
  private config: ReturnType<typeof getAWSConfig>;
  private workspaceCreationLocks: Map<string, Promise<WorkspaceInfo | null>> = new Map();

  constructor() {
    this.config = getAWSConfig();
    this.ecsClient = new ECSClient({
      region: this.config.region,
      credentials: {
        accessKeyId: this.config.accessKeyId,
        secretAccessKey: this.config.secretAccessKey
      }
    });
    this.ec2Client = new EC2Client({
      region: this.config.region,
      credentials: {
        accessKeyId: this.config.accessKeyId,
        secretAccessKey: this.config.secretAccessKey
      }
    });
  }

  // Helper: concise AWS SDK error summary
  private summarizeAwsError(error: any): string {
    try {
      const type = error?.__type || error?.name || 'AWSServiceError';
      const fault = error?.$fault;
      const status = error?.$metadata?.httpStatusCode;
      const requestId = error?.$metadata?.requestId;
      const code = error?.code;
      const message = error?.message;
      const parts: string[] = [String(type)];
      if (code) parts.push(String(code));
      if (fault) parts.push(String(fault));
      if (typeof status !== 'undefined') parts.push(`HTTP ${status}`);
      if (requestId) parts.push(`reqId ${requestId}`);
      if (message) parts.push(String(message));
      return parts.join(' | ');
    } catch {
      return String(error?.message || error);
    }
  }

  /**
   * Create a new ECS workspace
   */
  async createWorkspace(request: CreateWorkspaceRequest): Promise<WorkspaceInfo> {
    try {
      // Validate userEmail is provided
      if (!request.userEmail || request.userEmail.trim() === '') {
        throw new Error('User email is required for workspace creation');
      }
      
      console.log('🚀 Creating ECS workspace for user:', request.userId, 'interview:', request.interviewUuid);

      // Check if workspace already exists
      const existingWorkspace = await Workspace.findOne({
        userId: request.userId,
        interviewUuid: request.interviewUuid
      });

      if (existingWorkspace && !existingWorkspace.isExpired()) {
        console.log('📦 Existing workspace found, not extending lifetime');
        
        return {
          workspaceId: (existingWorkspace._id as any).toString(),
          containerId: existingWorkspace.ecsTaskArn || '',
          status: existingWorkspace.containerStatus,
          previewUrl: existingWorkspace.previewUrl,
          publicIp: existingWorkspace.publicIp,
          expiresAt: existingWorkspace.expiresAt
        };
      }

      // Create new workspace
      const workspaceId = uuidv4();
      const short = request.interviewUuid.substring(0, 8);
      const serviceName = `workspace-${short}-${Date.now().toString().slice(-5)}`;
      const lifetimeHours = request.lifetimeHours || this.config.defaultLifetimeHours;

      // Register task definition
      const taskDefinitionArn = await this.registerTaskDefinition(workspaceId, request);

      // Create workspace record
      const workspace = new Workspace({
        userId: request.userId,
        interviewUuid: request.interviewUuid,
        ecsClusterName: this.config.ecsClusterName,
        ecsTaskDefinitionArn: taskDefinitionArn,
        containerStatus: 'creating',
        containerConfig: {
          taskDefinitionArn,
          clusterName: this.config.ecsClusterName,
          serviceName,
          containerName: 'workspace-container',
          cpu: this.config.containerCpu,
          memory: this.config.containerMemory,
          port: this.config.containerPort
        },
        expiresAt: new Date(Date.now() + lifetimeHours * 60 * 60 * 1000),
        lastAccessedAt: new Date(),
        autoCleanup: true
      });

      // Initialize S3 storage metadata for this workspace so future saves can sync
      const cleanWorkspaceName = request.interviewUuid.replace(/^workspace_/, '');
      workspace.s3FileStorage = {
        userEmail: request.userEmail,
        s3Prefix: `${request.userEmail}/${cleanWorkspaceName}/`,
        lastSavedAt: new Date(),
        version: 0,
        fileCount: 0,
        totalSize: 0,
        initialFilesUploaded: false
      } as any;

      // Save initial files if provided
      if (request.files && Object.keys(request.files).length > 0) {
        await workspace.saveFilesToS3(request.files, request.userEmail, false);
      }

      await workspace.save();

      // Create ECS container record
      const container = new ECSContainer({
        workspaceId: (workspace._id as any).toString(),
        userId: request.userId,
        interviewUuid: request.interviewUuid,
        clusterName: this.config.ecsClusterName,
        taskDefinitionArn,
        containerName: 'workspace-container',
        port: this.config.containerPort,
        cpu: this.config.containerCpu,
        memory: this.config.containerMemory,
        expiresAt: workspace.expiresAt
      });

      await container.addEvent('created', 'Workspace container created');

      // Create ECS service
      const serviceArn = await this.createECSService(serviceName, taskDefinitionArn);
      
      workspace.ecsServiceArn = serviceArn;
      container.serviceArn = serviceArn;
      
      await workspace.save();
      await container.save();

      // Start monitoring the service
      this.monitorServiceStartup((workspace._id as any).toString(), (container._id as any).toString());

      return {
        workspaceId: (workspace._id as any).toString(),
        containerId: (container._id as any).toString(),
        status: 'creating',
        expiresAt: workspace.expiresAt
      };

    } catch (error: any) {
      console.error('❌ Error creating ECS workspace:', error);
      throw new Error(`Failed to create workspace: ${error.message}`);
    }
  }

  /**
   * Register ECS task definition
   */
  private async registerTaskDefinition(workspaceId: string, request: CreateWorkspaceRequest): Promise<string> {
    const taskDef = getDefaultTaskDefinition(this.config);
    
    // Add workspace-specific environment variables
    if (taskDef.containerDefinitions && taskDef.containerDefinitions[0] && taskDef.containerDefinitions[0].environment) {
      taskDef.containerDefinitions[0].environment.push(
        { name: 'WORKSPACE_ID', value: workspaceId },
        { name: 'USER_ID', value: request.userId },
        { name: 'INTERVIEW_UUID', value: request.interviewUuid }
      );
    }

    const command = new RegisterTaskDefinitionCommand(taskDef);
    const response = await this.ecsClient.send(command);
    
    if (!response.taskDefinition?.taskDefinitionArn) {
      throw new Error('Failed to register task definition');
    }

    console.log('✅ Task definition registered:', response.taskDefinition.taskDefinitionArn);
    return response.taskDefinition.taskDefinitionArn;
  }

  /**
   * Create ECS service
   */
  private async createECSService(serviceName: string, taskDefinitionArn: string): Promise<string> {
    const serviceConfig = getDefaultServiceConfig(this.config, taskDefinitionArn, serviceName);

    console.log('🔧 ECS Service Configuration:');
    console.log('   Subnets:', this.config.subnetIds);
    console.log('   Security Groups:', [this.config.securityGroupId]);
    console.log('   Assign Public IP:', serviceConfig.networkConfiguration?.awsvpcConfiguration?.assignPublicIp);

    const command = new CreateServiceCommand(serviceConfig);
    const response = await this.ecsClient.send(command);

    if (!response.service?.serviceArn) {
      throw new Error('Failed to create ECS service');
    }

    console.log('✅ ECS service created:', response.service.serviceArn);
    return response.service.serviceArn;
  }

  /**
   * Monitor service startup and update workspace status
   */
  private async monitorServiceStartup(workspaceId: string, containerId: string): Promise<void> {
    // Monitor multiple times to catch the transition from pending to running
    const config = getAppConfig();
    const checkIntervals = config.workspaceCheckIntervalsMs;

    for (const interval of checkIntervals) {
      setTimeout(async () => {
        try {
          log.debug(`Checking workspace status after ${interval/1000}s...`);
          await this.updateWorkspaceStatus(workspaceId, containerId);
        } catch (error) {
          log.failure('Error monitoring service startup:', error);
        }
      }, interval);
    }
  }

  /**
   * Update workspace status by checking ECS service
   */
  async updateWorkspaceStatus(workspaceId: string, containerId: string): Promise<void> {
    try {
      log.debug('Updating workspace status for:', workspaceId);

      const workspace = await Workspace.findById(workspaceId);
      const container = await ECSContainer.findById(containerId);

      if (!workspace || !container) {
        log.failure('Workspace or container not found');
        return;
      }

      if (!workspace.ecsServiceArn) {
        log.failure('No ECS service ARN found');
        return;
      }

      log.verbose('Checking ECS service:', workspace.ecsServiceArn);

      // Check service status
      const describeCommand = new DescribeServicesCommand({
        cluster: this.config.ecsClusterName,
        services: [workspace.ecsServiceArn]
      });

      const serviceResponse = await this.ecsClient.send(describeCommand);
      const service = serviceResponse.services?.[0];

      log.verbose('Service status:', service?.status);
      log.verbose('Service running count:', service?.runningCount);
      log.verbose('Service desired count:', service?.desiredCount);

      if (!service) {
        await container.addEvent('failed', 'ECS service not found');
        workspace.containerStatus = 'failed';
        await workspace.save();
        return;
      }

      // Get running tasks
      const listTasksCommand = new ListTasksCommand({
        cluster: this.config.ecsClusterName,
        serviceName: workspace.ecsServiceArn
      });

      const tasksResponse = await this.ecsClient.send(listTasksCommand);
      log.verbose('Found tasks:', tasksResponse.taskArns?.length || 0);

      if (tasksResponse.taskArns && tasksResponse.taskArns.length > 0) {
        const taskArn = tasksResponse.taskArns[0];
        workspace.ecsTaskArn = taskArn;
        const containerDoc = container;
        containerDoc.taskArn = taskArn;

        // Get task details
        const describeTasksCommand = new DescribeTasksCommand({
          cluster: this.config.ecsClusterName,
          tasks: [taskArn]
        });

        const taskResponse = await this.ecsClient.send(describeTasksCommand);
        const task = taskResponse.tasks?.[0];

        if (task) {
          const taskStatus = task.lastStatus?.toLowerCase();
          log.debug('Task status:', taskStatus);
          log.debug('Task health status:', task.healthStatus);

          if (taskStatus === 'running') {
            log.info('Task is running, updating network info...');
            await this.updateNetworkInfo(workspace, containerDoc, task);
            workspace.containerStatus = 'running';
            await containerDoc.addEvent('started', 'Container is running');
          } else if (taskStatus === 'stopped') {
            log.info('Task is stopped');
            workspace.containerStatus = 'stopped';
            await containerDoc.addEvent('stopped', 'Container stopped');
          } else {
            log.info('Task is starting, status:', taskStatus);
            workspace.containerStatus = 'starting';
            await containerDoc.addEvent('starting', `Container status: ${taskStatus}`);
          }
        } else {
          log.failure('No task found in response');
        }

        // Debug what we're about to save
        log.debug('About to save workspace with:', {
          containerStatus: workspace.containerStatus,
          publicIp: workspace.publicIp,
          privateIp: workspace.privateIp,
          previewUrl: workspace.previewUrl
        });

        await workspace.save();
        await containerDoc.save();
        log.success('Workspace and container saved to database');

        // Upload initial files if container just became running and has files in S3
        if (workspace.containerStatus === 'running' &&
            workspace.s3FileStorage &&
            !workspace.s3FileStorage?.initialFilesUploaded) {
          await this.uploadInitialFilesToContainer(workspace);
        }
      } else {
        log.failure('No tasks found for service');
      }

    } catch (error: any) {
      log.failure('Error updating workspace status:', this.summarizeAwsError(error));
      log.failure('Error details:', error);
    }
  }

  /**
   * Upload initial files from S3 to running container
   */
  private async uploadInitialFilesToContainer(workspace: IWorkspace): Promise<void> {
    try {
      if (!workspace.s3FileStorage) {
        log.workspace('No S3 storage configured for workspace:', workspace._id as any);
        return;
      }

      // Import services
      const { ECSFileSystemService } = await import('./ecsFileSystemService');
      const { S3FileService } = await import('./s3FileService');
      
      const ecsFileSystemService = new ECSFileSystemService();
      const s3Service = new S3FileService();

      // Download files from S3
      const s3FilesResult = await s3Service.downloadWorkspaceFiles(
        workspace.s3FileStorage.userEmail,
        workspace.interviewUuid
      );

      if (!s3FilesResult.success || !s3FilesResult.data?.files || Object.keys(s3FilesResult.data.files).length === 0) {
        log.workspace('No initial files to upload for workspace:', workspace._id as any);
        return;
      }

      log.sync('Uploading initial files to running container for workspace:', workspace._id as any);
      log.debug('Files to upload:', Object.keys(s3FilesResult.data.files));

      // Upload files to container
      const result = await ecsFileSystemService.saveFiles(
        (workspace._id as any).toString(),
        s3FilesResult.data.files
      );

      if (result.success) {
        log.success(`Successfully uploaded ${Object.keys(s3FilesResult.data.files).length} initial files to container`);

        // Mark that initial files have been uploaded to avoid re-uploading
        workspace.s3FileStorage.initialFilesUploaded = true;
        workspace.s3FileStorage.lastSavedAt = new Date();
        await workspace.save();
      } else {
        log.failure('Failed to upload initial files to container:', result.message);
      }
    } catch (error: any) {
      log.failure('Error uploading initial files to container:', this.summarizeAwsError(error), error);
    }
  }

  /**
   * Update network information for running container
   */
  private async updateNetworkInfo(workspace: IWorkspace, container: IECSContainer, task: any): Promise<void> {
    try {
      log.debug('Updating network info for workspace:', workspace._id as any);
      log.verbose('Full task object:', JSON.stringify(task, null, 2));

      // Try multiple ways to find network interface ID
      let networkInterfaceId: string | null = null;

      // Method 1: Check task attachments
      if (task.attachments && task.attachments.length > 0) {
        for (const attachment of task.attachments) {
          if (attachment.type === 'ElasticNetworkInterface' && attachment.details) {
            const eniDetail = attachment.details.find((detail: any) => detail.name === 'networkInterfaceId');
            if (eniDetail) {
              networkInterfaceId = eniDetail.value;
              log.debug('Found ENI via attachments:', networkInterfaceId);
              break;
            }
          }
        }
      }

      // Method 2: Check task containers network interfaces (alternative structure)
      if (!networkInterfaceId && task.containers) {
        for (const containerInfo of task.containers) {
          if (containerInfo.networkInterfaces && containerInfo.networkInterfaces.length > 0) {
            networkInterfaceId = containerInfo.networkInterfaces[0].networkInterfaceId;
            log.debug('Found ENI via container network interfaces:', networkInterfaceId);
            break;
          }
        }
      }

      log.debug('Final network interface ID:', networkInterfaceId);

      if (networkInterfaceId) {
        log.debug('Using Network Interface ID:', networkInterfaceId);

        const describeNICommand = new DescribeNetworkInterfacesCommand({
          NetworkInterfaceIds: [networkInterfaceId]
        });

        const niResponse = await this.ec2Client.send(describeNICommand);
        const networkInterface = niResponse.NetworkInterfaces?.[0];

        log.debug('Network Interface Details:', JSON.stringify({
          publicIp: networkInterface?.Association?.PublicIp,
          privateIp: networkInterface?.PrivateIpAddress,
          state: networkInterface?.Status,
          association: networkInterface?.Association
        }, null, 2));

        if (networkInterface) {
          const newPublicIp = networkInterface.Association?.PublicIp;
          workspace.publicIp = newPublicIp;
          workspace.privateIp = networkInterface.PrivateIpAddress;

          // If Route53 is configured, map preview hostname to public IP
          const { Route53PreviewDNSService } = await import('./route53Service');
          const dnsService = new Route53PreviewDNSService();
          let previewHostname: string | undefined = undefined;

          if (newPublicIp && dnsService.isEnabled()) {
            const recordName = dnsService.getRecordName(workspace.interviewUuid);

            // If IP changed, attempt to delete old A record
            if (workspace.previewHostname && workspace.previewHostname === recordName && container.publicIp && container.publicIp !== newPublicIp) {
              try {
                await dnsService.deleteARecord(recordName, container.publicIp);
              } catch {}
            }

            await dnsService.upsertARecord(recordName, newPublicIp);
            previewHostname = recordName;
          }

          if (newPublicIp) {
            const baseUrl = previewHostname ? `http://${previewHostname}:${this.config.previewPort}` : `http://${newPublicIp}:${this.config.previewPort}`;
            workspace.previewUrl = baseUrl;
            workspace.previewHostname = previewHostname;
            container.previewUrl = baseUrl;
            container.previewHostname = previewHostname;
            container.publicIp = newPublicIp;
            container.privateIp = workspace.privateIp;

            log.success('Network info updated successfully:', {
              publicIp: workspace.publicIp,
              privateIp: workspace.privateIp,
              previewUrl: workspace.previewUrl
            });
          } else {
            log.warning('No public IP assigned to network interface');
            log.warning('This usually means:');
            log.warning('  - Subnet is not a public subnet');
            log.warning('  - assignPublicIp is not enabled');
            log.warning('  - No available Elastic IPs');
          }
        } else {
          log.failure('Network interface not found in EC2 response');
        }
      } else {
        log.failure('No network interface ID found in task');
        log.failure('This indicates a problem with ECS task networking setup');
      }
    } catch (error) {
      log.failure('Error updating network info:', this.summarizeAwsError(error), error as any);
    }
  }

  /**
   * Stop workspace
   */
  async stopWorkspace(workspaceId: string): Promise<void> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      if (!workspace) {
        throw new Error('Workspace not found');
      }

      const container = await ECSContainer.findOne({ workspaceId });
      
      if (workspace.ecsServiceArn) {
        // Delete ECS service
        const deleteCommand = new DeleteServiceCommand({
          cluster: this.config.ecsClusterName,
          service: workspace.ecsServiceArn,
          force: true
        });

        await this.ecsClient.send(deleteCommand);
        log.success('ECS service deleted');
      }

      if (workspace.ecsTaskArn) {
        // Stop task
        const stopCommand = new StopTaskCommand({
          cluster: this.config.ecsClusterName,
          task: workspace.ecsTaskArn,
          reason: 'Workspace stopped by user'
        });

        await this.ecsClient.send(stopCommand);
        log.success('ECS task stopped');
      }

      // Cleanup DNS record if it exists
      if (workspace.previewHostname && workspace.publicIp) {
        try {
          const { Route53PreviewDNSService } = await import('./route53Service');
          const dnsService = new Route53PreviewDNSService();
          if (dnsService.isEnabled()) {
            await dnsService.deleteARecord(workspace.previewHostname, workspace.publicIp);
          }
        } catch (err) {
          log.warning('DNS cleanup failed:', (err as any)?.message || err);
        }
      }

      workspace.containerStatus = 'stopped';
      await workspace.save();

      if (container) {
        await container.addEvent('stopped', 'Workspace stopped by user');
      }

    } catch (error: any) {
      log.failure('Error stopping workspace:', this.summarizeAwsError(error));
      throw new Error(`Failed to stop workspace: ${error.message}`);
    }
  }

  /**
   * Get workspace info by workspace ID
   */
  async getWorkspaceInfo(workspaceId: string): Promise<WorkspaceInfo | null> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      if (!workspace) {
        return null;
      }

      const container = await ECSContainer.findOne({ workspaceId });

      return {
        workspaceId: (workspace._id as any).toString(),
        containerId: container ? (container._id as any).toString() : '',
        status: workspace.containerStatus,
        previewUrl: workspace.previewUrl,
        publicIp: workspace.publicIp,
        expiresAt: workspace.expiresAt
      };
    } catch (error: any) {
      log.failure('Error getting workspace info:', this.summarizeAwsError(error));
      return null;
    }
  }

  /**
   * Get workspace by workspace ID
   */
  async getWorkspaceById(workspaceId: string): Promise<any | null> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      return workspace;
    } catch (error: any) {
      log.failure('Error getting workspace by ID:', error);
      return null;
    }
  }

  /**
   * Get or create workspace with validation and restoration
   * This method ensures the workspace is actually running and creates a new one if needed
   */
  async getOrCreateWorkspace(workspaceName: string, userId: string, userEmail: string, lifetimeHours?: number): Promise<WorkspaceInfo | null> {
    // Create a unique key for this workspace request
    const lockKey = `${userId}:${workspaceName}`;

    // Check if there's already a creation in progress for this workspace
    if (this.workspaceCreationLocks.has(lockKey)) {
      log.debug('Workspace creation already in progress, waiting for completion:', lockKey);
      return await this.workspaceCreationLocks.get(lockKey)!;
    }

    // Create a new promise for this workspace creation
    const creationPromise = this.performGetOrCreateWorkspace(workspaceName, userId, userEmail, lifetimeHours);
    this.workspaceCreationLocks.set(lockKey, creationPromise);

    try {
      const result = await creationPromise;
      return result;
    } finally {
      // Clean up the lock after completion
      this.workspaceCreationLocks.delete(lockKey);
    }
  }

  /**
   * Internal method that performs the actual get or create logic
   */
  private async performGetOrCreateWorkspace(workspaceName: string, userId: string, userEmail: string, lifetimeHours?: number): Promise<WorkspaceInfo | null> {
    try {
      // Extract interview UUID from workspace name
      const interviewUuid = workspaceName.replace('workspace_', '');
      if (!interviewUuid) {
        log.failure('Invalid workspace name format:', workspaceName);
        return null;
      }

      log.debug('Getting or creating workspace for user:', userId, 'interview:', interviewUuid);

      // First, check if workspace exists in database (with a more specific query to avoid race conditions)
      const existingWorkspace = await Workspace.findOne({
        userId,
        interviewUuid,
        // Only consider workspaces that are not in failed state
        containerStatus: { $ne: 'failed' }
      }).sort({ createdAt: -1 }); // Get the most recent one

      if (existingWorkspace) {
        log.debug('Found existing workspace:', existingWorkspace._id, 'status:', existingWorkspace.containerStatus);

        // Check if workspace was created recently (within last 5 minutes)
        const createdAt = existingWorkspace.createdAt || new Date(0);
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        const isRecentlyCreated = createdAt > fiveMinutesAgo;

        // If workspace is recently created and still starting up, be patient
        if (isRecentlyCreated && ['creating', 'starting'].includes(existingWorkspace.containerStatus)) {
          log.debug('Workspace is recently created and still starting up, returning existing workspace');
          // Update last accessed time
          await existingWorkspace.updateLastAccessed();

          const container = await ECSContainer.findOne({ workspaceId: (existingWorkspace._id as any).toString() });

          return {
            workspaceId: (existingWorkspace._id as any).toString(),
            containerId: container ? (container._id as any).toString() : '',
            status: existingWorkspace.containerStatus,
            previewUrl: existingWorkspace.previewUrl,
            publicIp: existingWorkspace.publicIp,
            expiresAt: existingWorkspace.expiresAt
          };
        }

        // For older workspaces or those marked as running, verify they're actually running
        const isActuallyRunning = await this.verifyWorkspaceIsRunning(existingWorkspace);

        if (isActuallyRunning) {
          log.success('Existing workspace is running, returning workspace info');
          // Update last accessed time
          await existingWorkspace.updateLastAccessed();

          const container = await ECSContainer.findOne({ workspaceId: (existingWorkspace._id as any).toString() });

          return {
            workspaceId: (existingWorkspace._id as any).toString(),
            containerId: container ? (container._id as any).toString() : '',
            status: existingWorkspace.containerStatus,
            previewUrl: existingWorkspace.previewUrl,
            publicIp: existingWorkspace.publicIp,
            expiresAt: existingWorkspace.expiresAt
          };
        }

        log.warning('Existing workspace not running, creating a new one');
      }

      // Determine lifetime to use
      const lifetimeToUse = lifetimeHours ?? this.config.defaultLifetimeHours;

      // Create a new workspace if none exists or the existing one is not running
      const createRequest: CreateWorkspaceRequest = {
        userId,
        userEmail,
        interviewUuid,
        lifetimeHours: lifetimeToUse
      };

      const created = await this.createWorkspace(createRequest);
      return created;

    } catch (error) {
      log.failure('Error getting or creating workspace:', this.summarizeAwsError(error), error as any);
      return null;
    }
  }

  /**
   * Get workspace info by workspace name (format: workspace_${interviewUuid})
   * This method checks if the workspace exists and if the container is actually running
   */
  async getWorkspaceInfoByName(workspaceName: string, userId: string, userEmail?: string): Promise<WorkspaceInfo | null> {
    try {
      // Extract interview UUID from workspace name
      const interviewUuid = workspaceName.replace('workspace_', '');
      if (!interviewUuid) {
        log.failure('Invalid workspace name format:', workspaceName);
        return null;
      }

      log.debug('Looking for workspace with userId:', userId, 'interviewUuid:', interviewUuid);

      const workspace = await Workspace.findOne({
        userId,
        interviewUuid,
        // Only consider workspaces that are not in failed state
        containerStatus: { $ne: 'failed' }
      }).sort({ createdAt: -1 }); // Get the most recent one

      if (workspace) {
        log.debug('Found existing workspace:', workspace._id, 'status:', workspace.containerStatus);

        // Check if workspace was created recently (within last 5 minutes)
        const createdAt = workspace.createdAt || new Date(0);
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        const isRecentlyCreated = createdAt > fiveMinutesAgo;

        // If workspace is recently created and still starting up, be patient
        if (isRecentlyCreated && ['creating', 'starting'].includes(workspace.containerStatus)) {
          log.debug('Workspace is recently created and still starting up, returning existing workspace');
          // Update last accessed time
          await workspace.updateLastAccessed();

          const container = await ECSContainer.findOne({ workspaceId: (workspace._id as any).toString() });

          return {
            workspaceId: (workspace._id as any).toString(),
            containerId: container ? (container._id as any).toString() : '',
            status: workspace.containerStatus,
            previewUrl: workspace.previewUrl,
            publicIp: workspace.publicIp,
            expiresAt: workspace.expiresAt
          };
        }

        // For older workspaces or those marked as running, verify they're actually running
        const isActuallyRunning = await this.verifyWorkspaceIsRunning(workspace);

        if (isActuallyRunning) {
          log.success('Existing workspace is running, returning workspace info');
          // Update last accessed time
          await workspace.updateLastAccessed();

          const container = await ECSContainer.findOne({ workspaceId: (workspace._id as any).toString() });

          return {
            workspaceId: (workspace._id as any).toString(),
            containerId: container ? (container._id as any).toString() : '',
            status: workspace.containerStatus,
            previewUrl: workspace.previewUrl,
            publicIp: workspace.publicIp,
            expiresAt: workspace.expiresAt
          };
        } else {
          log.warning('Existing workspace is not running, creating new one with backup files');

          // Get backup files from S3 if available
          let backupFiles: { [filePath: string]: string } = {};
          if (workspace.s3FileStorage) {
            const { S3FileService } = await import('./s3FileService');
            const s3Service = new S3FileService();
            const s3FilesResult = await s3Service.downloadWorkspaceFiles(
              workspace.s3FileStorage.userEmail,
              workspace.interviewUuid
            );
            if (s3FilesResult.success && s3FilesResult.data?.files) {
              backupFiles = s3FilesResult.data.files;
            }
          }

          // Delete the old workspace record
          await Workspace.deleteOne({ _id: workspace._id });
          await ECSContainer.deleteMany({ workspaceId: (workspace._id as any).toString() });

          // Get user email if not provided
          let finalUserEmail = userEmail;
          if (!finalUserEmail) {
            const { User } = await import('../models/User');
            const user = await User.findById(userId);
            finalUserEmail = user?.email || 'unknown@unknown';
          }

          // Create new workspace with backup files
          const newWorkspaceResult = await this.createWorkspace({
            userId,
            userEmail: finalUserEmail,
            interviewUuid,
            lifetimeHours: this.config.defaultLifetimeHours,
            files: backupFiles
          });

          log.success('Created new workspace with restored files:', newWorkspaceResult.workspaceId);
          return newWorkspaceResult;
        }
      } else {
        log.debug('No existing workspace found, creating new one');

        // Get user email if not provided
        let finalUserEmail = userEmail;
        if (!finalUserEmail) {
          const { User } = await import('../models/User');
          const user = await User.findById(userId);
          finalUserEmail = user?.email || 'unknown@unknown';
        }

        // Create new workspace
        const newWorkspaceResult = await this.createWorkspace({
          userId,
          userEmail: finalUserEmail,
          interviewUuid,
          lifetimeHours: this.config.defaultLifetimeHours
        });

        log.success('Created new workspace:', newWorkspaceResult.workspaceId);
        return newWorkspaceResult;
      }
    } catch (error: any) {
      log.failure('Error getting or creating workspace:', this.summarizeAwsError(error));
      return null;
    }
  }

  /**
   * Verify if a workspace is actually running by checking ECS service status
   */
  private async verifyWorkspaceIsRunning(workspace: IWorkspace): Promise<boolean> {
    try {
      // If workspace status is not running, it's definitely not running
      if (workspace.containerStatus !== 'running') {
        log.debug('Workspace status is not running:', workspace.containerStatus);
        return false;
      }

      // If no service ARN, it's not running
      if (!workspace.ecsServiceArn) {
        log.debug('No ECS service ARN found');
        return false;
      }

      // Check ECS service status
      const describeCommand = new DescribeServicesCommand({
        cluster: this.config.ecsClusterName,
        services: [workspace.ecsServiceArn]
      });

      const serviceResponse = await this.ecsClient.send(describeCommand);
      const service = serviceResponse.services?.[0];

      if (!service) {
        log.debug('ECS service not found');
        return false;
      }

      // Check if service has running tasks
      if (service.runningCount === 0) {
        log.debug('ECS service has no running tasks');
        return false;
      }

      // Additional check: try to ping the container
      if (workspace.publicIp) {
        try {
          const containerUrl = `http://${workspace.publicIp}:${workspace.containerConfig.port}`;

          // Use AbortController for timeout
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 5000);

          const response = await fetch(`${containerUrl}/health`, {
            method: 'GET',
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            log.debug('Container health check failed');
            return false;
          }
        } catch (healthError) {
          log.debug('Container is not reachable:', healthError);
          return false;
        }
      }

      log.debug('Workspace is verified as running');
      return true;
    } catch (error: any) {
      log.debug('Error verifying workspace status:', error);
      return false;
    }
  }

  /**
   * Cleanup expired workspaces
   */
  async cleanupExpiredWorkspaces(): Promise<void> {
    try {
      const expiredWorkspaces = await Workspace.find({
        expiresAt: { $lt: new Date() },
        autoCleanup: true,
        containerStatus: { $in: ['running', 'starting', 'creating'] }
      });

      console.log(`🧹 Found ${expiredWorkspaces.length} expired workspaces to cleanup`);

      for (const workspace of expiredWorkspaces) {
        try {
          await this.stopWorkspace((workspace._id as any).toString());
          console.log(`✅ Cleaned up workspace: ${workspace._id}`);
        } catch (error) {
          console.error(`❌ Error cleaning up workspace ${workspace._id}:`, error);
        }
      }
    } catch (error: any) {
      console.error('❌ Error during cleanup:', error);
    }
  }
}
