#!/bin/bash

# Script to create project structure from extracted JSON files (No jq required)
# Usage: ./create_project_from_json_no_jq.sh <json_file> [output_directory]
# Example: ./create_project_from_json_no_jq.sh extracted_structure.json my_project

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Show help
show_help() {
    echo "🚀 Project Structure Creator from JSON (No jq required)"
    echo
    echo "Usage: $0 <json_file> [output_directory]"
    echo
    echo "Arguments:"
    echo "  json_file        JSON file containing project structure"
    echo "  output_directory Optional output directory (default: generated_project)"
    echo
    echo "Supported JSON formats:"
    echo "  1. Structure JSON - Hierarchical project structure"
    echo "  2. Full Content JSON - Contains codeBlocks array"
    echo
    echo "Examples:"
    echo "  $0 extracted_structure.json"
    echo "  $0 extracted_structure.json my_project"
    echo "  $0 extracted_full_content.json output_folder"
    echo
    echo "Requirements:"
    echo "  - bash 4.0+"
    echo "  - No external dependencies!"
    echo
}

# Check arguments
if [ $# -lt 1 ] || [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        show_help
        exit 0
    else
        print_error "Usage: $0 <json_file> [output_directory]"
        echo "Use --help for more information"
        exit 1
    fi
fi

JSON_FILE="$1"
OUTPUT_DIR="${2:-generated_project}"

YES_FLAG=false
if [[ "$3" == "-y" || "$3" == "--yes" ]]; then
    YES_FLAG=true
fi

# Check if JSON file exists
if [ ! -f "$JSON_FILE" ]; then
    print_error "JSON file '$JSON_FILE' not found!"
    exit 1
fi

print_info "Creating project structure from: $JSON_FILE"
print_info "Output directory: $OUTPUT_DIR"

# Create output directory
if [ -d "$OUTPUT_DIR" ]; then
    print_warning "Directory '$OUTPUT_DIR' already exists. Contents may be overwritten."
    if [ "$YES_FLAG" = true ]; then
        print_info "Auto-confirm enabled. Continuing..."
    else
        read -p "Continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Operation cancelled."
            exit 0
        fi
    fi
else
    mkdir -p "$OUTPUT_DIR"
    print_success "Created output directory: $OUTPUT_DIR"
fi

# Function to extract string value from JSON (simple parser)
extract_json_string() {
    local json="$1"
    local key="$2"
    
    # Look for "key": "value" pattern
    local pattern="\"$key\"[[:space:]]*:[[:space:]]*\"([^\"]*)\""
    if [[ $json =~ $pattern ]]; then
        echo "${BASH_REMATCH[1]}"
    else
        echo ""
    fi
}

# Function to decode JSON escaped strings
decode_json_string() {
    local str="$1"
    # Replace common JSON escape sequences
    str="${str//\\\\/\\}"      # Replace \\ with \
    str="${str//\\\"/\"}"      # Replace \" with "
    str="${str//\\n/$'\n'}"    # Replace \n with actual newline
    str="${str//\\t/$'\t'}"    # Replace \t with actual tab
    str="${str//\\r/$'\r'}"    # Replace \r with carriage return
    echo "$str"
}

# Function to extract content between braces for a specific key
extract_object_content() {
    local json="$1"
    local key="$2"
    
    # Find the key and extract its object content
    local start_pattern="\"$key\"[[:space:]]*:[[:space:]]*\\{"
    
    if [[ $json =~ $start_pattern ]]; then
        local start_pos=$((${#BASH_REMATCH[0]} + ${BASH_REMATCH_START[0]} - 1))
        local content="${json:$start_pos}"
        
        # Find matching closing brace
        local brace_count=1
        local pos=0
        local char
        
        while [ $brace_count -gt 0 ] && [ $pos -lt ${#content} ]; do
            char="${content:$pos:1}"
            if [ "$char" = "{" ]; then
                ((brace_count++))
            elif [ "$char" = "}" ]; then
                ((brace_count--))
            fi
            ((pos++))
        done
        
        if [ $brace_count -eq 0 ]; then
            echo "${content:0:$((pos-1))}"
        fi
    fi
}

# Function to check if JSON contains codeBlocks (full content format)
has_code_blocks() {
    local json="$1"
    [[ $json =~ \"codeBlocks\"[[:space:]]*:[[:space:]]*\[ ]]
}

# Function to extract codeBlocks array content
extract_code_blocks() {
    local json="$1"
    
    # Find codeBlocks array
    if [[ $json =~ \"codeBlocks\"[[:space:]]*:[[:space:]]*\[([^\]]*)\] ]]; then
        echo "${BASH_REMATCH[1]}"
    fi
}

# Function to process codeBlocks format
process_code_blocks() {
    local json_file="$1"
    local output_dir="$2"

    print_info "Processing full content JSON format..."

    # Try to use Python for JSON parsing if available
    if command -v python3 &> /dev/null; then
        python3 << EOF
import json
import os
import sys

try:
    with open('$json_file', 'r', encoding='utf-8') as f:
        data = json.load(f)

    code_blocks = data.get('codeBlocks', [])
    if not code_blocks:
        print("❌ No codeBlocks found in the JSON file")
        sys.exit(1)

    print(f"ℹ️  Found {len(code_blocks)} code blocks to process")

    os.makedirs('$output_dir', exist_ok=True)

    for block in code_blocks:
        filename = block.get('filename', '')
        content = block.get('content', '')

        if filename:
            full_path = os.path.join('$output_dir', filename)
            parent_dir = os.path.dirname(full_path)

            if parent_dir:
                os.makedirs(parent_dir, exist_ok=True)

            # Decode JSON escape sequences
            content = content.replace('\\\\\\\\', '\\\\').replace('\\\\"', '"').replace('\\\\n', '\n').replace('\\\\t', '\t')

            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Created file: {full_path}")

except Exception as e:
    print(f"❌ Error: {e}")
    sys.exit(1)
EOF
    else
        # Fallback to basic regex parsing
        print_warning "Python3 not found. Using basic parsing for codeBlocks"
        process_structure_basic "$json_file" "$output_dir"
    fi
}

# Simple approach: Use Python if available, otherwise use basic regex parsing
process_structure() {
    local json_file="$1"
    local output_dir="$2"

    # Try to use Python for JSON parsing if available
    if command -v python3 &> /dev/null; then
        python3 << EOF
import json
import os
import sys

def create_structure(data, base_path):
    for key, value in data.items():
        full_path = os.path.join(base_path, key)

        if isinstance(value, dict):
            if value.get('type') == 'folder':
                os.makedirs(full_path, exist_ok=True)
                print(f"✅ Created directory: {full_path}")

                children = value.get('children', {})
                if children:
                    create_structure(children, full_path)

            elif value.get('type') == 'file':
                os.makedirs(os.path.dirname(full_path), exist_ok=True)

                content = value.get('content', '')
                # Decode JSON escape sequences
                content = content.replace('\\\\\\\\', '\\\\').replace('\\\\"', '"').replace('\\\\n', '\n').replace('\\\\t', '\t')

                with open(full_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ Created file: {full_path}")

try:
    with open('$json_file', 'r', encoding='utf-8') as f:
        data = json.load(f)

    os.makedirs('$output_dir', exist_ok=True)
    create_structure(data, '$output_dir')

except Exception as e:
    print(f"❌ Error: {e}")
    sys.exit(1)
EOF
    else
        # Fallback to basic regex parsing (limited functionality)
        print_warning "Python3 not found. Using basic parsing (limited functionality)"
        process_structure_basic "$json_file" "$output_dir"
    fi
}

# Basic fallback parsing for simple structures
process_structure_basic() {
    local json_file="$1"
    local output_dir="$2"

    print_warning "Using basic parsing - complex nested structures may not work correctly"

    # Extract simple file patterns from codeBlocks if present
    if grep -q '"codeBlocks"' "$json_file"; then
        print_info "Attempting to parse codeBlocks format..."

        # Extract filename and content pairs using grep and sed
        grep -o '"filename"[^}]*"content"[^}]*' "$json_file" | while IFS= read -r block; do
            # Extract filename
            local filename=$(echo "$block" | sed -n 's/.*"filename"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/p')
            # Extract content (basic extraction)
            local content=$(echo "$block" | sed -n 's/.*"content"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/p')

            if [ -n "$filename" ]; then
                local full_path="$output_dir/$filename"
                local parent_dir=$(dirname "$full_path")

                mkdir -p "$parent_dir"

                # Basic decode (limited)
                content="${content//\\n/$'\n'}"
                content="${content//\\t/$'\t'}"
                content="${content//\\\\/\\}"
                content="${content//\\\"/\"}"

                printf '%s\n' "$content" > "$full_path"
                print_success "Created file: $full_path"
            fi
        done
    else
        print_error "Basic parser cannot handle structure format. Please install Python3 for full functionality."
        return 1
    fi
}

# Main processing logic
print_info "Analyzing JSON structure..."

json_content=$(cat "$JSON_FILE")

if has_code_blocks "$json_content"; then
    print_info "Detected full content JSON format (with codeBlocks)"
    process_code_blocks "$JSON_FILE" "$OUTPUT_DIR"
else
    print_info "Detected structure JSON format"
    process_structure "$JSON_FILE" "$OUTPUT_DIR"
fi

print_success "Project structure created successfully in: $OUTPUT_DIR"

print_success "Done! 🎉"
