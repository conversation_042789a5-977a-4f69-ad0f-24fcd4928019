#!/bin/bash

# Script to create project structure from extracted JSON files
# Usage: ./create_project_from_json.sh <json_file> [output_directory]
# Example: ./create_project_from_json.sh extracted_structure.json my_project

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    print_error "jq is required but not installed. Please install jq first:"
    echo "  macOS: brew install jq"
    echo "  Ubuntu/Debian: sudo apt-get install jq"
    echo "  CentOS/RHEL: sudo yum install jq"
    exit 1
fi

# Show help
show_help() {
    echo "🚀 Project Structure Creator from JSON"
    echo
    echo "Usage: $0 <json_file> [output_directory]"
    echo
    echo "Arguments:"
    echo "  json_file        JSON file containing project structure"
    echo "  output_directory Optional output directory (default: generated_project)"
    echo
    echo "Supported JSON formats:"
    echo "  1. Structure JSON - Hierarchical project structure"
    echo "  2. Full Content JSON - Contains codeBlocks array"
    echo
    echo "Examples:"
    echo "  $0 extracted_structure.json"
    echo "  $0 extracted_structure.json my_project"
    echo "  $0 extracted_full_content.json output_folder"
    echo
    echo "Requirements:"
    echo "  - jq (JSON processor)"
    echo "  - bash 4.0+"
    echo
}

# Check arguments
if [ $# -lt 1 ] || [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        show_help
        exit 0
    else
        print_error "Usage: $0 <json_file> [output_directory]"
        echo "Use --help for more information"
        exit 1
    fi
fi

JSON_FILE="$1"
OUTPUT_DIR="${2:-generated_project}"

# Check if JSON file exists
if [ ! -f "$JSON_FILE" ]; then
    print_error "JSON file '$JSON_FILE' not found!"
    exit 1
fi

print_info "Creating project structure from: $JSON_FILE"
print_info "Output directory: $OUTPUT_DIR"

# Create output directory
if [ -d "$OUTPUT_DIR" ]; then
    print_warning "Directory '$OUTPUT_DIR' already exists. Contents may be overwritten."
    read -p "Continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Operation cancelled."
        exit 0
    fi
else
    mkdir -p "$OUTPUT_DIR"
    print_success "Created output directory: $OUTPUT_DIR"
fi

# Function to create files and directories recursively
create_structure() {
    local json_data="$1"
    local current_path="$2"
    
    # Get all keys from the JSON object
    local keys=$(echo "$json_data" | jq -r 'keys[]' 2>/dev/null || echo "")
    
    if [ -z "$keys" ]; then
        return
    fi
    
    for key in $keys; do
        local item=$(echo "$json_data" | jq -r ".[\"$key\"]")
        local item_type=$(echo "$item" | jq -r '.type // empty')
        local full_path="$current_path/$key"
        
        if [ "$item_type" = "folder" ]; then
            # Create directory
            mkdir -p "$full_path"
            print_success "Created directory: $full_path"
            
            # Recursively process children
            local children=$(echo "$item" | jq -r '.children // {}')
            create_structure "$children" "$full_path"
            
        elif [ "$item_type" = "file" ]; then
            # Create file with content
            local content=$(echo "$item" | jq -r '.content // ""')
            
            # Create parent directory if it doesn't exist
            local parent_dir=$(dirname "$full_path")
            mkdir -p "$parent_dir"
            
            # Write content to file
            echo -e "$content" > "$full_path"
            print_success "Created file: $full_path"
            
        else
            print_warning "Unknown item type for '$key': $item_type"
        fi
    done
}

# Function to handle full content JSON (with codeBlocks)
create_from_full_content() {
    local json_file="$1"
    local output_dir="$2"

    print_info "Processing full content JSON format..."

    # Get the number of code blocks
    local block_count=$(jq -r '.codeBlocks | length' "$json_file" 2>/dev/null)

    if [ "$block_count" = "null" ] || [ "$block_count" = "0" ]; then
        print_error "No codeBlocks found in the JSON file"
        return 1
    fi

    print_info "Found $block_count code blocks to process"

    # Process each code block by index
    for ((i=0; i<block_count; i++)); do
        local filename=$(jq -r ".codeBlocks[$i].filename // empty" "$json_file")
        local content=$(jq -r ".codeBlocks[$i].content // \"\"" "$json_file")

        if [ -n "$filename" ] && [ "$filename" != "null" ] && [ "$filename" != "empty" ]; then
            local full_path="$output_dir/$filename"
            local parent_dir=$(dirname "$full_path")

            # Create parent directory
            mkdir -p "$parent_dir"

            # Write content to file (handle escaped characters properly)
            printf '%s\n' "$content" > "$full_path"
            print_success "Created file: $full_path"
        fi
    done
}

# Detect JSON format and process accordingly
print_info "Analyzing JSON structure..."

# Check if it's a full content JSON (has codeBlocks)
if jq -e '.codeBlocks' "$JSON_FILE" >/dev/null 2>&1; then
    print_info "Detected full content JSON format (with codeBlocks)"
    create_from_full_content "$JSON_FILE" "$OUTPUT_DIR"
else
    print_info "Detected structure JSON format"
    # Read the JSON and create structure
    json_content=$(cat "$JSON_FILE")
    create_structure "$json_content" "$OUTPUT_DIR"
fi

print_success "Project structure created successfully in: $OUTPUT_DIR"

# Show summary
total_files=$(find "$OUTPUT_DIR" -type f | wc -l)
total_dirs=$(find "$OUTPUT_DIR" -type d | wc -l)

echo
print_info "Summary:"
echo "  📁 Directories created: $((total_dirs - 1))"  # Subtract 1 for the root directory
echo "  📄 Files created: $total_files"
echo "  📂 Root directory: $OUTPUT_DIR"

# Show directory tree if tree command is available
if command -v tree &> /dev/null; then
    echo
    print_info "Project structure:"
    tree "$OUTPUT_DIR" -L 3  # Limit depth to 3 levels for readability
else
    echo
    print_info "Install 'tree' command to see the project structure visualization"
    echo "  macOS: brew install tree"
    echo "  Ubuntu/Debian: sudo apt-get install tree"
fi

print_success "Done! 🎉"
