{"compilerOptions": {"target": "es2020", "module": "commonjs", "lib": ["es2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "baseUrl": ".", "paths": {"*": ["node_modules/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.test.ts"]}