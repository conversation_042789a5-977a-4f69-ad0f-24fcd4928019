# Testing Anthropic API Integration Through Endpoints

## 🚀 NEW: Test Endpoints (No API Key Required!)

### 1. Test Payload Format
Test the exact payload that would be sent to Anthropic API:

```bash
curl -X POST http://localhost:3001/api/test/payload \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "create a website to introduce an AI company",
    "projectData": {
      "technical": {
        "Architecture & System Impact": [
          {"Is this a new module, a feature within an existing module, or a refactor?": "new"}
        ]
      },
      "functional": {
        "Understanding the Goal / Intent": [
          {"What problem are we trying to solve?": "create a marketing website"}
        ]
      }
    }
  }'
```

**Shows you:**
- ✅ Exact payload format that matches your curl example
- ✅ Generated curl command you can copy/paste
- ✅ Verification that all fields are correct
- ✅ Context formatting with "generate code for context:" prefix

### 2. Test Response Processing
Test how API responses are processed:

```bash
curl -X GET http://localhost:3001/api/test/response
```

**Shows you:**
- ✅ Mock Anthropic API response format
- ✅ How content.text is extracted
- ✅ Token usage processing
- ✅ Metadata generation

### 3. Test Complete End-to-End Flow
Test the entire flow from payload to final result:

```bash
curl -X POST http://localhost:3001/api/test/end-to-end \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "create a simple website",
    "projectData": {
      "technical": {"Architecture & System Impact": [{"type": "new"}]},
      "functional": {"Understanding the Goal / Intent": [{"goal": "marketing"}]}
    }
  }'
```

**Shows you:**
- ✅ Step 1: Payload generation and verification
- ✅ Step 2: API call simulation with fallback
- ✅ Step 3: BuildService processing and code extraction
- ✅ Complete verification that everything works

## 4. Health Check Endpoint

Test the API configuration status:

```bash
curl -X GET http://localhost:3001/api/llm/health
```

**Expected Response:**
```json
{
  "success": true,
  "status": "healthy",
  "anthropicAPI": {
    "configured": false,  // Will be true when you add real API key
    "model": "claude-sonnet-4-20250514",
    "maxTokens": 64000
  },
  "fallbackDataAvailable": true,
  "timestamp": "2025-07-26T03:27:05.710Z"
}
```

## 5. LLM Generation Endpoint (Requires Auth)

Test the actual LLM generation (requires authentication):

```bash
# First, get an auth token
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "leo123"
  }'

# Use the token from the response in the Authorization header
curl -X POST http://localhost:3001/api/llm/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "prompt": "create a website to introduce an AI company",
    "projectData": {
      "technical": {
        "Architecture & System Impact": [
          {
            "Is this a new module, a feature within an existing module, or a refactor?": "new"
          }
        ]
      },
      "functional": {
        "Understanding the Goal / Intent": [
          {
            "What problem are we trying to solve?": "create a marketing website"
          }
        ]
      }
    }
  }'
```

## 3. What You'll See in the Server Logs

When you make the API call, you'll see detailed logs showing:

### Payload Format:
```
📋 FULL REQUEST PAYLOAD:
{
  "model": "claude-sonnet-4-20250514",
  "max_tokens": 64000,
  "messages": [
    {
      "role": "user",
      "content": "generate code for context: {\"objective\":\"create a website...\",\"requirement\":{...}}"
    }
  ]
}
```

### Response Processing (when using fallback):
```
🔄 Attempting to use fallback mock data...
✅ Fallback: Using mock data from sample_response
```

### Response Processing (with real API key):
```
📥 FULL API RESPONSE STRUCTURE:
{
  "id": "msg_013Zva2CMHLNnXjNJJKqJ2EF",
  "model": "claude-sonnet-4-20250514",
  "role": "assistant",
  "stop_reason": "end_turn",
  "type": "message",
  "usage": {
    "input_tokens": 2095,
    "output_tokens": 503
  },
  "content_count": 1,
  "content_types": ["text"]
}

🔍 CONTENT EXTRACTION:
- Total content items: 1
- Text items found: 1
- Extracted content length: 49190
- Content preview: "I'll create a modern, responsive AI agent..."
```

## 4. Frontend Integration Test

You can also test through the frontend by:

1. Starting the frontend: `cd frontend && npm start`
2. Going through the interview process
3. Clicking "Build" on the review page
4. Watching the browser network tab to see the API calls
5. Checking the browser console for any errors

## 5. When You Add Your Real API Key

Simply update `backend/.env`:
```env
ANTHROPIC_API_KEY=sk-ant-api03-your-real-key-here
```

Then restart the backend server and you'll see:
- Health check shows `"configured": true`
- Real API calls are made instead of fallback
- Full request/response logging as shown above
