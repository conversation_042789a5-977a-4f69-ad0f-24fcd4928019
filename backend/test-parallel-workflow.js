/**
 * Test script to verify the parallel workflow timing
 * This simulates the production workflow with realistic timing
 */

const axios = require('axios');

async function testParallelWorkflow() {
  console.log('🧪 Testing parallel workflow with realistic timing...');
  
  const testPayload = {
    interviewUuid: `test-${Date.now()}`,
    prompt: 'Create a simple React todo app',
    projectData: {
      name: 'Todo App',
      type: 'react',
      description: 'A simple todo application'
    },
    userData: {
      email: '<EMAIL>'
    }
  };

  const startTime = Date.now();
  console.log('⏰ Starting unified build at:', new Date().toISOString());

  try {
    // Test the unified build endpoint
    const response = await axios.post('http://localhost:3001/api/build/unified', testPayload, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token' // You'll need a valid token
      },
      timeout: 300000 // 5 minutes timeout
    });

    const endTime = Date.now();
    const totalTime = (endTime - startTime) / 1000;

    console.log('✅ Unified build completed successfully!');
    console.log('⏰ Total time:', totalTime, 'seconds');
    console.log('📊 Response summary:', {
      success: response.data.success,
      buildResultUuid: response.data.buildResult?.uuid,
      codeBlocksCount: response.data.buildResult?.codeBlocks?.length || 0,
      workspaceStatus: response.data.workspace?.status
    });

    // Verify both operations completed
    if (response.data.buildResult && response.data.buildResult.codeBlocks) {
      console.log('✅ LLM generation: SUCCESS');
    } else {
      console.log('❌ LLM generation: FAILED');
    }

    if (response.data.workspace || response.data.buildResult) {
      console.log('✅ ECS workspace: SUCCESS');
    } else {
      console.log('❌ ECS workspace: FAILED');
    }

  } catch (error) {
    const endTime = Date.now();
    const totalTime = (endTime - startTime) / 1000;
    
    console.error('❌ Test failed after', totalTime, 'seconds');
    console.error('Error:', error.response?.data || error.message);
  }
}

// Run the test
if (require.main === module) {
  testParallelWorkflow();
}

module.exports = { testParallelWorkflow };
