# S3 Migration Guide

This guide explains how to migrate from storing ECS workspace files in the database (`fileSystemSnapshot`) to AWS S3.

## Overview

The migration moves workspace files from the MongoDB `fileSystemSnapshot` field to AWS S3 storage, organized by user email and workspace name:

```
S3 Bucket: mergen-code
Structure: user_email/workspace_name/file_path
Example: <EMAIL>/workspace_abc123/src/index.js
```

## Prerequisites

1. **AWS S3 Bucket**: Ensure the `mergen-code` bucket exists in `us-east-1`
2. **AWS Credentials**: Your AWS credentials must have S3 permissions for the bucket
3. **Environment Variables**: Ensure AWS credentials are configured in your `.env` file

## Required AWS S3 Permissions

Your AWS credentials need the following S3 permissions for the `mergen-code` bucket:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::mergen-code",
        "arn:aws:s3:::mergen-code/*"
      ]
    }
  ]
}
```

## Environment Variables

Add these to your `.env` file if not already present:

```bash
# AWS Configuration (required for S3)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key
```

## Migration Process

### 1. Backup Your Database

Before running the migration, create a backup of your MongoDB database:

```bash
mongodump --uri="your_mongodb_connection_string" --out=backup_before_s3_migration
```

### 2. Run the Migration

Navigate to the backend directory and run:

```bash
cd backend
npm run migrate-s3 migrate-all
```

This will:
- Find all workspaces with `fileSystemSnapshot` but no `s3FileStorage`
- Upload files to S3 in the format `user_email/workspace_name/`
- Update workspace records with S3 metadata
- Remove the old `fileSystemSnapshot` data

### 3. Verify Migration

To verify a specific workspace was migrated correctly:

```bash
npm run migrate-s3 verify <workspace_id>
```

### 4. Monitor Logs

The migration process provides detailed logging:
- ✅ Successful operations
- ❌ Errors and failures
- 📊 Progress statistics

## Database Schema Changes

### Before Migration
```typescript
interface FileSystemSnapshot {
  files: { [filePath: string]: string };
  structure: any;
  lastSavedAt: Date;
  version: number;
  initialFilesUploaded?: boolean;
}
```

### After Migration
```typescript
interface S3FileStorage {
  userEmail: string;
  s3Prefix: string; // "user_email/workspace_name/"
  lastSavedAt: Date;
  version: number;
  fileCount: number;
  totalSize: number;
  initialFilesUploaded?: boolean;
}
```

## Rollback (Emergency Only)

If you need to rollback a workspace from S3 to database storage:

```bash
npm run migrate-s3 rollback <workspace_id>
```

**Warning**: This downloads files from S3 back to the database. Only use in emergencies as it defeats the purpose of the migration.

## Code Changes Summary

### Backend Changes

1. **New S3FileService**: Handles all S3 operations
2. **Updated Workspace Model**: Replaced `fileSystemSnapshot` with `s3FileStorage`
3. **Updated ECSFileSystemService**: Uses S3 as fallback instead of database
4. **Updated ECSWorkspaceService**: Passes user email for S3 operations
5. **Updated Controllers**: Pass user email from JWT token

### Key Files Modified

- `src/models/Workspace.ts` - Schema changes
- `src/services/s3FileService.ts` - New S3 service
- `src/services/ecsFileSystemService.ts` - S3 integration
- `src/services/ecsWorkspaceService.ts` - User email handling
- `src/controllers/ecsWorkspaceController.ts` - Pass user email
- `src/controllers/buildController.ts` - Pass user email

## Testing

After migration, test the following workflows:

1. **Create New Workspace**: Files should be stored in S3
2. **Read Files**: Should work from both container and S3 fallback
3. **Write Files**: Should save to both container and S3
4. **Workspace Cleanup**: Should backup files to S3 before cleanup
5. **Container Restart**: Should restore files from S3 to container

## Troubleshooting

### Migration Fails with AWS Credentials Error
- Verify AWS credentials in `.env` file
- Check IAM permissions for S3 bucket access
- Ensure bucket `mergen-code` exists in `us-east-1`

### Files Not Found After Migration
- Run verification: `npm run migrate-s3 verify <workspace_id>`
- Check S3 bucket contents in AWS Console
- Review migration logs for errors

### Performance Issues
- S3 operations are async and may be slower than database
- Consider implementing caching if needed
- Monitor S3 request costs

## Benefits of S3 Migration

1. **Reduced Database Size**: Files no longer stored in MongoDB
2. **Better Scalability**: S3 handles large files better than database
3. **Cost Efficiency**: S3 storage is cheaper than database storage
4. **Backup & Recovery**: S3 provides built-in durability and versioning
5. **Performance**: Reduces database load and query times

## S3 Bucket Structure

```
mergen-code/
├── <EMAIL>/
│   ├── workspace_abc123/
│   │   ├── src/
│   │   │   ├── index.js
│   │   │   └── utils.js
│   │   ├── package.json
│   │   └── README.md
│   └── workspace_def456/
│       └── main.py
└── <EMAIL>/
    └── workspace_ghi789/
        └── app.js
```

## Monitoring

After migration, monitor:
- S3 storage usage and costs
- Application performance
- Error rates in logs
- User experience with file operations

## Support

If you encounter issues during migration:
1. Check the migration logs for specific error messages
2. Verify AWS credentials and permissions
3. Ensure the S3 bucket exists and is accessible
4. Contact the development team with specific error details