# Mergen Backend

This is the backend server for the Mergen application built with Node.js, Express, and TypeScript.

## Setup

1. Install dependencies:
```bash
npm install
```

2. Create a `.env` file in the root directory:
```bash
cp .env.example .env
```

3. Build the TypeScript code:
```bash
npm run build
```

## Development

Run the development server with hot reload:
```bash
npm run dev
```

## Production

Build and start the production server:
```bash
npm run build
npm start
```

## Available Scripts

- `npm start`: Run the production server
- `npm run dev`: Run the development server with hot reload
- `npm run build`: Build the TypeScript code
- `npm run test`: Run tests
- `npm run lint`: Run ESLint

## API Endpoints

- `GET /health`: Health check endpoint that returns server status 