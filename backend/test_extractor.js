// Test script for CodeExtractor
// Run with: node test_extractor.js

const { CodeExtractor } = require('./dist/utils/codeExtractor');
const fs = require('fs').promises;

async function testCodeExtractor() {
  console.log('🧪 Testing CodeExtractor...\n');

  // Test 1: Extract from sample response file
  console.log('📁 Test 1: Extract structure from ../doc/sample_response');
  try {
    const structure = await CodeExtractor.getCodeStructureFromFile('../doc/sample_response');
    console.log('✅ Structure extracted successfully!');
    console.log('📊 Number of root items:', Object.keys(structure).length);

    // Show some structure details
    if (structure.src) {
      console.log('📂 src folder contains:', Object.keys(structure.src.children).length, 'items');
    }
    if (structure['package.json']) {
      console.log('📦 package.json detected');
    }

    // Save structure as JSON file
    const structureJson = JSON.stringify(structure, null, 2);
    console.log('🔍 Structure type:', typeof structure);
    console.log('� Structure keys:', Object.keys(structure));
    console.log('🔍 JSON length:', structureJson.length);
    console.log('🔍 JSON preview:', structureJson.substring(0, 200));
    await fs.writeFile('../results/extracted_structure.json', structureJson, 'utf8');
    console.log('� Structure saved to extracted_structure.json');

    // Show preview of structure
    console.log('📋 Structure preview:');
    Object.keys(structure).slice(0, 5).forEach(key => {
      const item = structure[key];
      if (item.type === 'folder') {
        console.log(`   📂 ${key}/ (${Object.keys(item.children).length} items)`);
      } else {
        console.log(`   📄 ${key} (${item.language})`);
      }
    });
    if (Object.keys(structure).length > 5) {
      console.log(`   ... and ${Object.keys(structure).length - 5} more items`);
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 2: Extract full content from sample response
  console.log('📄 Test 2: Extract full content from ../doc/sample_response');
  try {
    const fullContent = await CodeExtractor.getFullContentFromFile('../doc/sample_response');
    console.log('✅ Full content extracted successfully!');
    console.log('📝 Description length:', fullContent.description.length, 'characters');
    console.log('🔢 Number of code blocks:', fullContent.codeBlocks.length);

    // Show code block details
    fullContent.codeBlocks.forEach((block, index) => {
      console.log(`   ${index + 1}. ${block.filename} (${block.language}) - ${block.content.length} chars`);
    });

    // Save full content as JSON file
    const fullContentJson = JSON.stringify(fullContent, null, 2);
    await fs.writeFile('../results/extracted_full_content.json', fullContentJson, 'utf8');
    console.log('💾 Full content saved to extracted_full_content.json');

    // Show description preview
    if (fullContent.description) {
      const descPreview = fullContent.description.length > 100
        ? fullContent.description.substring(0, 100) + '...'
        : fullContent.description;
      console.log('📝 Description preview:', descPreview);
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 3: Extract from LLM response content directly
  console.log('🤖 Test 3: Extract from LLM response content');
  const llmResponse = `
Here's a simple Node.js Express server:

### \`server.js\`
\`\`\`javascript
const express = require('express');
const app = express();
const port = 3000;

app.get('/', (req, res) => {
  res.send('Hello World!');
});

app.listen(port, () => {
  console.log(\`Server running at http://localhost:\${port}\`);
});
\`\`\`

### \`package.json\`
\`\`\`json
{
  "name": "simple-server",
  "version": "1.0.0",
  "main": "server.js",
  "dependencies": {
    "express": "^4.18.0"
  }
}
\`\`\`
`;

  try {
    const structure = CodeExtractor.extractCodeStructure(llmResponse);
    console.log('✅ Structure extracted from LLM response!');
    console.log('📊 Files found:', Object.keys(structure).length);
    Object.keys(structure).forEach(filename => {
      console.log(`   - ${filename} (${structure[filename].language})`);
    });

    // Save LLM response structure as JSON file
    const llmStructureJson = JSON.stringify(structure, null, 2);
    await fs.writeFile('../results/extracted_llm_structure.json', llmStructureJson, 'utf8');
    console.log('💾 LLM structure saved to extracted_llm_structure.json');
  } catch (error) {
    console.error('❌ Error:', error.message);
  }

  console.log('\n🎉 All tests completed!');
  console.log('📁 Generated files:');
  console.log('   - extracted_structure.json (from ../doc/sample_response)');
  console.log('   - extracted_full_content.json (full content from ../doc/sample_response)');
  console.log('   - extracted_llm_structure.json (from LLM response example)');
}

// Run the tests
testCodeExtractor().catch(console.error);
