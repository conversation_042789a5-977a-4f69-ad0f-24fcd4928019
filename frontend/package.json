{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "antd": "^5.27.0", "axios": "^1.6.7", "framer-motion": "^12.23.12", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.21.3", "postcss": "^8.5.6", "tailwindcss": "^4.0.14", "@tailwindcss/postcss": "^4.0.14"}, "devDependencies": {"@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^4.5.2", "@types/node": "^20.11.24"}}