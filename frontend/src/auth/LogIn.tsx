import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Form, Alert } from 'antd';
import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons';
import headingImg from '@/assets/images/headingImg.svg';
import bgImg from '@/assets/images/Maskgroup.png';
import textImg from '@/assets/images/textImg.svg';
import arrowIcon from '@/assets/images/arrowIcon.svg';
import { useAuth } from '@/context/AuthContext';

const LogIn = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { login } = useAuth();
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const togglePasswordVisibility = () => {
    setPasswordVisible((prev) => !prev);
  };

  const onFinish = async ({ email, password }: { email: string; password: string }) => {
    setAlertMessage('');
    setSuccessMessage('');
    setSubmitting(true);
    try {
      await login({ email, password });
      setSuccessMessage(`Logged in as ${email}`);
      navigate('/');
    } catch (err: any) {
      setAlertMessage(err?.response?.data?.message || 'Login failed');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <>
      <div className="flex lg:px-11 px-6 py-9 items-start font-Inter">
        <div className="relative w-[52%] hidden md:block">
          <img src={bgImg} alt="bgImg" loading="eager" className="w-full h-[calc(100vh-80px)]" />
          <img src={headingImg} alt="headingImg" loading="eager" className="invert max-h-[40px] max-w-[159px] absolute top-[50px] left-[37px]" />
          <img src={textImg} alt="headingImg" loading="eager" className="absolute bottom-[50px] left-[37px] lg:w-auto w-[80%]" />
        </div>
        <div className="md:w-[48%] w-full lg:px-[71px] md:px-8 px-2">
          <div className="pt-16">
            <h1 className="font-Inter font-black md:text-[45px] leading-[100%] tracking-[0%] text-[32px]">Welcome back!</h1>

            <div className="flex relative mt-8 mb-14">
              <button className={`bg-[#1E1E1E] py-4 px-6 rounded-[42px] cursor-pointer w-[50%] z-10`}>
                <span className="text-white flex gap-3 font-extrabold text-[16px] justify-center">Log In</span>
              </button>
              <button className={`bg-[#8C8C8C] py-4 px-6 rounded-[42px] cursor-pointer w-full absolute top-0 flex justify-end`} onClick={() => navigate('/signup')}>
                <span className="text-white flex gap-3 font-extrabold text-[16px] w-[50%] justify-center">Sign Up</span>
              </button>
            </div>

            {(alertMessage || successMessage) && (
              <>
                <Alert
                  message={successMessage ? successMessage : alertMessage}
                  type={successMessage ? 'success' : 'error'}
                  showIcon
                  closable
                  className="mb-5"
                  style={{ marginBottom: '30px' }}
                  onClose={() => {
                    setAlertMessage('');
                    setSuccessMessage('');
                  }}
                />
              </>
            )}

            <Form name="login" form={form} initialValues={{ remember: true }} onFinish={onFinish} className="w-full flex flex-col items-start gap-2 mt-6">
              <Form.Item
                name="email"
                rules={[
                  { required: true, message: 'Please input your Email!' },
                  { type: 'email' as const, message: 'Please enter a valid Email!' },
                ]}
                className="w-full"
              >
                <input
                  value={form.getFieldValue('email')}
                  onChange={(e) => form.setFieldsValue({ email: e.target.value })}
                  placeholder="Email"
                  className="bg-[#1D1D1DCC] shadow-[inset_0px_4px_4px_0px_#00000040] rounded-[42px] font-Inter font-normal text-[14px] leading-[100%] tracking-[0%] py-3.5 text-white placeholder:text-[#CFCFCF] ps-7 outline-0 w-full auth-input"
                  type="email"
                />
              </Form.Item>

              <div className="relative w-full">
                <Form.Item name="password" rules={[{ required: true, message: 'Please input your Password!' }]} className="w-full">
                  <input
                    value={form.getFieldValue('password')}
                    onChange={(e) => form.setFieldsValue({ password: e.target.value })}
                    placeholder="Password"
                    className="bg-[#1D1D1DCC] shadow-[inset_0px_4px_4px_0px_#00000040] rounded-[42px] font-Inter font-normal text-[14px] leading-[100%] tracking-[0%] py-3.5 text-white placeholder:text-[#CFCFCF] ps-7 outline-0 w-full auth-input"
                    type={passwordVisible ? 'text' : 'password'}
                  />
                </Form.Item>

                <span className="absolute right-5 top-6 -translate-y-1/2 text-white cursor-pointer">
                  {passwordVisible ? (
                    <EyeInvisibleOutlined onClick={() => togglePasswordVisibility()} className="text-gray-400 cursor-pointer hover:text-white transition" />
                  ) : (
                    <EyeOutlined onClick={() => togglePasswordVisibility()} className="text-gray-400 cursor-pointer hover:text-white transition" />
                  )}
                </span>
              </div>

              <div className="flex justify-between w-full">
                <Form.Item className="flex justify-center">
                  <button className={`bg-[#1E1E1E] py-3 px-6 rounded-[25px] cursor-pointer mt-5`} disabled={submitting}>
                    <span className="text-white flex gap-3 font-extrabold text-[18px]">
                      {submitting ? 'Logging In...' : 'Log In'}
                      <img src={arrowIcon} alt="icon" height={12} width={14} />
                    </span>
                  </button>
                </Form.Item>
                <button className="text-[14px] font-bold cursor-pointer" disabled>
                  Forgot password
                </button>
              </div>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
};

export default LogIn; 