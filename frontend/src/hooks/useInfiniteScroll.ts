import { useState, useEffect, useCallback, useRef } from 'react';

interface UseInfiniteScrollOptions<T> {
  fetchData: (page: number, limit: number) => Promise<{
    data: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      hasMore: boolean;
      totalPages: number;
    };
  }>;
  limit?: number;
  initialPage?: number;
}

interface UseInfiniteScrollReturn<T> {
  data: T[];
  loading: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  error: string | null;
  loadMore: () => void; // Manual load more only
  refresh: () => void;
  scrollRef: React.RefObject<HTMLDivElement>;
}

export function useInfiniteScroll<T>({
  fetchData,
  limit = 18,
  initialPage = 1
}: UseInfiniteScrollOptions<T>): UseInfiniteScrollReturn<T> {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [initialized, setInitialized] = useState(false);

  const scrollRef = useRef<HTMLDivElement>(null);
  const isLoadingRef = useRef(false);
  const fetchDataRef = useRef(fetchData);

  // Update fetchData ref when it changes
  useEffect(() => {
    fetchDataRef.current = fetchData;
  }, [fetchData]);

  // Initial data fetch
  const fetchInitialData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      isLoadingRef.current = true;

      const response = await fetchDataRef.current(initialPage, limit);
      setData(response.data);
      setHasMore(response.pagination.hasMore);
      setCurrentPage(initialPage);
      setInitialized(true);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch data');
      setData([]);
      setHasMore(false);
      setInitialized(true);
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, [limit, initialPage]);

  // Load more data
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoadingRef.current) return;

    try {
      setLoadingMore(true);
      setError(null);
      isLoadingRef.current = true;

      const nextPage = currentPage + 1;
      const response = await fetchDataRef.current(nextPage, limit);

      setData(prevData => [...prevData, ...response.data]);
      setHasMore(response.pagination.hasMore);
      setCurrentPage(nextPage);
    } catch (err: any) {
      setError(err.message || 'Failed to load more data');
    } finally {
      setLoadingMore(false);
      isLoadingRef.current = false;
    }
  }, [limit, currentPage, hasMore]);

  // Refresh data (reset to first page)
  const refresh = useCallback(() => {
    setCurrentPage(initialPage);
    fetchInitialData();
  }, [fetchInitialData, initialPage]);

  // No automatic scroll handling - events handled in component

  // Initial data fetch on mount - only run once
  useEffect(() => {
    if (!initialized) {
      fetchInitialData();
    }
  }, [fetchInitialData, initialized]);

  return {
    data,
    loading,
    loadingMore,
    hasMore,
    error,
    loadMore,
    refresh,
    scrollRef
  };
}
