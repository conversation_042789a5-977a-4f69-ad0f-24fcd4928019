import { QuestionSection } from './types';

export const FUNCTIONAL_QUESTIONS: QuestionSection[] = [
  {
    id: 1,
    title: "Understanding the Goal / Intent",
    emoji: "🔍",
    questions: [
      "What problem are we trying to solve?",
      "What is the primary goal of this task?",
      "What outcome or user behavior do we want to enable or improve?",
      "What would success look like?",
      "What are the key KPIs or metrics this task impacts?"
    ]
  },
  {
    id: 2,
    title: "User & Use Case Context",
    emoji: "👥",
    questions: [
      "Who are the end users?",
      "What are they trying to do when using this feature?",
      "What pain points or inefficiencies do they currently face?",
      "Are there different personas or user groups involved?",
      "How frequently will this be used?"
    ]
  },
  {
    id: 3,
    title: "Functionality & Requirements",
    emoji: "🧱",
    questions: [
      "What are the specific features or actions the system should support?",
      "Are there any must-have vs. nice-to-have functionalities?",
      "What inputs are expected? What outputs are required?",
      "Are there edge cases we should consider?",
      "Should this work across mobile, desktop, API, etc.?"
    ]
  },
  {
    id: 4,
    title: "Data & Integration",
    emoji: "📐",
    questions: [
      "What data is involved? Where does it come from?",
      "Is any new data storage or schema change needed?",
      "What existing systems or APIs does this need to connect with?",
      "What is the expected data flow or lifecycle?",
      "Are there any specific rules or calculations involved?"
    ]
  },
  {
    id: 5,
    title: "UI/UX Expectations",
    emoji: "🖼️",
    questions: [
      "Is there a design or wireframe available?",
      "If not, should we propose one or use an existing design system?",
      "What user interactions (buttons, drag-drop, filters, etc.) are expected?",
      "What is the desired layout or structure of the interface?",
      "Should it support accessibility standards?"
    ]
  },
  {
    id: 6,
    title: "Behavior & Logic",
    emoji: "🚦",
    questions: [
      "What should happen when a user takes [specific action]?",
      "Are there any validation or error-handling requirements?",
      "Are there states or transitions we need to manage?",
      "Are there any timing or conditional logic rules?"
    ]
  },
  {
    id: 7,
    title: "Security & Access Control",
    emoji: "🛡️",
    questions: [
      "Who can access this functionality?",
      "Are there any roles or permissions involved?",
      "Are there data privacy or compliance concerns (GDPR, PIPEDA)?",
      "Are there actions that should be logged or audited?"
    ]
  },
  {
    id: 8,
    title: "Performance & Scalability",
    emoji: "⏱️",
    questions: [
      "What is the expected load or usage?",
      "Are there performance benchmarks or SLAs?",
      "Should we plan for future scalability?"
    ]
  },
  {
    id: 9,
    title: "Testing & Quality Assurance",
    emoji: "🧪",
    questions: [
      "What are the expected test scenarios?",
      "Are there edge cases that need coverage?",
      "Should there be unit, integration, or end-to-end tests?",
      "Who is responsible for testing and sign-off?"
    ]
  },
  {
    id: 10,
    title: "Dependencies & Constraints",
    emoji: "🔄",
    questions: [
      "Are there any blockers or dependencies?",
      "Are there external systems or teams involved?",
      "Are there tech stack limitations or preferences?",
      "Are there regulatory or policy constraints?"
    ]
  }
];

export const TECHNICAL_QUESTIONS: QuestionSection[] = [
  {
    id: 1,
    title: "Architecture & System Impact",
    emoji: "🧱",
    questions: [
      "What part of the system does this affect?",
      "Is this a new module, a feature within an existing module, or a refactor?",
      "Will it require changes to backend, frontend, or both?",
      "Is this a short-term patch or long-term solution?",
      "Does it align with existing system architecture?"
    ]
  },
  {
    id: 2,
    title: "Scope & Boundaries",
    emoji: "🧩",
    questions: [
      "What is in-scope and what is explicitly out-of-scope?",
      "Are there any existing features we must not break?",
      "What are the known constraints (technical, time, resource)?",
      "Does this task depend on or block other tasks?"
    ]
  },
  {
    id: 3,
    title: "Interfaces, APIs & Integrations",
    emoji: "📐",
    questions: [
      "Are there existing APIs or services this will use or expose?",
      "Should we build or consume REST, GraphQL, gRPC, or something else?",
      "What are the expected request/response shapes?",
      "Is authentication or authorization needed on API calls?",
      "Are there third-party services or internal systems we need to integrate with?"
    ]
  },
  {
    id: 4,
    title: "Data Requirements",
    emoji: "🗃️",
    questions: [
      "What data sources are involved (DBs, files, external services)?",
      "Are there new tables, fields, or indexes to be created?",
      "What data transformations, validations, or calculations are needed?",
      "Will this need data migration or backfilling?",
      "What is the expected volume of data?"
    ]
  },
  {
    id: 5,
    title: "Security & Compliance",
    emoji: "🔐",
    questions: [
      "Does this involve sensitive data (PII, PHI, financials)?",
      "Are there specific security requirements (encryption, hashing)?",
      "Who can access what? Are RBAC/ABAC policies required?",
      "Are logs and error messages safe from leaking sensitive info?",
      "Are there compliance requirements (e.g., GDPR, SOC2, HIPAA)?"
    ]
  },
  {
    id: 6,
    title: "Testing & Reliability",
    emoji: "🧪",
    questions: [
      "What types of testing are needed (unit, integration, load, E2E)?",
      "Are there test cases or mock data available?",
      "Should this feature be behind a feature flag?",
      "Do we need to simulate failure conditions or retries?",
      "Will this need test automation or manual QA?"
    ]
  },
  {
    id: 7,
    title: "Deployment & DevOps",
    emoji: "⚙️",
    questions: [
      "Will this require new infrastructure (server, container, queue)?",
      "Are there CI/CD pipelines already set up?",
      "Should this be deployed independently or bundled?",
      "Any rollback or migration strategy in case of failure?",
      "Does it require blue/green deployment, canary release, or hotfix?"
    ]
  },
  {
    id: 8,
    title: "Performance & Scalability",
    emoji: "📈",
    questions: [
      "What is the expected request or transaction volume?",
      "Are there latency or throughput targets?",
      "Is this synchronous or asynchronous?",
      "Should we use caching, queuing, batching, or pagination?",
      "How does it scale under concurrent users or large datasets?"
    ]
  },
  {
    id: 9,
    title: "Logging, Monitoring, & Observability",
    emoji: "📊",
    questions: [
      "What should we log (info, errors, metrics)?",
      "Are alerts or dashboards required?",
      "Which tools are used (e.g., Datadog, Prometheus, ELK, Sentry)?",
      "Should we trace performance issues with APM tools?",
      "Are logs structured and searchable?"
    ]
  },
  {
    id: 10,
    title: "Versioning & Compatibility",
    emoji: "🔄",
    questions: [
      "Will this break existing clients or consumers?",
      "Should we version the API or keep backward compatibility?",
      "Do we need to support old schema/data alongside new one?"
    ]
  },
  {
    id: 11,
    title: "Codebase & Conventions",
    emoji: "🧭",
    questions: [
      "Is there an existing codebase or should we start fresh?",
      "What languages, frameworks, or libraries should we use?",
      "Are there style guides, linting, or coding standards to follow?",
      "Are there known tech debt areas to avoid or refactor?"
    ]
  }
];

// Flat arrays for easy filtering and categorization
export const ALL_FUNCTIONAL_QUESTIONS = FUNCTIONAL_QUESTIONS.flatMap(section => section.questions);
export const ALL_TECHNICAL_QUESTIONS = TECHNICAL_QUESTIONS.flatMap(section => section.questions); 