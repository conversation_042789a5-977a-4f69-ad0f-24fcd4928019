export interface LocationState {
  prompt?: string;
  // Legacy format for backward compatibility
  functionalAnswers?: { [key: string]: string };
  technicalAnswers?: { [key: string]: string };
  // New format
  user?: { id?: string; email?: string };
  projectData?: {
    technical: { [sectionName: string]: Array<{ [questionName: string]: string }> };
    functional: { [sectionName: string]: Array<{ [questionName: string]: string }> };
  };
}

export interface QuestionSection {
  id: number;
  title: string;
  emoji: string;
  questions: string[];
}

export interface StepProps {
  answers: { [key: string]: string };
  onAnswerChange: (questionKey: string, value: string) => void;
  initialSection?: number | null;
}

export interface ReviewStepProps extends Omit<StepProps, 'onAnswerChange'> {
  userPrompt: string;
} 