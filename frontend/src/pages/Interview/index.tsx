import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Stepper,
  Step,
  StepLabel,
  Button,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  Create as CreateIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import Header from '../../components/Header';
import { interviewAPI } from '../../utils/api';
import { FunctionalStep, TechnicalStep, ReviewStep } from './modules';
import { LocationState } from './types';
import { STEPS, STEP_INDICES } from './constants';
import { ALL_FUNCTIONAL_QUESTIONS, ALL_TECHNICAL_QUESTIONS } from './questions';

const Interview: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { uuid } = useParams<{ uuid?: string }>();
  const [activeStep, setActiveStep] = useState(0);
  const [answers, setAnswers] = useState<{ [key: string]: string }>({});
  const [targetSection, setTargetSection] = useState<number | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(!!uuid);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(!!uuid);

  // Get the prompt from location state (passed from MainPage)
  const locationState = location.state as LocationState;
  const [userPrompt, setUserPrompt] = useState(locationState?.prompt || "Create a todo app with React");

  // Load existing interview data when UUID is provided
  useEffect(() => {
    const loadExistingData = async () => {
      if (!uuid) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        
        // First try to use data from location state (faster)
        if (locationState?.functionalAnswers || locationState?.technicalAnswers) {
          const combinedAnswers = {
            ...locationState.functionalAnswers,
            ...locationState.technicalAnswers
          };
          setAnswers(combinedAnswers);
          if (locationState.prompt) {
            setUserPrompt(locationState.prompt);
          }
          setIsLoading(false);
          return;
        }

        // Fallback to API if no state data
        const response = await interviewAPI.get(uuid);
        
        if (response.success) {
          // Convert new schema format back to flat answers for the interview interface
          const { transformNewSchemaToFlatAnswers } = await import('../../utils/dataTransform');
          const { functionalAnswers, technicalAnswers } = transformNewSchemaToFlatAnswers(response.data.projectData);
          
          const combinedAnswers = {
            ...functionalAnswers,
            ...technicalAnswers
          };
          setAnswers(combinedAnswers);
          if (response.data.user?.prompt) {
            setUserPrompt(response.data.user.prompt);
          }
        } else {
          setError('Failed to load interview configuration');
        }
      } catch (error: any) {
        console.error('Error loading interview data:', error);
        setError(error.response?.data?.message || 'Failed to load interview configuration');
      } finally {
        setIsLoading(false);
      }
    };

    loadExistingData();
  }, [uuid, locationState]);

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
    setTargetSection(null);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
    setTargetSection(null);
  };

  const handleNavigateToStep = (stepIndex: number, sectionIndex?: number) => {
    setActiveStep(stepIndex);
    setTargetSection(sectionIndex ?? null);
  };

  const handleAnswerChange = (questionKey: string, value: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionKey]: value
    }));
  };

  const handleCreate = async () => {
    setIsSaving(true);
    
    try {
      // Import the transform function
      const { transformAnswersToNewSchema, transformNewSchemaToFlatAnswers } = await import('../../utils/dataTransform');
      
      // Prepare user data
      const userData = {
        id: user?.id,
        email: user?.email,
        prompt: userPrompt
      };

      // Transform answers to new schema format
      const newSchemaData = transformAnswersToNewSchema(answers, userData);
    
      
      // Also keep old format for backward compatibility
      const { functionalAnswers, technicalAnswers } = transformNewSchemaToFlatAnswers(newSchemaData.projectData);

      // Save interview configuration to backend with new schema
      const response = await interviewAPI.save({
        user: newSchemaData.user,
        projectData: newSchemaData.projectData,
        ...(isEditing && uuid && { uuid })
      });

      if (response.success) {
        // Get the target UUID (use existing UUID if editing, or new UUID if creating)
        const targetUuid = isEditing && uuid ? uuid : response.data.uuid;

        // Navigate to Build page immediately for better UX
        // The build page will handle the unified build API call and show loading states
        console.log('Interview saved successfully, navigating to build page...');
        navigate(`/build/${targetUuid}`, {
          state: {
            prompt: userPrompt,
            functionalAnswers,
            technicalAnswers,
            // Include new schema data
            user: newSchemaData.user,
            projectData: newSchemaData.projectData,
            projectDataOld: {
              requirements: isEditing ? 'Updated from interview process' : 'Generated from interview process'
            },
            // Flag to indicate this is a fresh build that needs to be started
            shouldStartBuild: true
          }
        });
      } else {
        console.error('Failed to save interview configuration');
        // Fallback to old navigation without UUID
        navigate('/build', { 
          state: { 
            prompt: userPrompt,
            functionalAnswers,
            technicalAnswers,
            projectData: {
              requirements: 'Generated from interview process'
            }
          } 
        });
      }
    } catch (error) {
      console.error('Error saving interview configuration:', error);
      // Fallback to old navigation without UUID
      const functionalAnswers = Object.keys(answers)
        .filter(key => ALL_FUNCTIONAL_QUESTIONS.includes(key))
        .reduce((obj, key) => {
          obj[key] = answers[key];
          return obj;
        }, {} as { [key: string]: string });

      const technicalAnswers = Object.keys(answers)
        .filter(key => ALL_TECHNICAL_QUESTIONS.includes(key))
        .reduce((obj, key) => {
          obj[key] = answers[key];
          return obj;
        }, {} as { [key: string]: string });

      navigate('/build', { 
        state: { 
          prompt: userPrompt,
          functionalAnswers,
          technicalAnswers,
          projectData: {
            requirements: 'Generated from interview process'
          }
        } 
      });
    } finally {
      setIsSaving(false);
    }
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case STEP_INDICES.FUNCTIONAL:
        return (
          <FunctionalStep 
            answers={answers} 
            onAnswerChange={handleAnswerChange}
            initialSection={targetSection}
          />
        );
      case STEP_INDICES.TECHNICAL:
        return (
          <TechnicalStep 
            answers={answers} 
            onAnswerChange={handleAnswerChange}
            initialSection={targetSection}
          />
        );
      case STEP_INDICES.REVIEW:
        return (
          <ReviewStep 
            answers={answers} 
            userPrompt={userPrompt}
            onNavigateToStep={handleNavigateToStep}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <Header />

      <Box
        sx={{
          minHeight: 'calc(100vh - 64px)',
          background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
          py: 4
        }}
      >
        {/* Loading State */}
        {isLoading && (
          <Container maxWidth="lg">
            <Paper elevation={6} sx={{ borderRadius: 4, p: 4, textAlign: 'center' }}>
              <CircularProgress size={60} sx={{ color: '#667eea', mb: 2 }} />
              <Typography variant="h6" sx={{ mb: 1 }}>
                Loading Interview Configuration...
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {uuid ? `Loading interview data for ID: ${uuid}` : 'Preparing interview...'}
              </Typography>
            </Paper>
          </Container>
        )}

        {/* Error State */}
        {error && !isLoading && (
          <Container maxWidth="lg">
            <Paper elevation={6} sx={{ borderRadius: 4, p: 4 }}>
              <Alert severity="error" sx={{ mb: 2 }}>
                <Typography variant="h6" sx={{ mb: 1 }}>
                  Failed to Load Interview Configuration
                </Typography>
                <Typography variant="body2">
                  {error}
                </Typography>
              </Alert>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                <Button
                  variant="outlined"
                  startIcon={<ArrowBackIcon />}
                  onClick={() => navigate('/')}
                >
                  Back to Home
                </Button>
                <Button
                  variant="contained"
                  onClick={() => window.location.reload()}
                  sx={{ bgcolor: '#667eea', '&:hover': { bgcolor: '#5a6fd8' } }}
                >
                  Retry
                </Button>
              </Box>
            </Paper>
          </Container>
        )}

        {/* Main Content */}
        {!isLoading && !error && (
        <Container maxWidth="lg">
          <Paper
            elevation={6}
            sx={{
              borderRadius: 4,
              overflow: 'hidden',
              background: 'rgba(255,255,255,0.95)',
              backdropFilter: 'blur(10px)',
              p: 4
            }}
          >
            {/* Header */}
            <Box sx={{ mb: 4, textAlign: 'center' }}>
              <Typography
                variant="h3"
                component="h1"
                sx={{
                  color: '#667eea',
                  fontWeight: 'bold',
                  mb: 1
                }}
              >
                {isEditing ? 'Edit Interview' : 'Project Interview'}
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  color: '#666',
                  fontWeight: 300,
                  mb: 2
                }}
              >
                {isEditing 
                  ? 'Update your project requirements and rebuild' 
                  : 'Answer questions to define your project requirements'
                }
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: '#888',
                  fontStyle: 'italic'
                }}
              >
                {isEditing ? 'Editing project: ' : 'Original idea: '}"{userPrompt}"
              </Typography>
              {isEditing && (
                <Typography
                  variant="caption"
                  sx={{
                    color: '#667eea',
                    fontWeight: 'medium',
                    display: 'block',
                    mt: 1
                  }}
                >
                  Project ID: {uuid}
                </Typography>
              )}
            </Box>

            {/* Stepper */}
            <Stepper 
              activeStep={activeStep} 
              sx={{ 
                mb: 4,
                '& .MuiStep-root': {
                  '& .MuiStepLabel-root': {
                    padding: '16px 8px',
                    cursor: 'pointer',
                    '&:hover': {
                      '& .MuiStepLabel-label': {
                        color: '#667eea'
                      },
                      '& .MuiStepIcon-root': {
                        transform: 'scale(1.1)',
                        transition: 'transform 0.2s ease-in-out'
                      }
                    }
                  }
                },
                '& .MuiStepIcon-root': {
                  fontSize: '2rem',
                  transition: 'all 0.3s ease-in-out',
                  '&.Mui-completed': {
                    color: '#4caf50',
                    fontSize: '2.2rem'
                  },
                  '&.Mui-active': {
                    color: '#667eea',
                    fontSize: '2.5rem',
                    filter: 'drop-shadow(0 0 8px rgba(102, 126, 234, 0.4))'
                  }
                },
                '& .MuiStepConnector-line': {
                  borderTopWidth: '3px'
                }
              }}
            >
              {STEPS.map((label, index) => (
                <Step key={label}>
                  <StepLabel
                    onClick={() => handleNavigateToStep(index)}
                    sx={{
                      '& .MuiStepLabel-label': {
                        fontSize: activeStep === index ? '1.2rem' : '1.1rem',
                        fontWeight: activeStep === index ? 'bold' : 'medium',
                        color: activeStep === index ? '#667eea' : undefined,
                        transition: 'all 0.3s ease-in-out',
                        cursor: 'pointer'
                      },
                      '& .MuiStepLabel-labelContainer': {
                        marginTop: '8px'
                      }
                    }}
                  >
                    {label}
                  </StepLabel>
                </Step>
              ))}
            </Stepper>

            {/* Step Content */}
            {renderStepContent()}

            {/* Main Navigation Buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
              <Button
                onClick={() => navigate('/')}
                startIcon={<ArrowBackIcon />}
                sx={{ color: '#667eea' }}
              >
                Back to Home
              </Button>

              <Box>
                <Button
                  disabled={activeStep === 0}
                  onClick={handleBack}
                  startIcon={<ArrowBackIcon />}
                  sx={{ mr: 1, color: '#667eea' }}
                >
                  Back
                </Button>
                <Button
                  onClick={activeStep === STEPS.length - 1 ? handleCreate : handleNext}
                  endIcon={activeStep === STEPS.length - 1 ? 
                    (isSaving ? <CircularProgress size={20} color="inherit" /> : <CreateIcon />) : 
                    <ArrowForwardIcon />
                  }
                  variant="contained"
                  disabled={isSaving}
                  sx={{
                    bgcolor: '#667eea',
                    '&:hover': {
                      bgcolor: '#5a6fd8'
                    },
                    '&:disabled': {
                      bgcolor: '#ccc'
                    }
                  }}
                >
                  {activeStep === STEPS.length - 1 ? 
                    (isSaving ? 'Saving...' : 'Build') : 
                    'Next Step'
                  }
                </Button>
              </Box>
            </Box>
          </Paper>
        </Container>
        )}
      </Box>
    </>
  );
};

export default Interview; 