import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>Content,
  Typography,
  Box,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  <PERSON>,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as EmptyCircleIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { ReviewStepProps, QuestionSection } from '../types';
import { FUNCTIONAL_QUESTIONS, TECHNICAL_QUESTIONS } from '../questions';

interface ReviewStepEnhancedProps extends ReviewStepProps {
  onNavigateToStep?: (stepIndex: number, sectionIndex?: number) => void;
}


const ReviewStep: React.FC<ReviewStepEnhancedProps> = ({ answers, userPrompt, onNavigateToStep }) => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const getFunctionalAnswers = () => {
    const functionalQuestionTexts = FUNCTIONAL_QUESTIONS.flatMap(section => section.questions);
    return Object.keys(answers)
      .filter(key => functionalQuestionTexts.includes(key))
      .reduce((obj, key) => {
        obj[key] = answers[key];
        return obj;
      }, {} as { [key: string]: string });
  };

  const getTechnicalAnswers = () => {
    const technicalQuestionTexts = TECHNICAL_QUESTIONS.flatMap(section => section.questions);
    return Object.keys(answers)
      .filter(key => technicalQuestionTexts.includes(key))
      .reduce((obj, key) => {
        obj[key] = answers[key];
        return obj;
      }, {} as { [key: string]: string });
  };

  const renderSectionReview = (sections: QuestionSection[], answerPrefix: string, sectionAnswers: { [key: string]: string }) => {
    const totalQuestions = sections.reduce((sum, section) => sum + section.questions.length, 0);
    const answeredQuestions = Object.keys(sectionAnswers).filter(key => sectionAnswers[key]?.trim()).length;
    const completionRate = totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;

    return (
      <Box>
        {/* Summary Header */}
        <Box sx={{ mb: 3, p: 2, backgroundColor: '#f8f9fa', borderRadius: 2 }}>
          <Typography variant="h6" sx={{ mb: 1, color: '#667eea', fontWeight: 'bold' }}>
            Summary
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
            <Chip 
              label={`${answeredQuestions}/${totalQuestions} Questions Answered`}
              color={completionRate === 100 ? 'success' : 'warning'}
              variant="outlined"
            />
            <Chip 
              label={`${completionRate}% Complete`}
              color={completionRate === 100 ? 'success' : completionRate > 50 ? 'warning' : 'error'}
            />
          </Box>
        </Box>

        {/* Sections */}
        {sections.map((section) => {
          const sectionQuestionAnswers = section.questions.map((question) => {
            return sectionAnswers[question] || '';
          });
          
          const answeredCount = sectionQuestionAnswers.filter(answer => answer.trim()).length;
          const isComplete = answeredCount === section.questions.length;

          return (
            <Accordion key={section.id} sx={{ mb: 1 }}>
              <AccordionSummary 
                expandIcon={<ExpandMoreIcon />}
                sx={{
                  '&:hover .edit-button': {
                    opacity: 1
                  }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {isComplete ? (
                      <CheckCircleIcon sx={{ color: 'success.main', fontSize: '1.2rem' }} />
                    ) : (
                      <EmptyCircleIcon sx={{ color: 'warning.main', fontSize: '1.2rem' }} />
                    )}
                    <span style={{ fontSize: '1.2em' }}>{section.emoji}</span>
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="h6" sx={{ fontWeight: 'medium' }}>
                      {section.title}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {answeredCount}/{section.questions.length} questions answered
                    </Typography>
                  </Box>
                  <Tooltip title="Edit this section">
                    <IconButton
                      className="edit-button"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onNavigateToStep) {
                          const stepIndex = answerPrefix === 'technical_' ? 1 : 0;
                          onNavigateToStep(stepIndex, section.id - 1);
                        }
                      }}
                      sx={{
                        opacity: 0,
                        transition: 'opacity 0.2s ease-in-out',
                        color: '#667eea',
                        '&:hover': {
                          backgroundColor: 'rgba(102, 126, 234, 0.1)'
                        }
                      }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              </AccordionSummary>
              <AccordionDetails sx={{ pt: 0 }}>
                <Box sx={{ pl: 2 }}>
                  {section.questions.map((question, index) => {
                    const questionKey = question; // Use actual question text as key
                    const answer = sectionAnswers[questionKey] || '';
                    const hasAnswer = answer.trim().length > 0;

                    return (
                      <Box key={index} sx={{ mb: 2 }}>
                        <Typography 
                          variant="subtitle2" 
                          sx={{ 
                            mb: 1, 
                            color: '#333',
                            fontWeight: 'medium',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1
                          }}
                        >
                          {hasAnswer ? (
                            <CheckCircleIcon sx={{ color: 'success.main', fontSize: '1rem' }} />
                          ) : (
                            <EmptyCircleIcon sx={{ color: 'warning.main', fontSize: '1rem' }} />
                          )}
                          {index + 1}. {question}
                        </Typography>
                        <Box 
                          sx={{ 
                            ml: 3,
                            p: 1.5, 
                            backgroundColor: hasAnswer ? '#f8f9fa' : '#fff3cd',
                            borderRadius: 1,
                            border: hasAnswer ? '1px solid #e9ecef' : '1px solid #ffeaa7',
                            minHeight: '40px',
                            display: 'flex',
                            alignItems: 'center'
                          }}
                        >
                          {hasAnswer ? (
                            <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                              {answer}
                            </Typography>
                          ) : (
                            <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                              No answer provided
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    );
                  })}
                </Box>
              </AccordionDetails>
            </Accordion>
          );
        })}
      </Box>
    );
  };

  const functionalAnswers = getFunctionalAnswers();
  const technicalAnswers = getTechnicalAnswers();

  return (
    <Card sx={{ mt: 3, minHeight: 500 }}>
      <CardContent sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3, textAlign: 'center' }}>
          <Typography variant="h4" sx={{ color: '#667eea', fontWeight: 'bold', mb: 1 }}>
            📋 Review Your Answers
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
            Review all your responses before proceeding to build
          </Typography>
          {/* <Typography variant="body2" sx={{ color: '#888', fontStyle: 'italic' }}>
            Original idea: "{userPrompt}"
          </Typography> */}
        </Box>

        <Divider sx={{ mb: 3 }} />

        {/* Review Tabs */}
        <Box sx={{ mb: 4 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{
              '& .MuiTabs-indicator': {
                backgroundColor: '#667eea',
                height: '4px',
                borderRadius: '2px'
              },
              '& .MuiTabs-flexContainer': {
                gap: 2
              }
            }}
          >
            <Tab 
              label={
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: 'column', 
                  alignItems: 'center', 
                  gap: 1.5,
                  py: 2
                }}>
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: 1.5,
                    fontSize: '1.2rem'
                  }}>
                    <span style={{ fontSize: '1.8rem' }}>🔍</span>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
                      Functional Requirements
                    </Typography>
                  </Box>
                  <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '0.85rem' }}>
                    Business logic, user needs, and feature requirements
                  </Typography>
                </Box>
              }
              sx={{
                textTransform: 'none',
                minHeight: '100px',
                backgroundColor: activeTab === 0 ? 'rgba(102, 126, 234, 0.08)' : 'rgba(0, 0, 0, 0.02)',
                borderRadius: '12px 12px 0 0',
                border: activeTab === 0 ? '2px solid rgba(102, 126, 234, 0.3)' : '2px solid transparent',
                borderBottom: 'none',
                margin: '0 4px',
                transition: 'all 0.3s ease-in-out',
                '&.Mui-selected': {
                  color: '#667eea',
                  backgroundColor: 'rgba(102, 126, 234, 0.12)',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 12px rgba(102, 126, 234, 0.2)'
                },
                '&:hover': {
                  backgroundColor: activeTab === 0 ? 'rgba(102, 126, 234, 0.12)' : 'rgba(102, 126, 234, 0.04)',
                  transform: 'translateY(-1px)'
                }
              }}
            />
            <Tab 
              label={
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: 'column', 
                  alignItems: 'center', 
                  gap: 1.5,
                  py: 2
                }}>
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: 1.5,
                    fontSize: '1.2rem'
                  }}>
                    <span style={{ fontSize: '1.8rem' }}>🔧</span>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
                      Technical Specifications
                    </Typography>
                  </Box>
                  <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '0.85rem' }}>
                    Architecture, implementation, and system design
                  </Typography>
                </Box>
              }
              sx={{
                textTransform: 'none',
                minHeight: '100px',
                backgroundColor: activeTab === 1 ? 'rgba(102, 126, 234, 0.08)' : 'rgba(0, 0, 0, 0.02)',
                borderRadius: '12px 12px 0 0',
                border: activeTab === 1 ? '2px solid rgba(102, 126, 234, 0.3)' : '2px solid transparent',
                borderBottom: 'none',
                margin: '0 4px',
                transition: 'all 0.3s ease-in-out',
                '&.Mui-selected': {
                  color: '#667eea',
                  backgroundColor: 'rgba(102, 126, 234, 0.12)',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 12px rgba(102, 126, 234, 0.2)'
                },
                '&:hover': {
                  backgroundColor: activeTab === 1 ? 'rgba(102, 126, 234, 0.12)' : 'rgba(102, 126, 234, 0.04)',
                  transform: 'translateY(-1px)'
                }
              }}
            />
          </Tabs>
        </Box>

        {/* Tab Content */}
        {activeTab === 0 && renderSectionReview(FUNCTIONAL_QUESTIONS, '', functionalAnswers)}
        {activeTab === 1 && renderSectionReview(TECHNICAL_QUESTIONS, 'technical_', technicalAnswers)}
      </CardContent>
    </Card>
  );
};

export default ReviewStep; 