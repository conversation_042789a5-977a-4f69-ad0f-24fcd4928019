import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  <PERSON>,
  Button,
  TextField,
  LinearProgress,
  Fade,
  Chip,
  Tabs,
  Tab
} from '@mui/material';
import {
  NavigateNext as NavigateNextIcon,
  NavigateBefore as NavigateBeforeIcon
} from '@mui/icons-material';
import { QuestionSection, StepProps } from '../types';
import { FUNCTIONAL_QUESTIONS } from '../questions';

const FunctionalStep: React.FC<StepProps> = ({ answers, onAnswerChange, initialSection }) => {
  const [currentSectionIndex, setCurrentSectionIndex] = useState(initialSection ?? 0);

  useEffect(() => {
    if (initialSection !== null && initialSection !== undefined) {
      setCurrentSectionIndex(initialSection);
    }
  }, [initialSection]);

  const handleNextSection = () => {
    if (currentSectionIndex < FUNCTIONAL_QUESTIONS.length - 1) {
      setCurrentSectionIndex(prev => prev + 1);
    }
  };

  const handlePrevSection = () => {
    if (currentSectionIndex > 0) {
      setCurrentSectionIndex(prev => prev - 1);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentSectionIndex(newValue);
  };

  const currentSection = FUNCTIONAL_QUESTIONS[currentSectionIndex];
  const progress = ((currentSectionIndex + 1) / FUNCTIONAL_QUESTIONS.length) * 100;

  return (
    <Fade in={true} timeout={300}>
      <Card sx={{ mt: 3, minHeight: 500 }}>
        <CardContent sx={{ p: 4 }}>
          {/* Progress */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Section {currentSectionIndex + 1} of {FUNCTIONAL_QUESTIONS.length}
              </Typography>
              {/* <Chip 
                label={`${Math.round(progress)}% Complete`} 
                size="small" 
                color="primary" 
                variant="outlined"
              /> */}
            </Box>
            <LinearProgress 
              variant="determinate" 
              value={progress} 
              sx={{ 
                height: 8, 
                borderRadius: 4,
                backgroundColor: '#e3f2fd',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: '#667eea'
                }
              }} 
            />
          </Box>

          {/* Section Tabs */}
          <Box sx={{ mb: 3 }}>
            <Tabs
              value={currentSectionIndex}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                '& .MuiTabs-flexContainer': {
                  gap: 1
                },
                '& .MuiTab-root': {
                  minWidth: 'auto',
                  padding: '8px 12px',
                  fontSize: '0.875rem',
                  fontWeight: 'medium',
                  textTransform: 'none',
                  color: '#666',
                  '&.Mui-selected': {
                    color: '#667eea',
                    fontWeight: 'bold'
                  }
                },
                '& .MuiTabs-indicator': {
                  backgroundColor: '#667eea'
                }
              }}
            >
              {FUNCTIONAL_QUESTIONS.map((section, index) => (
                <Tab
                  key={section.id}
                  label={
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <span>{section.emoji}</span>
                        <span>{section.id}</span>
                      </Box>
                      <Typography variant="caption" sx={{ fontSize: '0.75rem', textAlign: 'center', lineHeight: 1.2 }}>
                        {section.title}
                      </Typography>
                    </Box>
                  }
                  sx={{
                    backgroundColor: currentSectionIndex === index ? 'rgba(102, 126, 234, 0.08)' : 'transparent',
                    borderRadius: 1,
                    border: currentSectionIndex === index ? '1px solid rgba(102, 126, 234, 0.2)' : '1px solid transparent',
                    minHeight: '60px',
                    maxWidth: '140px'
                  }}
                />
              ))}
            </Tabs>
          </Box>

          {/* Section Header */}
          <Box sx={{ mb: 4, textAlign: 'center' }}>
            <Typography 
              variant="h4" 
              component="h2" 
              sx={{ 
                color: '#667eea', 
                fontWeight: 'bold',
                mb: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 2
              }}
            >
              <span style={{ fontSize: '1.5em' }}>{currentSection.emoji}</span>
              {currentSection.title}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Please answer the following questions to help us understand your requirements
            </Typography>
          </Box>

          {/* Questions */}
          <Box sx={{ mb: 4 }}>
            {currentSection.questions.map((question, index) => {
              const questionKey = question; // Use the actual question text as the key
              return (
                <Box key={index} sx={{ mb: 3 }}>
                  <Typography 
                    variant="h6" 
                    sx={{ 
                      mb: 2, 
                      color: '#333',
                      fontWeight: 'medium'
                    }}
                  >
                    {index + 1}. {question}
                  </Typography>
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    variant="outlined"
                    placeholder="Enter your answer here..."
                    value={answers[questionKey] || ''}
                    onChange={(e) => onAnswerChange(questionKey, e.target.value)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: '#f8f9fa',
                        '&:hover': {
                          backgroundColor: '#f1f3f4',
                        },
                        '&.Mui-focused': {
                          backgroundColor: '#fff',
                        }
                      }
                    }}
                  />
                </Box>
              );
            })}
          </Box>

          {/* Section Navigation */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
            <Button
              onClick={handlePrevSection}
              disabled={currentSectionIndex === 0}
              startIcon={<NavigateBeforeIcon />}
              variant="outlined"
              sx={{ 
                color: '#667eea',
                borderColor: '#667eea',
                '&:hover': {
                  borderColor: '#5a6fd8',
                  backgroundColor: 'rgba(102, 126, 234, 0.04)'
                }
              }}
            >
              Previous Section
            </Button>

            <Button
              onClick={handleNextSection}
              disabled={currentSectionIndex === FUNCTIONAL_QUESTIONS.length - 1}
              endIcon={<NavigateNextIcon />}
              variant="contained"
              sx={{
                bgcolor: '#667eea',
                '&:hover': {
                  bgcolor: '#5a6fd8'
                }
              }}
            >
              Next Section
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Fade>
  );
};

export default FunctionalStep; 