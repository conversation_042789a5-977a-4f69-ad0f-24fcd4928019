/**
 * Workspace persistence utilities for Sphere Engine workspaces
 * Handles storing and retrieving workspace IDs with user-specific keys
 */

export interface PersistedWorkspace {
  id: string;
  projectId: string;
  createdAt: string;
  lastAccessedAt: string;
  status?: 'starting' | 'running' | 'stopped' | 'error';
}

export interface WorkspacePersistenceConfig {
  maxAge?: number; // Maximum age in milliseconds (default: 24 hours)
  storageKey?: string; // Custom storage key prefix
}

class WorkspacePersistenceManager {
  private readonly DEFAULT_MAX_AGE = 24 * 60 * 60 * 1000; // 24 hours
  private readonly DEFAULT_STORAGE_KEY = 'sphere_engine_workspaces';
  
  private config: Required<WorkspacePersistenceConfig>;

  constructor(config: WorkspacePersistenceConfig = {}) {
    this.config = {
      maxAge: config.maxAge || this.DEFAULT_MAX_AGE,
      storageKey: config.storageKey || this.DEFAULT_STORAGE_KEY
    };
  }

  /**
   * Get storage key for current user
   */
  private getUserStorageKey(): string {
    // Get user info from localStorage (set by AuthContext)
    const userStr = localStorage.getItem('user');
    if (!userStr) {
      throw new Error('User not authenticated');
    }
    
    const user = JSON.parse(userStr);
    return `${this.config.storageKey}_${user.id}`;
  }

  /**
   * Get all persisted workspaces for current user
   */
  getPersistedWorkspaces(): PersistedWorkspace[] {
    try {
      const storageKey = this.getUserStorageKey();
      const workspacesStr = localStorage.getItem(storageKey);
      
      if (!workspacesStr) {
        return [];
      }

      const workspaces: PersistedWorkspace[] = JSON.parse(workspacesStr);
      const now = Date.now();

      // Filter out expired workspaces
      const validWorkspaces = workspaces.filter(workspace => {
        const createdAt = new Date(workspace.createdAt).getTime();
        return (now - createdAt) < this.config.maxAge;
      });

      // Update storage if we filtered out any workspaces
      if (validWorkspaces.length !== workspaces.length) {
        this.saveWorkspaces(validWorkspaces);
      }

      return validWorkspaces;
    } catch (error) {
      console.error('Error getting persisted workspaces:', error);
      return [];
    }
  }

  /**
   * Get workspace by project ID
   */
  getWorkspaceByProjectId(projectId: string): PersistedWorkspace | null {
    const workspaces = this.getPersistedWorkspaces();
    return workspaces.find(w => w.projectId === projectId) || null;
  }

  /**
   * Save workspace to persistence
   */
  saveWorkspace(workspace: Omit<PersistedWorkspace, 'createdAt' | 'lastAccessedAt'>): void {
    try {
      const workspaces = this.getPersistedWorkspaces();
      const now = new Date().toISOString();
      
      // Remove existing workspace with same project ID
      const filteredWorkspaces = workspaces.filter(w => w.projectId !== workspace.projectId);
      
      // Add new workspace
      const newWorkspace: PersistedWorkspace = {
        ...workspace,
        createdAt: now,
        lastAccessedAt: now
      };
      
      filteredWorkspaces.push(newWorkspace);
      this.saveWorkspaces(filteredWorkspaces);
      
      console.log('Workspace saved to persistence:', newWorkspace);
    } catch (error) {
      console.error('Error saving workspace:', error);
    }
  }

  /**
   * Update workspace last accessed time and status
   */
  updateWorkspaceAccess(workspaceId: string, status?: PersistedWorkspace['status']): void {
    try {
      const workspaces = this.getPersistedWorkspaces();
      const workspaceIndex = workspaces.findIndex(w => w.id === workspaceId);
      
      if (workspaceIndex !== -1) {
        workspaces[workspaceIndex].lastAccessedAt = new Date().toISOString();
        if (status) {
          workspaces[workspaceIndex].status = status;
        }
        this.saveWorkspaces(workspaces);
      }
    } catch (error) {
      console.error('Error updating workspace access:', error);
    }
  }

  /**
   * Remove workspace from persistence
   */
  removeWorkspace(workspaceId: string): void {
    try {
      const workspaces = this.getPersistedWorkspaces();
      const filteredWorkspaces = workspaces.filter(w => w.id !== workspaceId);
      this.saveWorkspaces(filteredWorkspaces);
      
      console.log('Workspace removed from persistence:', workspaceId);
    } catch (error) {
      console.error('Error removing workspace:', error);
    }
  }

  /**
   * Clear all persisted workspaces for current user
   */
  clearAllWorkspaces(): void {
    try {
      const storageKey = this.getUserStorageKey();
      localStorage.removeItem(storageKey);
      console.log('All workspaces cleared from persistence');
    } catch (error) {
      console.error('Error clearing workspaces:', error);
    }
  }

  /**
   * Save workspaces array to localStorage
   */
  private saveWorkspaces(workspaces: PersistedWorkspace[]): void {
    try {
      const storageKey = this.getUserStorageKey();
      localStorage.setItem(storageKey, JSON.stringify(workspaces));
    } catch (error) {
      console.error('Error saving workspaces to localStorage:', error);
    }
  }

  /**
   * Check if workspace is expired based on age
   */
  isWorkspaceExpired(workspace: PersistedWorkspace): boolean {
    const now = Date.now();
    const createdAt = new Date(workspace.createdAt).getTime();
    return (now - createdAt) > this.config.maxAge;
  }

  /**
   * Check if workspace was recently accessed (within last hour)
   */
  isWorkspaceRecentlyAccessed(workspace: PersistedWorkspace): boolean {
    const now = Date.now();
    const lastAccessed = new Date(workspace.lastAccessedAt).getTime();
    const oneHour = 60 * 60 * 1000; // 1 hour
    return (now - lastAccessed) < oneHour;
  }

  /**
   * Get workspace statistics
   */
  getWorkspaceStats(): { total: number; byStatus: Record<string, number>; expired: number; recentlyAccessed: number } {
    const workspaces = this.getPersistedWorkspaces();
    const stats = {
      total: workspaces.length,
      byStatus: {} as Record<string, number>,
      expired: 0,
      recentlyAccessed: 0
    };

    workspaces.forEach(workspace => {
      const status = workspace.status || 'unknown';
      stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;

      if (this.isWorkspaceExpired(workspace)) {
        stats.expired++;
      }

      if (this.isWorkspaceRecentlyAccessed(workspace)) {
        stats.recentlyAccessed++;
      }
    });

    return stats;
  }

  /**
   * Clean up expired workspaces
   */
  cleanupExpiredWorkspaces(): number {
    const workspaces = this.getPersistedWorkspaces();
    const validWorkspaces = workspaces.filter(workspace => !this.isWorkspaceExpired(workspace));
    const removedCount = workspaces.length - validWorkspaces.length;

    if (removedCount > 0) {
      this.saveWorkspaces(validWorkspaces);
      console.log(`Cleaned up ${removedCount} expired workspaces`);
    }

    return removedCount;
  }
}

// Export singleton instance
export const workspacePersistence = new WorkspacePersistenceManager();

// Export utility functions for convenience
export const {
  getPersistedWorkspaces,
  getWorkspaceByProjectId,
  saveWorkspace,
  updateWorkspaceAccess,
  removeWorkspace,
  clearAllWorkspaces,
  getWorkspaceStats
} = workspacePersistence;
