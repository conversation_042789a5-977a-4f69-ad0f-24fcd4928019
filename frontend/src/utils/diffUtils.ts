/**
 * Utility functions for generating and applying file diffs
 * to optimize API payloads by sending only changes instead of full file content
 */

export interface DiffOperation {
  type: 'insert' | 'delete' | 'replace';
  position: number;
  length?: number; // For delete and replace operations
  text?: string;   // For insert and replace operations
}

export interface FileChange {
  filePath: string;
  type: 'create' | 'modify' | 'delete';
  content?: string;     // For create operations
  operations?: DiffOperation[]; // For modify operations
}

/**
 * Generate diff operations between two strings
 */
export function generateDiff(oldContent: string, newContent: string): DiffOperation[] {
  const operations: DiffOperation[] = [];
  
  // Simple character-by-character diff algorithm
  // For production, consider using a more sophisticated algorithm like <PERSON>' diff
  
  let oldIndex = 0;
  let newIndex = 0;
  
  while (oldIndex < oldContent.length || newIndex < newContent.length) {
    // Find the next difference
    while (oldIndex < oldContent.length && 
           newIndex < newContent.length && 
           oldContent[oldIndex] === newContent[newIndex]) {
      oldIndex++;
      newIndex++;
    }
    
    if (oldIndex >= oldContent.length && newIndex >= newContent.length) {
      break; // No more differences
    }
    
    // Find the end of the current difference
    const diffStartOld = oldIndex;
    const diffStartNew = newIndex;
    
    // Look ahead to find matching content
    let matchFound = false;
    let lookAhead = 1;
    
    while (!matchFound && (diffStartOld + lookAhead <= oldContent.length || diffStartNew + lookAhead <= newContent.length)) {
      // Try to find a match in the next few characters
      for (let windowSize = 3; windowSize <= 10; windowSize++) {
        if (diffStartOld + lookAhead + windowSize <= oldContent.length &&
            diffStartNew + lookAhead + windowSize <= newContent.length) {
          
          const oldWindow = oldContent.slice(diffStartOld + lookAhead, diffStartOld + lookAhead + windowSize);
          const newWindow = newContent.slice(diffStartNew + lookAhead, diffStartNew + lookAhead + windowSize);
          
          if (oldWindow === newWindow) {
            matchFound = true;
            oldIndex = diffStartOld + lookAhead;
            newIndex = diffStartNew + lookAhead;
            break;
          }
        }
      }
      
      if (!matchFound) {
        lookAhead++;
      }
    }
    
    if (!matchFound) {
      // No match found, consume the rest
      oldIndex = oldContent.length;
      newIndex = newContent.length;
    }
    
    // Generate operation based on the difference
    const oldSegment = oldContent.slice(diffStartOld, oldIndex);
    const newSegment = newContent.slice(diffStartNew, newIndex);
    
    if (oldSegment.length === 0 && newSegment.length > 0) {
      // Insert operation
      operations.push({
        type: 'insert',
        position: diffStartOld,
        text: newSegment
      });
    } else if (oldSegment.length > 0 && newSegment.length === 0) {
      // Delete operation
      operations.push({
        type: 'delete',
        position: diffStartOld,
        length: oldSegment.length
      });
    } else if (oldSegment.length > 0 && newSegment.length > 0) {
      // Replace operation
      operations.push({
        type: 'replace',
        position: diffStartOld,
        length: oldSegment.length,
        text: newSegment
      });
    }
  }
  
  return operations;
}

/**
 * Generate file changes for modified files
 */
export function generateFileChanges(
  oldFiles: { [path: string]: string },
  newFiles: { [path: string]: string }
): FileChange[] {
  const changes: FileChange[] = [];
  const allPaths = new Set([...Object.keys(oldFiles), ...Object.keys(newFiles)]);
  
  for (const filePath of allPaths) {
    const oldContent = oldFiles[filePath];
    const newContent = newFiles[filePath];
    
    if (oldContent === undefined && newContent !== undefined) {
      // New file created
      changes.push({
        filePath,
        type: 'create',
        content: newContent
      });
    } else if (oldContent !== undefined && newContent === undefined) {
      // File deleted
      changes.push({
        filePath,
        type: 'delete'
      });
    } else if (oldContent !== newContent) {
      // File modified
      const operations = generateDiff(oldContent, newContent);
      if (operations.length > 0) {
        changes.push({
          filePath,
          type: 'modify',
          operations
        });
      }
    }
  }
  
  return changes;
}

/**
 * Calculate the payload size reduction
 */
export function calculatePayloadReduction(
  fullPayload: { [path: string]: string },
  changes: FileChange[]
): { originalSize: number; optimizedSize: number; reduction: number } {
  // Calculate original payload size (approximate)
  const originalSize = JSON.stringify(fullPayload).length;
  
  // Calculate optimized payload size (approximate)
  const optimizedSize = JSON.stringify(changes).length;
  
  const reduction = ((originalSize - optimizedSize) / originalSize) * 100;
  
  return {
    originalSize,
    optimizedSize,
    reduction: Math.max(0, reduction)
  };
}

/**
 * Check if using diff is beneficial (sometimes full content is smaller)
 */
export function shouldUseDiff(
  oldContent: string,
  newContent: string,
  threshold: number = 0.7 // Use diff if it reduces payload by at least 30%
): boolean {
  const operations = generateDiff(oldContent, newContent);
  const fullSize = newContent.length;
  const diffSize = JSON.stringify(operations).length;
  
  return (diffSize / fullSize) < threshold;
}

/**
 * Apply diff operations to content (client-side verification)
 */
export function applyDiffOperations(content: string, operations: DiffOperation[]): string {
  let result = content;
  
  // Sort operations by position (descending) to avoid offset issues
  const sortedOps = [...operations].sort((a, b) => b.position - a.position);
  
  for (const op of sortedOps) {
    const { type, position, length, text } = op;
    
    switch (type) {
      case 'insert':
        result = result.slice(0, position) + (text || '') + result.slice(position);
        break;
        
      case 'delete':
        result = result.slice(0, position) + result.slice(position + (length || 0));
        break;
        
      case 'replace':
        result = result.slice(0, position) + (text || '') + result.slice(position + (length || 0));
        break;
    }
  }
  
  return result;
}
