/**
 * Test file for diff utilities
 * Run with: npm test diffUtils.test.ts
 */

import { generateDiff, generateFileChanges, calculatePayloadReduction, shouldUseDiff, applyDiffOperations } from './diffUtils';

// Test data
const oldContent = `function hello() {
    console.log("Hello World!");
    return true;
}

const data = {
    name: "test",
    value: 42
};`;

const newContent = `function hello() {
    console.log("Hello Universe!");
    console.log("This is a new line");
    return true;
}

const data = {
    name: "test",
    value: 100,
    description: "Updated value"
};`;

// Test diff generation
console.log('=== Testing Diff Generation ===');
const operations = generateDiff(oldContent, newContent);
console.log('Generated operations:', operations);

// Test applying diff operations
console.log('\n=== Testing Diff Application ===');
const appliedContent = applyDiffOperations(oldContent, operations);
console.log('Applied content matches new content:', appliedContent === newContent);

if (appliedContent !== newContent) {
    console.log('Expected:', newContent);
    console.log('Got:', appliedContent);
}

// Test file changes generation
console.log('\n=== Testing File Changes Generation ===');
const oldFiles = {
    'src/index.js': oldContent,
    'src/utils.js': 'export const utils = {};',
    'README.md': '# Project\n\nThis is a test project.'
};

const newFiles = {
    'src/index.js': newContent,
    'src/utils.js': 'export const utils = {};\nexport const helpers = {};',
    'src/new-file.js': 'console.log("New file");',
    'README.md': '# Project\n\nThis is a test project.'
};

const changes = generateFileChanges(oldFiles, newFiles);
console.log('Generated file changes:', changes);

// Test payload reduction calculation
console.log('\n=== Testing Payload Reduction ===');
const payloadStats = calculatePayloadReduction(newFiles, changes);
console.log('Payload statistics:', {
    originalSize: `${(payloadStats.originalSize / 1024).toFixed(2)} KB`,
    optimizedSize: `${(payloadStats.optimizedSize / 1024).toFixed(2)} KB`,
    reduction: `${payloadStats.reduction.toFixed(1)}%`
});

// Test shouldUseDiff function
console.log('\n=== Testing Diff Benefit Analysis ===');
const shouldUse = shouldUseDiff(oldContent, newContent);
console.log('Should use diff:', shouldUse);

// Test edge cases
console.log('\n=== Testing Edge Cases ===');

// Empty to content
const emptyToContent = generateDiff('', 'Hello World');
console.log('Empty to content operations:', emptyToContent);

// Content to empty
const contentToEmpty = generateDiff('Hello World', '');
console.log('Content to empty operations:', contentToEmpty);

// No changes
const noChanges = generateDiff('Same content', 'Same content');
console.log('No changes operations:', noChanges);

// Small change
const smallChange = generateDiff('Hello World', 'Hello Universe');
console.log('Small change operations:', smallChange);

console.log('\n=== Test Complete ===');
