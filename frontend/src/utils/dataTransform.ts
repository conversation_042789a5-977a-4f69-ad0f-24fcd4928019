import { FUNCTIONAL_QUESTIONS, TECHNICAL_QUESTIONS } from '../pages/Interview/questions';

// Types for the new data schema
export interface QuestionAnswer {
  [questionName: string]: string;
}

export interface ProjectData {
  technical: {
    [sectionName: string]: QuestionAnswer[];
  };
  functional: {
    [sectionName: string]: QuestionAnswer[];
  };
}

export interface UserData {
  id?: string;
  email?: string;
  prompt?: string;
}

export interface NewSchemaData {
  user: UserData;
  projectData: ProjectData;
}

/**
 * Transform flat answers object into the new sectioned schema structure
 */
export const transformAnswersToNewSchema = (
  answers: { [key: string]: string },
  user: UserData
): NewSchemaData => {
  const projectData: ProjectData = {
    functional: {},
    technical: {}
  };

  // Process functional answers
  FUNCTIONAL_QUESTIONS.forEach(section => {
    const sectionName = section.title;
    const sectionAnswers: QuestionAnswer[] = [];
    
    section.questions.forEach(question => {
      if (answers[question] && answers[question].trim() !== '') {
        sectionAnswers.push({
          [question]: answers[question]
        });
      }
    });
    
    if (sectionAnswers.length > 0) {
      projectData.functional[sectionName] = sectionAnswers;
    }
  });

  // Process technical answers
  TECHNICAL_QUESTIONS.forEach(section => {
    const sectionName = section.title;
    const sectionAnswers: QuestionAnswer[] = [];
    
    section.questions.forEach(question => {
      if (answers[question] && answers[question].trim() !== '') {
        sectionAnswers.push({
          [question]: answers[question]
        });
      }
    });
    
    if (sectionAnswers.length > 0) {
      projectData.technical[sectionName] = sectionAnswers;
    }
  });

  return {
    user,
    projectData
  };
};

/**
 * Convert new schema back to flat answers for backward compatibility
 */
export const transformNewSchemaToFlatAnswers = (
  projectData: ProjectData
): { functionalAnswers: { [key: string]: string }, technicalAnswers: { [key: string]: string } } => {
  const functionalAnswers: { [key: string]: string } = {};
  const technicalAnswers: { [key: string]: string } = {};

  // Process functional sections
  Object.values(projectData.functional).forEach(sectionAnswers => {
    sectionAnswers.forEach(questionAnswer => {
      Object.entries(questionAnswer).forEach(([question, answer]) => {
        functionalAnswers[question] = answer;
      });
    });
  });

  // Process technical sections
  Object.values(projectData.technical).forEach(sectionAnswers => {
    sectionAnswers.forEach(questionAnswer => {
      Object.entries(questionAnswer).forEach(([question, answer]) => {
        technicalAnswers[question] = answer;
      });
    });
  });

  return { functionalAnswers, technicalAnswers };
}; 