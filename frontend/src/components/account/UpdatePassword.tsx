import { useState } from 'react';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { userAPI } from '../../utils/api';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';

type Props = {
	setShowAccountModal: (show: boolean) => void;
};

const UpdatePassword = ({ setShowAccountModal }: Props) => {
	const navigate = useNavigate();
	const { logout } = useAuth();
	const [isLoading, setIsLoading] = useState(false);
	const [feedback, setFeedback] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
	const [formData, setFormData] = useState({
		oldPassword: '',
		newPassword: '',
	});
	const [showOldPassword, setShowOldPassword] = useState(false);
	const [showNewPassword, setShowNewPassword] = useState(false);

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setFormData((prev) => ({
			...prev,
			[e.target.name]: e.target.value,
		}));
	};

	const handleSubmit = async () => {
		if (!formData.oldPassword || !formData.newPassword) {
			setFeedback({ type: 'error', text: 'Please fill in all fields' });
			return;
		}

		try {
			setIsLoading(true);
			setFeedback(null);
			await userAPI.updatePassword({ oldPassword: formData.oldPassword, newPassword: formData.newPassword });
			setFeedback({ type: 'success', text: 'Password updated successfully. Redirecting to login...' });
			// Give user a brief moment to read the message, then logout and redirect
			setTimeout(async () => {
				await logout();
				navigate('/login');
			}, 800);
		} catch (error: unknown) {
			setFeedback({ type: 'error', text: (error as any)?.response?.data?.message || 'Failed to update password' });
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="flex flex-col h-full">
			<div className="flex-1">
				<div className="flex justify-between px-6 my-8">
					<h2 className="text-3xl font-bold">Update Password</h2>
				</div>
				{feedback && (
					<div className={`px-6 text-sm ${feedback.type === 'success' ? 'text-green-600' : 'text-red-600'}`}>
						{feedback.text}
					</div>
				)}
				<div className="my-6 px-6">
					<div className="flex flex-col gap-5 my-4">
						<div className="relative">
							<input
								type={showOldPassword ? 'text' : 'password'}
								name="oldPassword"
								placeholder="Current Password"
								value={formData.oldPassword}
								onChange={handleChange}
								className="rounded-[10px] w-full p-4 text-sm bg-[#9C9C9C]/[0.28] backdrop-blur-[30.3px] text-black placeholder:text-black/70 outline-none"
							/>
							<button
								type="button"
								onClick={() => setShowOldPassword(!showOldPassword)}
								className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
							>
								{showOldPassword ? <EyeOutlined /> : <EyeInvisibleOutlined />}
							</button>
						</div>
						<div className="relative">
							<input
								type={showNewPassword ? 'text' : 'password'}
								name="newPassword"
								placeholder="New Password"
								value={formData.newPassword}
								onChange={handleChange}
								className="rounded-[10px] w-full p-4 text-sm bg-[#9C9C9C]/[0.28] backdrop-blur-[30.3px] text-black placeholder:text-black/70 outline-none"
							/>
							<button
								type="button"
								onClick={() => setShowNewPassword(!showNewPassword)}
								className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
							>
								{showNewPassword ? <EyeOutlined /> : <EyeInvisibleOutlined />}
							</button>
						</div>
					</div>
				</div>
			</div>
			<div className="mt-auto p-4 border-t border-gray-200 flex justify-between gap-2 sm:gap-4">
				<button
					onClick={() => setShowAccountModal(false)}
					className="px-4 sm:px-6 py-2 text-sm sm:text-base text-gray-600 hover:text-gray-800 cursor-pointer"
					disabled={isLoading}
				>
					Cancel
				</button>
				<button
					onClick={handleSubmit}
					disabled={isLoading}
					className="px-4 sm:px-6 py-2 text-sm sm:text-base bg-black !text-white rounded-lg hover:bg-gray-800 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
				>
					{isLoading ? 'Saving...' : 'Save Changes'}
				</button>
			</div>
		</div>
	);
};

export default UpdatePassword; 