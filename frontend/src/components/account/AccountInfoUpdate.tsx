import { useEffect, useState } from 'react';
import { userAPI } from '../../utils/api';
import { useAuth } from '../../context/AuthContext';

type Props = {
	setShowAccountModal: (show: boolean) => void;
};

type LocalUser = {
	firstName: string;
	lastName: string;
	username: string;
	email: string;
};

const AccountInfoUpdate = ({ setShowAccountModal }: Props) => {
	const { user, updateAuth } = useAuth();
	const [isLoading, setIsLoading] = useState(false);
	const [isFetching, setIsFetching] = useState(true);
	const [feedback, setFeedback] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
	const [formData, setFormData] = useState<LocalUser>({
		firstName: user?.firstName || '',
		lastName: user?.lastName || '',
		username: user?.username || '',
		email: user?.email || '',
	});

	useEffect(() => {
		let mounted = true;
		(async () => {
			try {
				setIsFetching(true);
				const res = await userAPI.getMe();
				if (mounted && res.success) {
					setFormData({
						firstName: res.data.firstName || '',
						lastName: res.data.lastName || '',
						username: res.data.username || '',
						email: res.data.email || '',
					});
				}
			} catch (err) {
				// ignore; use existing auth user as fallback
			} finally {
				setIsFetching(false);
			}
		})();
		return () => {
			mounted = false;
		};
	}, []);

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target;
		setFormData((prev) => ({
			...prev,
			[name]: value,
		}));
	};

	const handleSubmit = async () => {
		setFeedback(null);
		try {
			setIsLoading(true);
			const payload: Partial<LocalUser> = {
				firstName: formData.firstName,
				lastName: formData.lastName,
				username: formData.username,
				email: formData.email,
			};
			const res = await userAPI.updateMe(payload);
			if (res.success) {
				updateAuth({ token: res.token, user: res.data });
				setFeedback({ type: 'success', text: 'Account information updated successfully' });
			}
		} catch (error: unknown) {
			const errMsg = (error as any)?.response?.data?.message || 'An error occurred while updating your profile';
			setFeedback({ type: 'error', text: errMsg });
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="flex flex-col h-full">
			<div className="flex-1">
				<div className="flex justify-between px-6 my-8">
					<h2 className="text-3xl font-bold">Account Information</h2>
				</div>
				{feedback && (
					<div className={`px-6 text-sm ${feedback.type === 'success' ? 'text-green-600' : 'text-red-600'}`}>
						{feedback.text}
					</div>
				)}
				<div className="my-6 px-6">
					<div className="flex flex-col gap-5 my-4">
						<input
							type="text"
							name="firstName"
							placeholder="First Name"
							value={formData.firstName}
							onChange={handleChange}
							className="rounded-[10px] w-full p-4 text-sm bg-[#9C9C9C]/[0.28] backdrop-blur-[30.3px] text-black placeholder:text-black/70 outline-none"
							disabled={isFetching || isLoading}
						/>
						<input
							type="text"
							name="lastName"
							placeholder="Last Name"
							value={formData.lastName}
							onChange={handleChange}
							className="rounded-[10px] w-full p-4 text-sm bg-[#9C9C9C]/[0.28] backdrop-blur-[30.3px] text-black placeholder:text-black/70 outline-none"
							disabled={isFetching || isLoading}
						/>
						<input
							type="text"
							name="username"
							placeholder="User Name"
							value={formData.username}
							onChange={handleChange}
							className="rounded-[10px] w-full p-4 text-sm bg-[#9C9C9C]/[0.28] backdrop-blur-[30.3px] text-black placeholder:text-black/70 outline-none"
							disabled={isFetching || isLoading}
						/>
						<input
							type="email"
							name="email"
							placeholder="Email"
							value={formData.email}
							onChange={handleChange}
							className="rounded-[10px] w-full p-4 text-sm bg-[#9C9C9C]/[0.28] backdrop-blur-[30.3px] text-black placeholder:text-black/70 outline-none"
							disabled={isFetching || isLoading}
						/>
					</div>
				</div>
			</div>
			<div className="mt-auto p-4 border-t border-gray-200 flex justify-between gap-2 sm:gap-4">
				<button
					onClick={() => setShowAccountModal(false)}
					className="px-4 sm:px-6 py-2 text-sm sm:text-base text-gray-600 hover:text-gray-800 cursor-pointer"
					disabled={isLoading}
				>
					Cancel
				</button>
				<button
					onClick={handleSubmit}
					disabled={isFetching || isLoading}
					className="px-4 sm:px-6 py-2 text-sm sm:text-base bg-black !text-white rounded-lg hover:bg-gray-800 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
				>
					{isLoading ? 'Saving...' : 'Save Changes'}
				</button>
			</div>
		</div>
	);
};

export default AccountInfoUpdate; 