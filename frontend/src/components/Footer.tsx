import React, { useState, useEffect, useMemo } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import settingsIcon from '../assets/images/settingsIcon.svg';
import { useAuth } from '../context/AuthContext';
import AccountModal from './account/AccountModal';

const Footer: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [openSettingMenu, setOpenSettingMenu] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [location, setLocation] = useState<string>('Loading...');
  const [currentDate, setCurrentDate] = useState<string>('');
  const [showAccountModal, setShowAccountModal] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 767);
  


  useEffect(() => {
    const date = new Date();
    setCurrentDate(
      date.toLocaleDateString('en-US', { month: '2-digit', year: '2-digit' })
    );

    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            const response = await axios.get(
              `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${position.coords.latitude}&longitude=${position.coords.longitude}`
            );
            const data = response.data;
            setLocation(`${data.city}, ${data.principalSubdivision}`);
          } catch (error) {
            console.log('error', error);
            setLocation('Location unavailable');
          }
        },
        () => {
          setLocation('Location unavailable');
        }
      );
    }
  }, []);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleSignOut = () => {
    setIsLoading(true);
    try {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      setIsLoading(false);
      navigate('/login');
    } catch (error) {
      console.error('Sign out error:', error);
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="flex justify-between flex-col-reverse md:flex-row mb-8 px-12 gap-4">
        <div className="flex gap-3 justify-center">
          <div className="inline-flex h-9 sm:h-10 items-center rounded-full border-2 border-white text-white px-4 sm:px-5">
            <span className="font-semibold text-[14px] sm:text-[17px] leading-none">
              {location}
            </span>
          </div>
          <div className="inline-flex h-9 sm:h-10 items-center rounded-full border-2 border-white bg-white text-black px-4 sm:px-5">
            <span className="font-semibold text-[14px] sm:text-[17px] leading-none">
              {currentDate}
            </span>
          </div>
        </div>



        <div className="flex gap-2 ps-0 lg:ps-[12%] justify-center">
          <div className="relative">
            <motion.img
              src={settingsIcon}
              alt="settingsIcon"
              loading="lazy"
              height={34}
              width={34}
              className="cursor-pointer"
              animate={{
                rotate: openSettingMenu ? 225 : 0,
              }}
              transition={{ duration: 0.5, ease: 'easeInOut' }}
              onClick={() => {
                setOpenSettingMenu(!openSettingMenu);
              }}
            />
            <AnimatePresence>
              {openSettingMenu && (
                <>
                  <motion.div
                    initial={{ opacity: 0, y: 0 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 0 }}
                    transition={{ duration: 0.5, ease: 'easeInOut' }}
                    className="absolute bottom-12 md:bottom-16 -right-[115%] md:right-[50%] items-center flex flex-col gap-2 w-max bg-[#FFFFFF8C] rounded-[15px] p-3 z-[9999]"
                  >
                    <button
                      className="font-semibold text-[10px] leading-[100%] tracking-[0%] w-[-webkit-fill-available] px-7 py-2.5 text-center text-[#282828] bg-[#FFFFFF] rounded-[25px] cursor-pointer"
                      onClick={() => {
                        setShowAccountModal(true);
                        setOpenSettingMenu(false);
                      }}
                    >
                      Account
                    </button>
                    <button
                      className="font-semibold text-[10px] leading-[100%] tracking-[0%] w-[-webkit-fill-available] px-4 py-2.5 text-center text-[#282828] bg-[#FFFFFF] rounded-[25px] cursor-pointer"
                      onClick={handleSignOut}
                    >
                      Sign out
                    </button>
                  </motion.div>
                </>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>

      <AnimatePresence>
        {showAccountModal && (
          <AccountModal setShowAccountModal={setShowAccountModal} />
        )}
      </AnimatePresence>

      {isMobile && openSettingMenu && (
        <div
          className="fixed inset-0 bg-black/50 z-[9998]"
          onClick={() => setOpenSettingMenu(false)}
        />
      )}
    </>
  );
};

export default Footer; 