import React, { useEffect, useRef, useState } from 'react';
import { CircularProgress, IconButton, Tooltip } from '@mui/material';
import { 
  Remove as MinimizeIcon,
  Fullscreen as MaximizeIcon,
  FullscreenExit as FullscreenExitIcon,
  Psychology as PsychologyIcon
} from '@mui/icons-material';
import arrowIcon from '../assets/images/arrowIcon.svg';
import { ChatMessage } from '../utils/api';

interface BuildChatboxProps {
  prompt: string;
  setPrompt: (value: string) => void;
  handleSubmit: (e: React.FormEvent) => void;
  messages: ChatMessage[];
  isSubmitting: boolean;
  hasCodeGenerated: boolean;
  isLLMGenerating: boolean;
  onMinimizeChange?: (isMinimized: boolean) => void;
  isMinimized?: boolean; // Add prop to control minimized state from parent
}

const BuildChatbox: React.FC<BuildChatboxProps> = ({
  prompt,
  setPrompt,
  handleSubmit,
  messages,
  isSubmitting,
  hasCodeGenerated,
  isLLMGenerating,
  onMinimizeChange,
  isMinimized: externalIsMinimized = false, // Use prop with default value
}) => {
  const [activeTab, setActiveTab] = useState<'chat' | 'terminal'>('chat');
  const [internalIsMinimized, setInternalIsMinimized] = useState(externalIsMinimized);
  const [hasNewMessages, setHasNewMessages] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement | null>(null);
  const overlayRef = useRef<HTMLDivElement | null>(null);
  const messagesContainerRef = useRef<HTMLDivElement | null>(null);
  const [overlayHeight, setOverlayHeight] = useState<number>(120);
  const SINGLE_ROW_HEIGHT = 52; // px
  const MAX_TEXTAREA_HEIGHT = 160; // px

  const autoResizeTextarea = () => {
    const el = textareaRef.current;
    if (!el) return;
    el.style.height = 'auto';
    const newHeight = Math.max(SINGLE_ROW_HEIGHT, Math.min(el.scrollHeight, MAX_TEXTAREA_HEIGHT));
    el.style.height = `${newHeight}px`;
    el.style.overflowY = el.scrollHeight > MAX_TEXTAREA_HEIGHT ? 'auto' : 'hidden';
    el.style.minHeight = `${SINGLE_ROW_HEIGHT}px`;
  };

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  }, [messages, isSubmitting]);

  useEffect(() => {
    autoResizeTextarea();
  }, [prompt]);

  useEffect(() => {
    const measure = () => {
      if (overlayRef.current) {
        setOverlayHeight(overlayRef.current.offsetHeight);
      }
    };
    measure();
    window.addEventListener('resize', measure);
    return () => window.removeEventListener('resize', measure);
  }, [prompt]);

  // Track new messages when minimized
  useEffect(() => {
    if (internalIsMinimized && messages.length > 0) {
      setHasNewMessages(true);
    }
  }, [messages, internalIsMinimized]);

  // Sync internal state with external prop
  useEffect(() => {
    if (externalIsMinimized !== internalIsMinimized) {
      console.log('🔧 BuildChatbox: Syncing internal state with external prop:', externalIsMinimized);
      setInternalIsMinimized(externalIsMinimized);
    }
  }, [externalIsMinimized]);

  // Log when internal state changes
  useEffect(() => {
    console.log('🔧 BuildChatbox: Internal state changed:', internalIsMinimized);
  }, [internalIsMinimized]);

  const handleMinimize = () => {
    setInternalIsMinimized(true);
    setHasNewMessages(false);
    onMinimizeChange?.(true);
  };

  const handleMaximize = () => {
    setInternalIsMinimized(false);
    setHasNewMessages(false);
    onMinimizeChange?.(false);
  };



  return (
    <div className="transition-all duration-300 ease-in-out">
      {/* Wrapper keeps same width in both states to avoid lateral slide */}
      <div className="relative mx-auto rounded-[10px] font-['Charter']"
           style={{ background: internalIsMinimized ? 'transparent' : '#414141', transition: 'max-height 300ms ease-in-out, background-color 200ms ease-in-out' }}>
        {/* Minimized content (compact pill) */}
        {internalIsMinimized ? (
          <div className="w-full flex justify-center py-2">
            <div className={`bg-white rounded-[10px] shadow-lg border border-[#FFB6A3] px-4 py-3 flex items-center gap-3 hover:shadow-xl transition-colors duration-200 font-['Charter'] ${hasNewMessages ? 'ring-2 ring-[#FFB6A3] ring-opacity-50' : ''}`}>
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${hasNewMessages ? 'bg-[#FFB6A3] animate-pulse' : 'bg-green-500'}`}></div>
                <span className="text-sm font-medium text-gray-700">Chat</span>
                {messages.length > 0 && (
                  <span className="text-xs text-gray-500">({messages.length} messages)</span>
                )}
              </div>
              <IconButton
                size="small"
                onClick={handleMaximize}
                sx={{ 
                  color: '#1E1E1E',
                  '&:hover': { backgroundColor: 'rgba(30, 30, 30, 0.1)' }
                }}
                title="Maximize chat"
              >
                <MaximizeIcon />
              </IconButton>
            </div>
          </div>
        ) : (
          <>
            {/* Top padding to make room for input overlay */}
            <div style={{ paddingTop: overlayHeight }}>
              {/* Chat/Terminal display */}
              <div className="w-full flex justify-center px-2 md:px-3 pb-3 md:pb-4">
                  {activeTab === 'chat' ? (
                    <div 
                      ref={messagesContainerRef}
                      className="text-[12px] sm:text-[15px] leading-normal sm:leading-[32px] tracking-[0.01em] font-normal text-white w-[90%] max-h-[350px] overflow-y-auto scrollbar-custom pl-0 relative font-['Charter']" 
                      style={{ overscrollBehaviorY: 'contain' }} 
                      onWheel={(e) => e.stopPropagation()}
                    >
                      {messages.length === 0 ? (
                        <p>The AI response will be displayed in this area. This is where you'll see the detailed response to your queries.</p>
                      ) : (
                        <div className="flex flex-col gap-3 pr-4">
                          {messages.map((m) => (
                            <div
                              key={m.id}
                              className={
                                `max-w-[90%] w-fit px-4 py-2 rounded-[12px] whitespace-pre-wrap font-['Charter'] ` +
                                (m.type === 'user'
                                  ? 'bg-[#353535] self-end text-white'
                                  : 'bg-[#1E1E1E] self-start text-white')
                              }
                            >
                              {m.content}
                            </div>
                          ))}
                          {/* AI Thinking Indicator during chat submit */}
                          {isSubmitting && (
                            <div className="bg-[#1E1E1E] self-start text-white max-w-[90%] w-fit px-4 py-3 rounded-[12px] flex items-center gap-3 font-['Charter']">
                              <CircularProgress size={16} sx={{ color: '#667eea' }} />
                              <span className="text-[12px] sm:text-[15px]">AI is thinking...</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-[12px] sm:text-[15px] leading-normal sm:leading-[32px] tracking-[0.01em] font-normal text-white w-[90%] max-h-[220px] pl-0 flex items-center justify-center opacity-70 font-['Charter']">
                      <span>Terminal coming soon...</span>
                    </div>
                  )}
                </div>

                {/* Bottom tab bar */}
                <div className="w-full flex">
                <button
                  className={`${activeTab === 'chat' ? 'bg-[#2F2F2F]' : 'bg-[#1E1E1E]'} text-white !text-white flex-1 py-3 sm:py-4 text-center font-medium font-['Charter']`}
                  onClick={() => setActiveTab('chat')}
                >
                  Chat
                </button>
                <button
                  className={`${activeTab === 'terminal' ? 'bg-[#2F2F2F]' : 'bg-[#1E1E1E]'} text-white !text-white flex-1 py-3 sm:py-4 text-center border-l border-[#3a3a3a] font-medium font-['Charter']`}
                  onClick={() => setActiveTab('terminal')}
                >
                  Terminal
                </button>
              </div>
            </div>

            {/* Input overlay - only for Chat */}
            {activeTab === 'chat' && (
              <div ref={overlayRef} className="text-[17px] bg-white leading-[100%] tracking-[0] text-center font-normal absolute top-0 left-0 w-full rounded-[10px] pb-6 pt-10 flex justify-center gap-4 px-8 font-['Charter'] z-10">
                {/* LLM Intention Placeholder */}
                <div className="absolute top-6 left-1/2 transform -translate-x-1/2 flex items-center gap-2 text-black text-base font-medium font-['Charter']">
                  <PsychologyIcon sx={{ fontSize: 24, color: '#1E1E1E' }} />
                  <span>Placeholder for LLM Intention</span>
                </div>

                {/* Minimize and Maximize buttons */}
                <div className="absolute top-3 right-4 flex gap-2">
                  <Tooltip title="Minimize the Chatbox" arrow>
                    <IconButton
                      size="small"
                      onClick={handleMinimize}
                      sx={{ 
                        color: '#1E1E1E',
                        '&:hover': { backgroundColor: 'rgba(30, 30, 30, 0.1)' }
                      }}
                    >
                      <MinimizeIcon />
                    </IconButton>
                  </Tooltip>
                  {/* <IconButton
                    size="small"
                    disabled
                    sx={{ 
                      color: '#1E1E1E',
                      opacity: 0.3,
                      '&:hover': { backgroundColor: 'rgba(30, 30, 30, 0.1)' }
                    }}
                    title="Maximized"
                  >
                    <FullscreenExitIcon />
                  </IconButton> */}
                </div>
                
                <div className="absolute -bottom-0 mb-12 w-[90%] sm:w-[60%]">
                  <div className="relative">
                    <textarea
                      ref={textareaRef}
                      rows={1}
                      className="w-[95%] bg-[#F6F6F6] rounded-[25px] ps-7 pe-16 py-4 outline-none border border-black resize-none text-left font-['Charter']"
                      onChange={(e) => setPrompt(e.target.value)}
                      onInput={autoResizeTextarea}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          if (e.shiftKey) {
                            return; // allow newline
                          }
                          e.preventDefault();
                          handleSubmit(e);
                        }
                      }}
                      placeholder="Write your message here..."
                      value={prompt}
                      disabled={isSubmitting || !hasCodeGenerated}
                      aria-label="Message input"
                    />
                    <button
                      className="bg-[#424242] h-[52px] px-6 rounded-[25px] absolute bottom-[2px] right-[-8px] cursor-pointer flex items-center justify-center"
                      onClick={(e) => {
                        e.preventDefault();
                        handleSubmit(e);
                      }}
                      disabled={!prompt.trim() || isSubmitting || !hasCodeGenerated}
                      aria-label="Send message"
                    >
                      {(isSubmitting || isLLMGenerating) ? (
                        <CircularProgress size={20} sx={{ color: 'white' }} />
                      ) : (
                        <img src={arrowIcon} alt="icon" height={17} width={20} />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default BuildChatbox; 