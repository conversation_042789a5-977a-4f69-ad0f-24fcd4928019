import React, { useState } from 'react';
import {
  A<PERSON><PERSON>ar,
  Too<PERSON>bar,
  Typography,
  IconButton,
  Avatar,
  Chip,
  CircularProgress
} from '@mui/material';
import {
  Logout as LogoutIcon,
  Psychology as AIIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';

const Header: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
      navigate('/login');
    } finally {
      setIsLoggingOut(false);
    }
  };

  const handleTitleClick = () => {
    navigate('/');
  };

  return (
    <>
      <AppBar position="static" sx={{ background: '#343a40' }}>
        <Toolbar>
          {/* <AIIcon sx={{ mr: 1 }} /> */}
          <Typography 
            variant="h6" 
            component="div" 
            sx={{ 
              flexGrow: 1, 
              fontWeight: 'bold',
              cursor: 'pointer',
              '&:hover': {
                opacity: 0.8
              }
            }}
            onClick={handleTitleClick}
          >
            Mergen Code
          </Typography>
          <Chip
            avatar={<Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)' }}>{user?.email?.[0]?.toUpperCase()}</Avatar>}
            label={user?.email}
            variant="outlined"
            sx={{ color: 'white', borderColor: 'rgba(255,255,255,0.3)', mr: 2 }}
          />
          <IconButton
            color="inherit"
            onClick={handleLogout}
            disabled={isLoggingOut}
            sx={{ '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' } }}
          >
            {isLoggingOut ? <CircularProgress size={24} color="inherit" /> : <LogoutIcon />}
          </IconButton>
        </Toolbar>
      </AppBar>
    </>
  );
};

export default Header; 