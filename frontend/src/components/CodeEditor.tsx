import React, { useRef } from 'react';
import { Editor } from '@monaco-editor/react';
import { Box, Typography } from '@mui/material';

interface CodeEditorProps {
  value: string;
  language: string;
  onChange: (value: string | undefined) => void;
  fileName?: string;
  height?: string | number;
  theme?: 'vs-dark' | 'light' | 'vs';
  readOnly?: boolean;
}

const CodeEditor: React.FC<CodeEditorProps> = ({
  value,
  language,
  onChange,
  fileName,
  height = '100%',
  theme = 'vs-dark',
  readOnly = false
}) => {
  const editorRef = useRef<any>(null);

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;

    // Configure editor options
    editor.updateOptions({
      fontSize: 14,
      lineHeight: 20,
      fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      automaticLayout: true,
      tabSize: 2,
      insertSpaces: true,
      wordWrap: 'on',
      lineNumbers: 'on',
      glyphMargin: true,
      folding: true,
      lineDecorationsWidth: 10,
      lineNumbersMinChars: 3,
      renderWhitespace: 'selection',
      readOnly: readOnly
    });

    // Set up key bindings for save
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      // Trigger save - you can add save functionality here
      console.log('Save triggered');
    });

    // Add additional language support if needed
    monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.Latest,
      allowNonTsExtensions: true,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.languages.typescript.ModuleKind.CommonJS,
      noEmit: true,
      esModuleInterop: true,
      jsx: monaco.languages.typescript.JsxEmit.React,
      reactNamespace: 'React',
      allowJs: true,
      typeRoots: ['node_modules/@types']
    });

    // Enable JSX support for .tsx files
    monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
      noSemanticValidation: false,
      noSyntaxValidation: false
    });
  };

  // Get appropriate language for Monaco Editor
  const getMonacoLanguage = (lang: string): string => {
    const langMap: { [key: string]: string } = {
      'javascript': 'javascript',
      'jsx': 'javascript',
      'typescript': 'typescript',
      'tsx': 'typescript',
      'json': 'json',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'less': 'less',
      'python': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'yaml': 'yaml',
      'yml': 'yaml',
      'xml': 'xml',
      'markdown': 'markdown',
      'md': 'markdown',
      'bash': 'shell',
      'sh': 'shell',
      'dockerfile': 'dockerfile',
      'sql': 'sql',
      'php': 'php',
      'go': 'go',
      'rust': 'rust',
      'text': 'plaintext'
    };

    return langMap[lang.toLowerCase()] || 'plaintext';
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {fileName && (
        <Box
          sx={{
            backgroundColor: theme === 'vs-dark' ? '#2d2d2d' : '#f5f5f5',
            borderBottom: `1px solid ${theme === 'vs-dark' ? '#444' : '#ddd'}`,
            p: 1,
            display: 'flex',
            alignItems: 'center'
          }}
        >
          <Typography 
            variant="body2" 
            sx={{ 
              color: theme === 'vs-dark' ? '#cccccc' : '#333',
              fontSize: '13px',
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace'
            }}
          >
            {fileName}
          </Typography>
        </Box>
      )}
      
      <Box sx={{ flex: 1, minHeight: 0 }}>
        <Editor
          height={height}
          language={getMonacoLanguage(language)}
          value={value}
          onChange={onChange}
          theme={theme}
          onMount={handleEditorDidMount}
          options={{
            selectOnLineNumbers: true,
            roundedSelection: false,
            readOnly: readOnly,
            cursorStyle: 'line',
            automaticLayout: true,
          }}
        />
      </Box>
    </Box>
  );
};

export default CodeEditor; 