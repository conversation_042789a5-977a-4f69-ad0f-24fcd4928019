import React from 'react';

export const ShareIcon: React.FC<{ color?: string }> = ({ color = '#1E1E1E' }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    aria-label="Upload Icon"
  >
    <path
      d="M6.66669 6.66671L10 3.33337L13.3334 6.66671M10 3.75004V12.5"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.33331 11.6666V11.8666C3.33331 13.5468 3.33331 14.3869 3.66029 15.0286C3.94791 15.5931 4.40686 16.052 4.97134 16.3396C5.61308 16.6666 6.45316 16.6666 8.13331 16.6666H11.8666C13.5468 16.6666 14.3869 16.6666 15.0286 16.3396C15.5931 16.052 16.052 15.5931 16.3397 15.0286C16.6666 14.3869 16.6666 13.5468 16.6666 11.8666V11.6666"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
    />
  </svg>
); 