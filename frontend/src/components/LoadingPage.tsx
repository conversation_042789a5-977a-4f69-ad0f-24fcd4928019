import React from 'react';
import {
  Paper,
  Typography,
  Box,
  CircularProgress,
  Alert
} from '@mui/material';

interface LoadingPageProps {
  title?: string;
  subtitle?: string;
  progress?: {
    llmGenerating: boolean;
    llmCompleted: boolean;
    workspaceCreating: boolean;
    workspaceCompleted: boolean;
    completed: boolean;
  };
  notice?: string;
}

const LoadingPage: React.FC<LoadingPageProps> = ({
  title = "Building Your Project",
  subtitle = "AI is analyzing your requirements and generating code while we prepare your workspace",
  progress,
  notice
}) => {
  // Debug logging for progress updates
  console.log('🔄 LoadingPage render:', {
    progress,
    llmStatus: progress?.llmGenerating ? 'generating' : progress?.llmCompleted ? 'completed' : 'waiting',
    workspaceStatus: progress?.workspaceCreating ? 'creating' : progress?.workspaceCompleted ? 'completed' : 'waiting',
    notice
  });

  return (
    <Paper
      elevation={6}
      sx={{
        height: '600px',
        borderRadius: 3,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#666',
        p: 3
      }}
    >
      <CircularProgress size={50} sx={{ mb: 3, color: '#667eea' }} />
      <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
        {title}
      </Typography>
      <Typography variant="body1" sx={{ mb: 3, textAlign: 'center', maxWidth: '400px' }}>
        {subtitle}
      </Typography>

      {notice && (
        <Box sx={{ width: '100%', maxWidth: '500px', mb: 2 }}>
          <Alert severity="info" sx={{ width: '100%' }}>
            {notice}
          </Alert>
        </Box>
      )}

      {/* Progress indicators */}
      {progress && (
        <Box sx={{ width: '100%', maxWidth: '400px', mt: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            {progress.llmGenerating ? (
              <CircularProgress size={20} sx={{ mr: 2, color: '#667eea' }} />
            ) : progress.llmCompleted ? (
              <Box sx={{ width: 20, height: 20, mr: 2, display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '16px' }}>
                ✅
              </Box>
            ) : (
              <CircularProgress size={20} sx={{ mr: 2, color: '#667eea' }} />
            )}
            <Typography variant="body2" sx={{
              color: progress.llmCompleted ? '#4caf50' : '#666',
              fontWeight: progress.llmCompleted ? 'bold' : 'normal'
            }}>
              AI generating code structure...
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            {progress.workspaceCompleted ? (
              <Box sx={{ width: 20, height: 20, mr: 2, display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '16px' }}>
                ✅
              </Box>
            ) : (
              <CircularProgress size={20} sx={{ mr: 2, color: '#667eea' }} />
            )}
            <Typography variant="body2" sx={{
              color: progress.workspaceCompleted ? '#4caf50' : '#666',
              fontWeight: progress.workspaceCompleted ? 'bold' : 'normal'
            }}>
              Preparing workspace container...
            </Typography>
          </Box>
          <Typography variant="caption" sx={{ color: '#888', mt: 2, display: 'block' }}>
            This may take 1-3 minutes for complex projects
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

export default LoadingPage;