import React, { useState } from 'react';
import {
    Box,
    Typography,
    TextField,
    Button,
    Paper,
    Grid,
    Chip,
    Alert
} from '@mui/material';
import { generateDiff, calculatePayloadReduction, generateFileChanges } from '../utils/diffUtils';

const DiffOptimizationDemo: React.FC = () => {
    const [oldContent, setOldContent] = useState(`function hello() {
    console.log("Hello World!");
    return true;
}

const data = {
    name: "test",
    value: 42
};`);

    const [newContent, setNewContent] = useState(`function hello() {
    console.log("Hello Universe!");
    console.log("This is a new line");
    return true;
}

const data = {
    name: "test",
    value: 100,
    description: "Updated value"
};`);

    const [results, setResults] = useState<any>(null);

    const analyzeDiff = () => {
        // Generate diff operations
        const operations = generateDiff(oldContent, newContent);
        
        // Calculate payload sizes
        const fullPayload = { 'test.js': newContent };
        const changes = generateFileChanges({ 'test.js': oldContent }, { 'test.js': newContent });
        const payloadStats = calculatePayloadReduction(fullPayload, changes);
        
        setResults({
            operations,
            payloadStats,
            changes
        });
    };

    return (
        <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
            <Typography variant="h4" gutterBottom>
                Diff Optimization Demo
            </Typography>
            
            <Typography variant="body1" sx={{ mb: 3 }}>
                This demo shows how the Monaco workspace optimizes API payloads by sending only file changes 
                instead of complete file content.
            </Typography>

            <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                    <Paper sx={{ p: 2 }}>
                        <Typography variant="h6" gutterBottom>
                            Original Content
                        </Typography>
                        <TextField
                            multiline
                            rows={12}
                            fullWidth
                            value={oldContent}
                            onChange={(e) => setOldContent(e.target.value)}
                            variant="outlined"
                            sx={{ fontFamily: 'monospace' }}
                        />
                    </Paper>
                </Grid>

                <Grid item xs={12} md={6}>
                    <Paper sx={{ p: 2 }}>
                        <Typography variant="h6" gutterBottom>
                            Modified Content
                        </Typography>
                        <TextField
                            multiline
                            rows={12}
                            fullWidth
                            value={newContent}
                            onChange={(e) => setNewContent(e.target.value)}
                            variant="outlined"
                            sx={{ fontFamily: 'monospace' }}
                        />
                    </Paper>
                </Grid>

                <Grid item xs={12}>
                    <Box sx={{ textAlign: 'center', my: 2 }}>
                        <Button 
                            variant="contained" 
                            onClick={analyzeDiff}
                            size="large"
                        >
                            Analyze Diff & Calculate Savings
                        </Button>
                    </Box>
                </Grid>

                {results && (
                    <>
                        <Grid item xs={12}>
                            <Alert severity="success" sx={{ mb: 2 }}>
                                <Typography variant="h6">
                                    Payload Optimization Results
                                </Typography>
                                <Box sx={{ display: 'flex', gap: 2, mt: 1, flexWrap: 'wrap' }}>
                                    <Chip 
                                        label={`Original: ${(results.payloadStats.originalSize / 1024).toFixed(2)} KB`}
                                        color="default"
                                    />
                                    <Chip 
                                        label={`Optimized: ${(results.payloadStats.optimizedSize / 1024).toFixed(2)} KB`}
                                        color="primary"
                                    />
                                    <Chip 
                                        label={`Reduction: ${results.payloadStats.reduction.toFixed(1)}%`}
                                        color="success"
                                    />
                                </Box>
                            </Alert>
                        </Grid>

                        <Grid item xs={12} md={6}>
                            <Paper sx={{ p: 2 }}>
                                <Typography variant="h6" gutterBottom>
                                    Generated Diff Operations
                                </Typography>
                                <Box sx={{ 
                                    bgcolor: '#f5f5f5', 
                                    p: 2, 
                                    borderRadius: 1,
                                    fontFamily: 'monospace',
                                    fontSize: '0.875rem',
                                    maxHeight: 300,
                                    overflow: 'auto'
                                }}>
                                    <pre>{JSON.stringify(results.operations, null, 2)}</pre>
                                </Box>
                            </Paper>
                        </Grid>

                        <Grid item xs={12} md={6}>
                            <Paper sx={{ p: 2 }}>
                                <Typography variant="h6" gutterBottom>
                                    File Changes Payload
                                </Typography>
                                <Box sx={{ 
                                    bgcolor: '#f5f5f5', 
                                    p: 2, 
                                    borderRadius: 1,
                                    fontFamily: 'monospace',
                                    fontSize: '0.875rem',
                                    maxHeight: 300,
                                    overflow: 'auto'
                                }}>
                                    <pre>{JSON.stringify(results.changes, null, 2)}</pre>
                                </Box>
                            </Paper>
                        </Grid>

                        <Grid item xs={12}>
                            <Paper sx={{ p: 2, bgcolor: '#e8f5e8' }}>
                                <Typography variant="h6" gutterBottom>
                                    How It Works
                                </Typography>
                                <Typography variant="body2" paragraph>
                                    1. <strong>Change Detection:</strong> The system compares the current file content with the last saved version.
                                </Typography>
                                <Typography variant="body2" paragraph>
                                    2. <strong>Diff Generation:</strong> Instead of sending the entire file, it generates a series of operations (insert, delete, replace) that describe the changes.
                                </Typography>
                                <Typography variant="body2" paragraph>
                                    3. <strong>Payload Optimization:</strong> The system automatically chooses between sending the full content or the diff operations based on which is smaller.
                                </Typography>
                                <Typography variant="body2" paragraph>
                                    4. <strong>Server Application:</strong> The backend applies these operations to the existing file content, reconstructing the new version.
                                </Typography>
                                <Typography variant="body2">
                                    This approach significantly reduces network traffic, especially for large files with small changes, improving save performance and reducing bandwidth usage.
                                </Typography>
                            </Paper>
                        </Grid>
                    </>
                )}
            </Grid>
        </Box>
    );
};

export default DiffOptimizationDemo;
