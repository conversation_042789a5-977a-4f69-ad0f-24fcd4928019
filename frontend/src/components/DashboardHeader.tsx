import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import headingImg from '../assets/images/headingImg.svg';
import settingsIcon from '../assets/images/settingsIcon.svg';

interface DashboardHeaderProps {
  setOpenHistoryView: (value: boolean) => void;
  onOpenAccount: () => void;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({ setOpenHistoryView, onOpenAccount }) => {
  const [openSettingMenu, setOpenSettingMenu] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const navigate = useNavigate();
  const { logout } = useAuth();

  const handleLogoClick = () => {
    navigate('/');
  };

  const handleSignOut = async () => {
    try {
      setOpenSettingMenu(false);
      await logout();
    } finally {
      navigate('/login');
    }
  };

  return (
    <div className="flex justify-between shadow-[0px_3px_8.4px_0px_rgba(0,0,0,0.25)] px-6 sm:px-14 py-8 sm:py-11">
      {/* Left section with history button */}
      <div className="flex-col gap-2.5 flex-1 hidden lg:flex">
        
      </div>
      
      {/* Center logo/title */}
      <div className="flex-1 flex justify-start lg:justify-center">
        <img
          src={headingImg}
          alt="Mergen AI"
          className="cursor-pointer invert w-[200px] h-[50px]"
          onClick={handleLogoClick}
        />
      </div>
      
      {/* Right controls - Desktop */}
      <div className="lg:flex justify-end gap-2.5 items-center flex-1 hidden">
        <div className="relative flex">
          <motion.img
            src={settingsIcon}
            alt="Settings"
            className="cursor-pointer invert"
            width={32}
            height={32}
            animate={{
              rotate: openSettingMenu ? -225 : 0,
            }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
            onClick={() => setOpenSettingMenu(!openSettingMenu)}
          />
          
          <AnimatePresence>
            {openSettingMenu && (
              <motion.div
                initial={{ opacity: 0, y: 0 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 0 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
                className="absolute -bottom-28 -right-[30px] items-center z-50 flex flex-col gap-2 w-max font-['Inter']"
              >
                <button className="font-bold text-[17px] leading-[100%] tracking-[0%] w-fit px-4 py-2.5 text-center !text-white bg-[#1E1E1E] rounded-[25px] cursor-pointer" onClick={() => { setOpenSettingMenu(false); onOpenAccount(); }}>
                  Account
                </button>
                <button className="font-bold text-[17px] leading-[100%] tracking-[0%] w-fit px-4 py-2.5 text-center !text-white bg-[#1E1E1E] rounded-[25px] cursor-pointer" onClick={handleSignOut}>
                  Sign Out
                </button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Mobile menu button */}
      <div className="flex justify-end gap-2.5 flex-1 lg:hidden">
        <button 
          className="px-2 py-1 bg-gray-200 rounded text-sm mr-2"
          onClick={() => setOpenHistoryView(true)}
        >
          📋
        </button>
        <button 
          className="text-[26px]"
          onClick={() => setMenuOpen(!menuOpen)}
        >
          ☰
        </button>
      </div>
    </div>
  );
};

export default DashboardHeader; 