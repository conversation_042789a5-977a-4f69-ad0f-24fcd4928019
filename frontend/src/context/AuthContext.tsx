import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI, AuthResponse, LoginData, SignupData } from '../utils/api';

interface User {
	id: string;
	email: string;
	username?: string;
	firstName?: string;
	lastName?: string;
}

interface AuthContextType {
	user: User | null;
	token: string | null;
	login: (data: LoginData) => Promise<void>;
	signup: (data: SignupData) => Promise<void>;
	logout: () => Promise<void>;
	isLoading: boolean;
	updateAuth: (data: { token?: string; user?: Partial<User> }) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error('useAuth must be used within an AuthProvider');
	}
	return context;
};

interface AuthProviderProps {
	children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
	const [user, setUser] = useState<User | null>(null);
	const [token, setToken] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		// Check for stored token on app load
		const storedToken = localStorage.getItem('token');
		const storedUser = localStorage.getItem('user');
		
		if (storedToken && storedUser) {
			setToken(storedToken);
			setUser(JSON.parse(storedUser));
		}
		setIsLoading(false);
	}, []);

	const login = async (data: LoginData): Promise<void> => {
		try {
			const response: AuthResponse = await authAPI.login(data);
			
			setToken(response.token);
			setUser(response.user);
			
			localStorage.setItem('token', response.token);
			localStorage.setItem('user', JSON.stringify(response.user));
		} catch (error) {
			throw error;
		}
	};

	const signup = async (data: SignupData): Promise<void> => {
		try {
			const response: AuthResponse = await authAPI.signup(data);
			
			setToken(response.token);
			setUser(response.user);
			
			localStorage.setItem('token', response.token);
			localStorage.setItem('user', JSON.stringify(response.user));
		} catch (error) {
			throw error;
		}
	};

	const logout = async (): Promise<void> => {
		try {
			// Call backend logout API
			await authAPI.logout();
		} catch (error) {
			// Even if the API call fails, we should still clear local storage
			console.error('Logout API error:', error);
		} finally {
			// Clear local state and storage regardless of API response
			setUser(null);
			setToken(null);
			localStorage.removeItem('token');
			localStorage.removeItem('user');
		}
	};

	const updateAuth = (data: { token?: string; user?: Partial<User> }) => {
		if (typeof data.token !== 'undefined' && data.token) {
			setToken(data.token);
			localStorage.setItem('token', data.token);
		}
		if (typeof data.user !== 'undefined') {
			const nextUser = { ...(user || {}), ...(data.user as any) } as User;
			setUser(nextUser);
			localStorage.setItem('user', JSON.stringify(nextUser));
		}
	};

	const value: AuthContextType = {
		user,
		token,
		login,
		signup,
		logout,
		isLoading,
		updateAuth,
	};

	return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}; 