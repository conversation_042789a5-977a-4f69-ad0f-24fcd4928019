/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'inter': ['Inter', 'sans-serif'],
      },
      spacing: {
        '18': '4.5rem',
        '25': '6.25rem',
      },
      zIndex: {
        '500': 500,
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'expand-up': 'expandUp 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideUp: {
          '0%': { transform: 'translateY(100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        expandUp: {
          '0%': { 
            opacity: '0', 
            transform: 'scale(0.9)',
            transformOrigin: 'bottom center'
          },
          '100%': { 
            opacity: '1', 
            transform: 'scale(1)',
            transformOrigin: 'bottom center'
          },
        },
      },
    },
  },
  plugins: [],
}