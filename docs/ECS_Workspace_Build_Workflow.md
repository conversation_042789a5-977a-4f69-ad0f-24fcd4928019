# ECS Workspace Build Workflow Documentation

## Overview

This document describes the complete workflow for building projects and rendering the Monaco workspace in the Mergen AI application. The system coordinates between frontend rendering, backend build processes, and ECS workspace management.

## Problem Solved

### Original Issue
- **MonacoWorkspace showed early** before build completion
- **Circular dependency** between `hasCodeGenerated` and `isWorkspaceReady` states
- **Users stuck on loading page** when ECS workspace was ready but frontend didn't detect it
- **No mechanism to retry** when ECS workspace became ready after initial build

### Solution
- **Fixed render condition** to only depend on `hasCodeGenerated`
- **Added ECS workspace polling** to detect when workspace becomes ready
- **Removed circular dependency** between render states
- **Added safety mechanisms** and manual retry options

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │  ECS Workspace  │
│   Build Page    │◄──►│ Build Controller│◄──►│   Container     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
    ┌────▼────┐             ┌────▼────┐             ┌────▼────┐
    │ Monaco  │             │ Build   │             │  File   │
    │Workspace│             │ Result  │             │ System  │
    │Component│             │Database │             │ Service │
    └─────────┘             └─────────┘             └─────────┘
```

## Key States and Flow

### State Variables

| State | Type | Purpose | Set By |
|-------|------|---------|--------|
| `hasCodeGenerated` | boolean | Controls MonacoWorkspace rendering | Build completion or polling |
| `isWorkspaceReady` | boolean | Indicates workspace initialization complete | MonacoWorkspace component |
| `isLoading` | boolean | Shows loading spinner | Various components |
| `buildProgress` | object | Tracks build stages | Build process |

### Render Decision Logic

```typescript
// FIXED: Only check hasCodeGenerated for rendering decision
const shouldShowLoading = !hasCodeGenerated;

// MonacoWorkspace renders when: hasCodeGenerated === true
// Loading page shows when: hasCodeGenerated === false
```

## Detailed Workflow

### 1. Build Initialization

**Location:** `frontend/src/pages/Build/index.tsx` - `loadInterviewData` useEffect

```typescript
useEffect(() => {
  const loadInterviewData = async () => {
    if (!uuid) {
      setIsLoading(false);
      return;
    }

    try {
      const response = await interviewAPI.get(uuid);
      
      if (response.success) {
        // Check for existing build in database
        const existingBuildData = await llmAPI.checkBuildExists(uuid);
        
        if (existingBuildData.success && existingBuildData.exists) {
          // For existing builds, verify ECS workspace is ready
          const workspaceStructure = await ecsWorkspaceAPI.getStructure(`workspace_${uuid}`);
          
          if (workspaceStructure.success && workspaceStructure.data.structure) {
            const fileCount = Object.keys(workspaceStructure.data.structure).length;
            if (fileCount > 0) {
              setHasCodeGenerated(true); // ✅ Render MonacoWorkspace
            } else {
              // Start polling for existing builds
              startECSWorkspacePolling(existingBuildData.buildResult, uuid);
            }
          }
        } else {
          // Start new build
          buildProject(effectivePrompt, projectData, userData);
        }
      }
    } catch (error) {
      setError(error.message);
    }
  };
}, [uuid]);
```

### 2. Build Process

**Location:** `frontend/src/pages/Build/index.tsx` - `buildProject` function

```typescript
const buildProject = async (prompt: string, projectData?: any, userData?: any) => {
  try {
    // Call unified build API
    const response = await buildAPI.unifiedBuild({
      interviewUuid: uuid,
      prompt: prompt,
      projectData: projectData,
      userData: userData
    });

    if (response.success && response.buildResult?.projectStructure) {
      setProjectStructure(response.buildResult.projectStructure);
      
      // FIXED: Only set hasCodeGenerated=true if ECS workspace is ready
      if (response.workspace?.uploadSuccess && response.workspace?.filesUploaded > 0) {
        console.log('✅ ECS workspace ready with files, setting hasCodeGenerated=true');
        setHasCodeGenerated(true); // ✅ Render MonacoWorkspace immediately
      } else {
        console.log('⏳ ECS workspace not ready yet, starting polling');
        startECSWorkspacePolling(response.buildResult, uuid); // 🔄 Start polling
      }

      // Dispatch build completion event if successful
      if (response.workspace?.uploadSuccess && response.workspace?.filesUploaded > 0) {
        window.dispatchEvent(new CustomEvent('ecs-workspace-build-complete', {
          detail: {
            interviewUuid: uuid,
            codeBlocksCount: response.buildResult.codeBlocks.length,
            filesUploaded: response.workspace.filesUploaded,
            uploadSuccess: response.workspace.uploadSuccess,
            uploadedFiles: response.workspace.uploadedFiles
          }
        }));
      }
    }
  } catch (error) {
    console.error('Build error:', error);
  }
};
```

### 3. ECS Workspace Polling

**Location:** `frontend/src/pages/Build/index.tsx` - `startECSWorkspacePolling` function

```typescript
const startECSWorkspacePolling = async (buildResult: any, interviewUuid: string) => {
  console.log('🔄 Starting ECS workspace polling for interview:', interviewUuid);
  
  // Clear any existing polling
  if (pollingIntervalRef.current) {
    clearInterval(pollingIntervalRef.current);
    pollingIntervalRef.current = null;
  }
  
  const pollInterval = 3000; // Poll every 3 seconds
  const maxAttempts = 40; // Poll for up to 2 minutes
  let attempts = 0;
  let consecutiveErrors = 0;

  const pollWorkspace = async () => {
    attempts++;
    console.log(`🔍 ECS workspace polling attempt ${attempts}/${maxAttempts}`);

    try {
      const workspaceStructure = await ecsWorkspaceAPI.getStructure(`workspace_${interviewUuid}`);
      
      if (workspaceStructure.success && workspaceStructure.data.structure) {
        const files = workspaceStructure.data.structure;
        const fileCount = Object.keys(files).length;
        
        if (fileCount > 0) {
          // 🎉 ECS workspace is now ready!
          console.log('🎉 ECS workspace is now ready - stopping polling');
          
          // Clear polling
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }
          
          // Set hasCodeGenerated to trigger MonacoWorkspace rendering
          setHasCodeGenerated(true); // ✅ Render MonacoWorkspace
          
          // Dispatch build completion event
          window.dispatchEvent(new CustomEvent('ecs-workspace-build-complete', {
            detail: {
              interviewUuid: interviewUuid,
              codeBlocksCount: buildResult.codeBlocks?.length || 0,
              filesUploaded: fileCount,
              ecsReady: true,
              uploadSuccess: true,
              uploadedFiles: Object.keys(files)
            }
          }));
          
          return;
        }
      }
      
      consecutiveErrors = 0; // Reset on successful API call
      
      if (attempts >= maxAttempts) {
        console.log('⚠️ Max attempts reached, showing workspace as fallback');
        setHasCodeGenerated(true); // ✅ Fallback render
      }
      
    } catch (error) {
      consecutiveErrors++;
      console.error(`❌ Polling error (attempt ${attempts}/${maxAttempts}):`, error);
      
      if (consecutiveErrors >= 5 || attempts >= maxAttempts) {
        console.log('⚠️ Too many errors, showing workspace as fallback');
        setHasCodeGenerated(true); // ✅ Error fallback render
      }
    }
  };

  // Start polling immediately, then every 3 seconds
  await pollWorkspace();
  
  if (attempts < maxAttempts && consecutiveErrors < 5) {
    pollingIntervalRef.current = setInterval(pollWorkspace, pollInterval);
  }
};
```

### 4. Build Completion Event Handling

**Location:** `frontend/src/pages/Build/index.tsx` - Build completion event listener

```typescript
useEffect(() => {
  const handleBuildComplete = (event: CustomEvent) => {
    const { interviewUuid: eventInterviewUuid, uploadSuccess, filesUploaded } = event.detail || {};
    
    console.log('🔄 Build page received build complete event:', { 
      eventInterviewUuid, 
      uploadSuccess,
      filesUploaded,
      currentUuid: uuid,
      currentHasCodeGenerated: hasCodeGenerated
    });

    if (eventInterviewUuid === uuid && uploadSuccess && filesUploaded > 0) {
      console.log('✅ Build completion event received - ensuring workspace is ready');
      
      // FIXED: Always set hasCodeGenerated=true when we get successful event
      if (!hasCodeGenerated) {
        console.log('✅ Setting hasCodeGenerated=true after build completion event');
        setHasCodeGenerated(true); // ✅ Render MonacoWorkspace
      }
    }
  };

  window.addEventListener('ecs-workspace-build-complete', handleBuildComplete as EventListener);

  return () => {
    window.removeEventListener('ecs-workspace-build-complete', handleBuildComplete as EventListener);
  };
}, [uuid, hasCodeGenerated]);
```

### 5. MonacoWorkspace Component Integration

**Location:** `frontend/src/components/MonacoWorkspace.tsx` - Component lifecycle

```typescript
const MonacoWorkspace: React.FC<MonacoWorkspaceProps> = ({
  interviewUuid,
  onWorkspaceReady,
  onError,
  height = '100%',
  minHeight = '500px'
}) => {
  console.log('🎯 MonacoWorkspace: *** COMPONENT MOUNTED ***');
  
  // Initialize workspace when component mounts
  useEffect(() => {
    if (interviewUuid && !isInitialized) {
      console.log('🚀 MonacoWorkspace: Starting initialization');
      initializeWorkspace();
    }
  }, [interviewUuid, isInitialized]);

  // Listen for build completion events
  useEffect(() => {
    const handleBuildComplete = (event: CustomEvent) => {
      const { interviewUuid: eventInterviewUuid, uploadSuccess, uploadedFiles } = event.detail || {};
      
      if (eventInterviewUuid === interviewUuid && uploadSuccess) {
        console.log('🔄 MonacoWorkspace: Refreshing after build completion');
        setIsBuildComplete(true);
        
        // Retry fetching workspace structure until files are available
        const retryFetchWithFiles = async (attempt = 0) => {
          const maxAttempts = 10;
          
          try {
            const data = await ecsWorkspaceAPI.getStructure(workspaceNameRef.current);
            
            if (data.success && data.data.structure) {
              const files = flattenStructureToFiles(data.data.structure);
              const fileCount = Object.keys(files).length;
              
              if (fileCount > 0) {
                // Update workspace with files
                const fileTree = createFileTreeFromStructure(data.data.structure);
                
                setWorkspace(prev => ({
                  ...prev,
                  files,
                  savedFiles: { ...files },
                  fileTree,
                  selectedFile: Object.keys(files)[0] || null
                }));
                
                console.log('✅ MonacoWorkspace: Files loaded, calling onWorkspaceReady');
                onWorkspaceReady?.(workspaceNameRef.current); // ✅ Set isWorkspaceReady=true
                return;
              }
            }
            
            if (attempt < maxAttempts - 1) {
              setTimeout(() => retryFetchWithFiles(attempt + 1), 1000);
            } else {
              console.log('⚠️ MonacoWorkspace: Max attempts reached, calling onWorkspaceReady anyway');
              onWorkspaceReady?.(workspaceNameRef.current); // ✅ Fallback
            }
          } catch (error) {
            if (attempt < maxAttempts - 1) {
              setTimeout(() => retryFetchWithFiles(attempt + 1), 1000);
            } else {
              console.log('⚠️ MonacoWorkspace: Max attempts with errors, calling onWorkspaceReady anyway');
              onWorkspaceReady?.(workspaceNameRef.current); // ✅ Error fallback
            }
          }
        };
        
        retryFetchWithFiles();
      }
    };

    window.addEventListener('ecs-workspace-build-complete', handleBuildComplete as EventListener);
    
    return () => {
      window.removeEventListener('ecs-workspace-build-complete', handleBuildComplete as EventListener);
    };
  }, [interviewUuid, onWorkspaceReady]);
};
```

### 6. Backend Build Process

**Location:** `backend/src/controllers/buildController.ts` - `unifiedInitialBuild` function

```typescript
export const unifiedInitialBuild = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid, prompt, projectData, userData } = req.body;

    // Step 1: Generate LLM response
    const llmResult = await AnthropicService.generateResponse(prompt, projectData, userData);
    
    // Step 2: Process and save BuildResult
    const buildServiceResult = await BuildService.processLLMResponse({
      interviewUuid,
      userId: req.user?.userId,
      rawLLMResponse: llmResult.content,
      llmMetadata: llmResult.metadata
    });

    // Step 3: Create ECS workspace
    const { ECSWorkspaceService } = await import('../services/ecsWorkspaceService');
    const ecsWorkspaceService = new ECSWorkspaceService();
    const ecsWorkspaceResult = await ecsWorkspaceService.getOrCreateWorkspace(
      `workspace_${interviewUuid}`,
      req.user?.userId || '',
      24 // 24 hour lifetime
    );

    // Step 4: Upload files to ECS workspace with retry mechanism
    let actualFilesUploaded = 0;
    let uploadSuccess = false;
    let uploadedFilesList: string[] = [];

    if (buildResult.codeBlocks && buildResult.codeBlocks.length > 0) {
      const files: { [filePath: string]: string } = {};
      
      for (const codeBlock of buildResult.codeBlocks) {
        if (codeBlock.filename && codeBlock.content) {
          files[codeBlock.filename] = codeBlock.content;
        }
      }

      if (Object.keys(files).length > 0) {
        const { ECSFileSystemService } = await import('../services/ecsFileSystemService');
        const ecsFileSystemService = new ECSFileSystemService();

        // Retry upload until workspace is ready
        const maxRetries = 10;
        const retryDelay = 3000;
        let retryCount = 0;

        while (retryCount < maxRetries) {
          try {
            const uploadResult = await ecsFileSystemService.saveFiles(
              ecsWorkspaceResult.workspaceId, 
              files
            );

            if (uploadResult.success) {
              // Verify files are available
              const structure = await ecsFileSystemService.getWorkspaceStructure(
                ecsWorkspaceResult.workspaceId
              );
              const availableFiles = countFilesInStructure(structure);

              if (availableFiles >= Object.keys(files).length) {
                console.log('✅ UNIFIED BUILD: Upload verified - all files available');
                actualFilesUploaded = Object.keys(files).length;
                uploadSuccess = true;
                uploadedFilesList = Object.keys(files);
                break; // Success!
              }
            }
          } catch (uploadError) {
            retryCount++;
            if (retryCount < maxRetries) {
              await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
          }
        }
      }
    }

    // Step 5: Return response with accurate upload status
    return res.json({
      success: true,
      message: 'Unified initial build completed successfully',
      buildResult: {
        uuid: buildResult.uuid,
        description: buildResult.description,
        deploymentInstructions: buildResult.deploymentInstructions,
        status: buildResult.status,
        codeBlocks: buildResult.codeBlocks,
        projectStructure: buildResult.projectStructure,
        chatHistory: buildResult.chatHistory || []
      },
      workspace: {
        workspaceId: `workspace_${interviewUuid}`,
        workspaceUrl: null,
        status: uploadSuccess ? 'ready' : 'pending',
        filesUploaded: actualFilesUploaded, // Actual count
        ecsReady: uploadSuccess,
        uploadSuccess: uploadSuccess, // Explicit flag
        uploadedFiles: uploadedFilesList // List of files
      },
      metadata: {
        interviewUuid,
        effectivePrompt: prompt,
        timestamp: new Date(),
        workflow: 'unified-initial-build'
      }
    });

  } catch (error) {
    console.error('❌ UNIFIED BUILD: Error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to complete unified initial build',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};
```

## Configuration Settings

### Polling Settings

```typescript
const POLLING_CONFIG = {
  interval: 3000,        // Poll every 3 seconds
  maxAttempts: 40,       // Poll for up to 2 minutes (40 * 3 seconds)
  maxConsecutiveErrors: 5, // Stop after 5 consecutive errors
  retryDelay: 1000       // 1 second delay between retries in MonacoWorkspace
};
```

### Safety Timeouts

```typescript
const TIMEOUT_CONFIG = {
  safetyTimeout: 300000,    // 5 minutes - force hasCodeGenerated=true if stuck
  workspaceRetry: 180000,   // 3 minutes - MonacoWorkspace retry timeout
  buildTimeout: 600000      // 10 minutes - overall build timeout
};
```

### Backend Retry Settings

```typescript
const BACKEND_CONFIG = {
  maxUploadRetries: 10,     // Retry file upload up to 10 times
  uploadRetryDelay: 3000,   // 3 seconds between upload retries
  workspaceLifetime: 24     // 24 hours ECS workspace lifetime
};
```

## Flow Diagrams

### Happy Path Flow

```
1. User loads Build page
   ↓
2. Check for existing build
   ↓
3a. If exists + ECS ready → hasCodeGenerated=true → MonacoWorkspace renders
   ↓
3b. If exists + ECS not ready → Start polling → When ready → hasCodeGenerated=true
   ↓
4. If no existing build → Start new build
   ↓
5a. Build completes + ECS ready → hasCodeGenerated=true → MonacoWorkspace renders
   ↓
5b. Build completes + ECS not ready → Start polling → When ready → hasCodeGenerated=true
   ↓
6. MonacoWorkspace initializes → onWorkspaceReady() → isWorkspaceReady=true
```

### Error Handling Flow

```
1. Polling fails after max attempts
   ↓
2. Set hasCodeGenerated=true (fallback)
   ↓
3. MonacoWorkspace renders with empty state
   ↓
4. User can manually retry or continue with empty workspace
```

## Key Files and Locations

### Frontend Files

| File | Key Functions | Purpose |
|------|---------------|---------|
| `frontend/src/pages/Build/index.tsx` | `buildProject`, `startECSWorkspacePolling`, `handleBuildComplete` | Main build orchestration |
| `frontend/src/components/MonacoWorkspace.tsx` | `initializeWorkspace`, `handleBuildComplete` | Workspace rendering and file management |
| `frontend/src/utils/api.ts` | `buildAPI.unifiedBuild`, `ecsWorkspaceAPI.getStructure` | API communication |

### Backend Files

| File | Key Functions | Purpose |
|------|---------------|---------|
| `backend/src/controllers/buildController.ts` | `unifiedInitialBuild` | Complete build process |
| `backend/src/services/ecsWorkspaceService.ts` | `getOrCreateWorkspace` | ECS workspace management |
| `backend/src/services/ecsFileSystemService.ts` | `saveFiles`, `getWorkspaceStructure` | File operations |

## Debugging and Troubleshooting

### Key Log Messages

| Log Message | Meaning | Action |
|-------------|---------|--------|
| `🎯 MonacoWorkspace: *** COMPONENT MOUNTED ***` | MonacoWorkspace component rendered | Normal - workspace should initialize |
| `🔄 Starting ECS workspace polling` | Polling started for ECS workspace | Normal - waiting for files |
| `🎉 ECS workspace is now ready` | Files found in ECS workspace | Success - workspace should render |
| `✅ Setting hasCodeGenerated=true` | Triggering MonacoWorkspace render | Success - workspace will show |
| `⚠️ Max attempts reached` | Polling failed, using fallback | Warning - may show empty workspace |

### Common Issues and Solutions

| Issue | Symptoms | Solution |
|-------|----------|----------|
| Infinite loading | Loading page never disappears | Check ECS workspace status, verify polling is working |
| Empty workspace | MonacoWorkspace shows but no files | Check backend file upload, verify ECS workspace has files |
| Build never starts | No build progress indicators | Check interview data, verify API endpoints |
| Polling stuck | Polling logs but never finds files | Check ECS workspace container status, verify file upload |

### Manual Debugging Steps

1. **Check browser console** for polling and event logs
2. **Verify ECS workspace** has files via API: `GET /api/ecs-workspace/workspace_${uuid}/structure`
3. **Check database** for BuildResult: `GET /api/llm/check-build-exists/${uuid}`
4. **Monitor network tab** for API calls and responses
5. **Check backend logs** for upload status and errors

## Future Modifications

### Adding New Build Steps

1. **Add progress state** to `buildProgress` object
2. **Update progress indicators** in loading UI
3. **Add corresponding backend logging** for new step
4. **Update polling logic** if new step affects ECS workspace

### Modifying Polling Behavior

1. **Update `POLLING_CONFIG`** constants
2. **Modify `startECSWorkspacePolling`** function logic
3. **Test with different timeout values** for your use case
4. **Consider adding exponential backoff** for production

### Adding New Workspace Types

1. **Create new workspace service** (similar to ECS)
2. **Add workspace type detection** in build process
3. **Update polling logic** to handle different workspace types
4. **Modify MonacoWorkspace** to support new workspace APIs

## Conclusion

This workflow ensures reliable coordination between frontend rendering, backend build processes, and ECS workspace management. The key innovation is the polling mechanism that bridges the gap between asynchronous build completion and workspace readiness, providing a smooth user experience even when systems are not perfectly synchronized.

The render condition fix (`hasCodeGenerated` only) eliminates circular dependencies and ensures MonacoWorkspace renders as soon as the build is logically complete, regardless of workspace initialization state.