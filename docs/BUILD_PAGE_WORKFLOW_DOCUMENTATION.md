# Build Page Workflow Documentation

This document provides a comprehensive overview of the build page workflow in the Mergen-AI code generation platform.

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [User Events and API Triggers](#user-events-and-api-triggers)
3. [Frontend to Backend Workflow](#frontend-to-backend-workflow)
4. [Backend API Processing](#backend-api-processing)
5. [Detailed API Documentation with cURL Examples](#detailed-api-documentation-with-curl-examples)
6. [Configuration Parameters and Customization](#configuration-parameters-and-customization)
7. [Deployment Guide](#deployment-guide)
8. [Data Models and Database Schema](#data-models-and-database-schema)
9. [Architecture Optimization Opportunities](#architecture-optimization-opportunities)
10. [Deployment Readiness Assessment](#deployment-readiness-assessment)

## Architecture Overview

### Technology Stack
- **Frontend**: React 18 + TypeScript + Material-UI + Monaco Editor
- **Backend**: Node.js + Express + TypeScript
- **Database**: MongoDB with Mongoose ODM
- **Infrastructure**: AWS ECS for containerized workspaces
- **LLM Integration**: Anthropic Claude Sonnet 4 API
- **File System**: ECS-based workspace management

### Key Components
- **Build Page** (`frontend/src/pages/Build/index.tsx`): Main interface for code generation and editing
- **Monaco Workspace** (`frontend/src/components/MonacoWorkspace.tsx`): Code editor and file management
- **ECS Workspace Service** (`backend/src/services/ecsWorkspaceService.ts`): Container management
- **Build Controller** (`backend/src/controllers/buildController.ts`): Core build logic
- **Anthropic Service** (`backend/src/services/anthropicService.ts`): LLM integration

## User Events and API Triggers

### 1. Initial Page Load (`/build/:uuid`)
**User Action**: Navigate to build page with interview UUID
**API Calls Triggered**:
- `GET /api/interview/:uuid` - Load interview configuration
- `GET /api/llm/build-exists/:uuid` - Check for existing build
- `POST /api/build/unified` - Create unified initial build (if new)
- `GET /api/ecs-workspace/:workspaceName/structure` - Load workspace files

### 2. Chat Message Submission
**User Action**: Type message and click send or press Enter
**API Calls Triggered**:
- `POST /api/build/chat` - Send chat message to AI
- `POST /api/ecs-workspace/:workspaceName/extract` - Extract files from AI response
- `PUT /api/llm/chat/:buildResultUuid` - Save chat history to database

### 3. File Operations in Monaco Editor
**User Action**: Select, edit, save, or revert files
**API Calls Triggered**:
- `POST /api/ecs-workspace/:workspaceName/files/read` - Read file content
- `POST /api/ecs-workspace/:workspaceName/save` - Save modified files
- `GET /api/ecs-workspace/:workspaceName/structure` - Refresh file structure

### 4. Preview Operations
**User Action**: Start, stop, refresh, or open preview in new tab
**API Calls Triggered**:
- `POST /api/ecs-workspace/:workspaceName/preview` - Start preview server
- `DELETE /api/ecs-workspace/:workspaceName/preview` - Stop preview server
- `GET /api/ecs-workspace/:workspaceName/preview/status` - Check preview status

### 5. Workspace Management
**User Action**: Refresh workspace, collapse/expand UI elements
**API Calls Triggered**:
- `POST /api/ecs-workspace` - Create new workspace (if needed)
- `GET /api/ecs-workspace/:workspaceName/structure` - Refresh structure

## Frontend to Backend Workflow

### Initial Build Process
1. **Frontend**: User navigates to `/build/:uuid`
2. **Frontend**: Check localStorage for existing state
3. **Frontend**: Call `interviewAPI.get(uuid)` to load interview data
4. **Frontend**: Call `chatAPI.checkBuildExists(uuid)` to check for existing build
5. **Frontend**: If no existing build, call `buildAPI.unifiedBuild()` with interview data
6. **Backend**: Process unified build (LLM generation + workspace creation + file extraction)
7. **Frontend**: Display progressive messages and populate Monaco workspace
8. **Frontend**: Set up event listeners for workspace updates

### Chat Interaction Process
1. **Frontend**: User types message and submits
2. **Frontend**: Add user message to chat history immediately
3. **Frontend**: Call `buildAPI.chat()` with message and conversation history
4. **Backend**: Get complete conversation context from database
5. **Backend**: Generate AI response using Anthropic API (or mock data)
6. **Backend**: Extract files from AI response and save to ECS workspace
7. **Backend**: Save conversation to database for context continuity
8. **Frontend**: Display AI response and trigger workspace refresh
9. **Frontend**: Update Monaco editor with new/modified files

### File Management Process
1. **Frontend**: User selects file in Monaco workspace
2. **Frontend**: Call `ecsWorkspaceAPI.readFile()` if content not cached
3. **Frontend**: Display file content in Monaco editor
4. **Frontend**: User edits file content
5. **Frontend**: Track unsaved changes in component state
6. **Frontend**: User clicks save button
7. **Frontend**: Call `ecsWorkspaceAPI.saveFiles()` with modified files
8. **Backend**: Update files in ECS container filesystem
9. **Frontend**: Update saved state and remove unsaved changes indicator

## Backend API Processing

### Core API Endpoints

#### `/api/build/unified` (POST)
**Purpose**: Unified initial build process
**Processing**:
1. Validate interview UUID and user authentication
2. Check for existing BuildResult to prevent duplicates
3. Start parallel operations: LLM generation + ECS workspace creation
4. Process LLM response using `BuildService.processLLMResponse()`
5. Extract code blocks and create project structure
6. Upload files to ECS workspace using `ECSFileSystemService`
7. Create initial chat history from extracted content
8. Return comprehensive build result with chat history

#### `/api/build/chat` (POST)
**Purpose**: Handle chat interactions with AI
**Processing**:
1. Validate user authentication and message content
2. Find associated BuildResult for chat history storage
3. Get complete conversation context using `ConversationHistoryService`
4. Generate AI response using `AnthropicService.generateResponse()`
5. Create chat summary for UI display vs full response for context
6. Extract files from AI response using `CodeExtractor`
7. Save files to ECS workspace using `ECSFileSystemService`
8. Update BuildResult with new chat messages
9. Save conversation context for future LLM API calls

#### `/api/ecs-workspace/:workspaceName/*` (Various)
**Purpose**: ECS workspace file system operations
**Processing**:
1. Authenticate user and validate workspace access
2. Get workspace info using `ECSWorkspaceService`
3. Perform file operations using `ECSFileSystemService`
4. Handle container lifecycle and preview server management
5. Return structured responses with success/error status

### Service Layer Architecture

#### `BuildService`
- Processes LLM responses and extracts structured content
- Manages BuildResult database operations
- Preserves chat history during build updates

#### `ECSWorkspaceService`
- Manages AWS ECS container lifecycle
- Handles workspace creation, monitoring, and cleanup
- Integrates with AWS ECS APIs for container management

#### `ECSFileSystemService`
- Provides file system operations within ECS containers
- Handles file reading, writing, and directory operations
- Manages file extraction from LLM responses

#### `AnthropicService`
- Integrates with Anthropic Claude API
- Handles prompt formatting and response processing
- Supports mock mode for testing without API keys

#### `ConversationHistoryService`
- Manages conversation context for LLM continuity
- Formats messages for Anthropic API compatibility
- Provides conversation statistics and management

## Detailed API Documentation with cURL Examples

### Authentication
All API endpoints require JWT authentication. Include the token in the Authorization header:
```bash
Authorization: Bearer <your_jwt_token>
```

### Core Build APIs

#### 1. POST /api/build/unified
**Purpose**: Unified initial build process (replaces separate LLM generation + workspace creation)
**Description**: Creates complete project from interview data in a single atomic operation

**Request Body**:
```json
{
  "interviewUuid": "550e8400-e29b-41d4-a716-************",
  "prompt": "Create a React todo app",
  "projectData": {
    "name": "Todo App",
    "description": "A simple todo application",
    "techStack": ["React", "TypeScript", "Material-UI"],
    "features": ["Add tasks", "Mark complete", "Delete tasks"]
  },
  "userData": {
    "email": "<EMAIL>",
    "preferences": {}
  }
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:3001/api/build/unified \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "interviewUuid": "550e8400-e29b-41d4-a716-************",
    "prompt": "Create a React todo app",
    "projectData": {
      "name": "Todo App",
      "techStack": ["React", "TypeScript"]
    }
  }'
```

**Response**:
```json
{
  "success": true,
  "data": {
    "buildResult": {
      "uuid": "build-uuid-here",
      "interviewUuid": "550e8400-e29b-41d4-a716-************",
      "projectStructure": { /* file tree */ },
      "chatHistory": [
        {
          "type": "ai",
          "content": "I've created your React todo app...",
          "timestamp": "2024-01-01T00:00:00.000Z"
        }
      ],
      "ecsWorkspaceId": "workspace_550e8400-e29b-41d4-a716-************"
    }
  }
}
```

**Internal Process**:
1. Validate authentication and request data
2. Check for existing BuildResult to prevent duplicates
3. Generate LLM response using Anthropic Claude API
4. Extract code blocks and create project structure
5. Create ECS workspace container
6. Upload extracted files to ECS workspace
7. Save BuildResult and conversation history to database
8. Return comprehensive response with chat history

#### 2. POST /api/build/chat
**Purpose**: Handle chat interactions with AI for code modifications
**Description**: Processes user messages, generates AI responses, and updates workspace files

**Request Body**:
```json
{
  "interviewUuid": "550e8400-e29b-41d4-a716-************",
  "message": "Add a search feature to filter tasks",
  "conversationHistory": [
    {
      "type": "user",
      "content": "Previous message",
      "timestamp": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:3001/api/build/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "interviewUuid": "550e8400-e29b-41d4-a716-************",
    "message": "Add a search feature to filter tasks"
  }'
```

**Response**:
```json
{
  "success": true,
  "aiMessage": {
    "type": "ai",
    "content": "I'll add a search feature to your todo app...",
    "timestamp": "2024-01-01T00:00:00.000Z"
  },
  "filesUpdated": ["src/components/TodoList.tsx", "src/components/SearchBar.tsx"],
  "workspaceUpdated": true
}
```

**Internal Process**:
1. Validate user authentication and message content
2. Retrieve complete conversation context from database
3. Format conversation for Anthropic API compatibility
4. Generate AI response with file modifications
5. Extract updated files from AI response
6. Save files to ECS workspace using file system service
7. Update BuildResult with new chat messages
8. Save conversation context for future continuity

### ECS Workspace APIs

#### 3. POST /api/ecs-workspace
**Purpose**: Create new ECS workspace container
**Description**: Provisions AWS ECS container for isolated development environment

**Request Body**:
```json
{
  "workspaceName": "workspace_550e8400-e29b-41d4-a716-************",
  "lifetimeHours": 2
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:3001/api/ecs-workspace \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "workspaceName": "workspace_550e8400-e29b-41d4-a716-************",
    "lifetimeHours": 2
  }'
```

**Internal Process**:
1. Extract interview UUID from workspace name
2. Register ECS task definition with workspace-specific environment
3. Create ECS service with auto-scaling configuration
4. Set up networking and security groups
5. Configure EFS volume for persistent storage
6. Save workspace metadata to database
7. Return workspace access information

#### 4. GET /api/ecs-workspace/:workspaceName/structure
**Purpose**: Get workspace file structure
**Description**: Returns hierarchical file tree of workspace contents

**cURL Example**:
```bash
curl -X GET http://localhost:3001/api/ecs-workspace/workspace_550e8400/structure \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Response**:
```json
{
  "success": true,
  "data": {
    "structure": {
      "src": {
        "type": "folder",
        "children": [
          {
            "name": "App.tsx",
            "type": "file",
            "path": "src/App.tsx",
            "size": 1024
          }
        ]
      }
    }
  }
}
```

#### 5. POST /api/ecs-workspace/:workspaceName/files/read
**Purpose**: Read file content from workspace
**Description**: Retrieves content of specific file from ECS container

**Request Body**:
```json
{
  "filePath": "src/App.tsx"
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:3001/api/ecs-workspace/workspace_550e8400/files/read \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"filePath": "src/App.tsx"}'
```

#### 6. POST /api/ecs-workspace/:workspaceName/save
**Purpose**: Save multiple files to workspace
**Description**: Batch save operation for modified files

**Request Body**:
```json
{
  "files": {
    "src/App.tsx": "import React from 'react';\n\nfunction App() {\n  return <div>Hello World</div>;\n}",
    "src/index.tsx": "import React from 'react';\nimport ReactDOM from 'react-dom';"
  }
}
```

### Preview Server APIs

#### 7. POST /api/ecs-workspace/:workspaceName/preview
**Purpose**: Start preview server in workspace
**Description**: Launches development server for live preview

**cURL Example**:
```bash
curl -X POST http://localhost:3001/api/ecs-workspace/workspace_550e8400/preview \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 8. DELETE /api/ecs-workspace/:workspaceName/preview
**Purpose**: Stop preview server
**Description**: Terminates running preview server to free resources

#### 9. GET /api/ecs-workspace/:workspaceName/preview/status
**Purpose**: Check preview server status
**Description**: Returns current status and URL of preview server

### Authentication APIs

#### 10. POST /api/auth/login
**Purpose**: User authentication
**Description**: Authenticates user and returns JWT token

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "leo123"
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "leo123"
  }'
```

**Response**:
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>"
  }
}
```

### Interview APIs

#### 11. GET /api/interview/:uuid
**Purpose**: Load interview configuration
**Description**: Retrieves interview data and project requirements

**cURL Example**:
```bash
curl -X GET http://localhost:3001/api/interview/550e8400-e29b-41d4-a716-************ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 12. GET /api/llm/build-exists/:uuid
**Purpose**: Check for existing build
**Description**: Determines if build already exists for interview to prevent duplicates

**cURL Example**:
```bash
curl -X GET http://localhost:3001/api/llm/build-exists/550e8400-e29b-41d4-a716-************ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Response**:
```json
{
  "success": true,
  "exists": true,
  "buildResult": {
    "uuid": "build-uuid",
    "chatHistory": [/* chat messages */],
    "projectStructure": {/* file structure */}
  }
}
```

## Configuration Parameters and Customization

### Environment Variables

#### Backend Configuration (.env)
```bash
# Database Configuration
MONGODB_URI=mongodb://localhost:27017/mergen_code

# Server Configuration
PORT=3001
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here

# LLM API Configuration
ANTHROPIC_API_KEY=your-anthropic-api-key-here
BYPASS_API_KEY=false  # Set to true for testing with mock responses

# AWS Configuration for ECS Workspaces
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key

# ECS Configuration
ECS_CLUSTER_NAME=mergen-workspace-cluster
ECS_TASK_DEFINITION_FAMILY=mergen-workspace-task
ECS_EXECUTION_ROLE_ARN=arn:aws:iam::YOUR_ACCOUNT_ID:role/ecsTaskExecutionRole
ECS_TASK_ROLE_ARN=arn:aws:iam::YOUR_ACCOUNT_ID:role/ecsTaskRole

# Networking Configuration
VPC_ID=vpc-xxxxxxxxx
SUBNET_IDS=subnet-xxxxxxxxx,subnet-yyyyyyyyy
SECURITY_GROUP_ID=sg-xxxxxxxxx

# EFS Configuration (for persistent file storage)
EFS_FILE_SYSTEM_ID=fs-xxxxxxxxx
EFS_ACCESS_POINT_ID=fsap-xxxxxxxxx

# Container Configuration
CONTAINER_IMAGE=mergen/workspace:latest
CONTAINER_CPU=256
CONTAINER_MEMORY=512
CONTAINER_PORT=3000
PREVIEW_PORT=8080

# Lifecycle Management
DEFAULT_LIFETIME_HOURS=1      # Default workspace lifetime
CLEANUP_INTERVAL_MINUTES=5    # How often to check for expired workspaces
```

#### Frontend Configuration (.env)
```bash
# API Configuration
VITE_API_BASE_URL=http://localhost:3001
VITE_MOCK_MODE=false  # Set to true for development without backend
```

### Key Configuration Parameters

#### ECS Workspace Lifetime
**Location**: `backend/src/config/aws.ts`
**Parameter**: `defaultLifetimeHours`
**Default**: 1 hour
**Description**: How long ECS workspaces remain active before automatic cleanup

**To modify**:
1. Update `DEFAULT_LIFETIME_HOURS` in `.env`
2. Or modify the default in `aws.ts`:
```typescript
defaultLifetimeHours: parseInt(process.env.DEFAULT_LIFETIME_HOURS || '1'),
```

**API Override**: Can be overridden per workspace in creation request:
```json
{
  "workspaceName": "workspace_uuid",
  "lifetimeHours": 4  // Override default
}
```

**Frontend Configuration**: The frontend automatically fetches configuration from `/api/config` endpoint and uses centralized values for:
- Default lifetime display
- Auto-extension duration
- User interface messages

#### File Filtering in Monaco Workspace
**Location**: `frontend/src/components/MonacoWorkspace.tsx`
**Lines**: 295-312
**Description**: Controls which files are hidden from the file explorer

**Current Hidden Files**:
```typescript
const hiddenFiles = [
    // Container system files (at root level)
    'server.js',           // Hides only root server.js, not backend/server.js
    'health.json',         // Hides only root health.json
    'README.md',           // Hides only root README.md, not backend/README.md

    // Specific paths you want to hide:
    'logs',                // Hides logs directory
    'frontend/node_modules',
    'frontend/package-lock.json',
    'backend/node_modules',
    'backend/package-lock.json',

    // Add more exact paths here as needed:
    // 'frontend/.gitignore',
    // 'backend/.env',
    // 'docker-compose.yml',
];
```

**To modify**: Add exact file paths to the `hiddenFiles` array. The system uses exact path matching, so specify complete paths like `'frontend/src/config.ts'` to hide specific files.

#### Container Resource Allocation
**Location**: `backend/src/config/aws.ts`
**Parameters**:
- `containerCpu`: CPU units (256 = 0.25 vCPU)
- `containerMemory`: Memory in MB (512 = 0.5 GB)

**To modify**:
```bash
# In .env file
CONTAINER_CPU=512      # 0.5 vCPU
CONTAINER_MEMORY=1024  # 1 GB
```

#### Cleanup and Monitoring Intervals
**Location**: `backend/src/config/aws.ts`
**Parameters**:
- `cleanupIntervalMinutes`: How often to check for expired workspaces
- `syncIntervalMinutes`: How often to sync workspace state

**To modify**:
```bash
# In .env file
CLEANUP_INTERVAL_MINUTES=10  # Check every 10 minutes
SYNC_INTERVAL_MINUTES=30     # Sync every 30 minutes
```

#### LLM API Configuration
**Location**: `backend/src/services/anthropicService.ts`
**Parameters**:
- Model: `claude-sonnet-4-20250514`
- Max tokens: `64000`
- API endpoint: `https://api.anthropic.com/v1/messages`

**Mock Mode**: Set `BYPASS_API_KEY=true` to use mock responses from:
- Initial builds: `doc/llm_response.json`
- Chat messages: `doc/llm_reply.json`

#### Database Configuration
**Connection**: MongoDB with Mongoose ODM
**Collections**:
- `buildresults`: Build data and chat history
- `conversationhistories`: LLM conversation context
- `interviewconfigs`: Interview data
- `workspaces`: ECS workspace metadata
- `ecscontainers`: Container status and configuration

### Customization Examples

#### Extend Workspace Lifetime for Specific Users
```typescript
// In ecsWorkspaceController.ts
const lifetimeHours = req.user?.isPremium ? 24 : 1; // Premium users get 24 hours
```

#### Add Custom File Types to Filter
```typescript
// In MonacoWorkspace.tsx
const hiddenFiles = [
    ...existingFiles,
    'src/test/',           // Hide test directory
    'package.json',        // Hide package.json
    '.env.local',          // Hide local env files
];
```

#### Modify Container Resources Based on Project Type
```typescript
// In ecsWorkspaceService.ts
const getResourcesForProject = (projectType: string) => {
    switch (projectType) {
        case 'large-app':
            return { cpu: 1024, memory: 2048 };
        case 'microservice':
            return { cpu: 512, memory: 1024 };
        default:
            return { cpu: 256, memory: 512 };
    }
};
```

## Deployment Guide

### Prerequisites

#### AWS Infrastructure Setup
1. **ECS Cluster**:
   ```bash
   aws ecs create-cluster --cluster-name mergen-workspace-cluster
   ```

2. **VPC and Networking**:
   - Create VPC with public and private subnets
   - Configure security groups for ECS tasks
   - Set up NAT Gateway for private subnet internet access

3. **EFS File System** (for persistent storage):
   ```bash
   aws efs create-file-system --creation-token mergen-workspaces
   aws efs create-access-point --file-system-id fs-xxxxxxxxx
   ```

4. **IAM Roles**:
   - `ecsTaskExecutionRole`: For ECS to pull images and write logs
   - `ecsTaskRole`: For tasks to access AWS services

#### Docker Image Preparation
1. **Build Workspace Container**:
   ```bash
   # Create Dockerfile for workspace container
   FROM node:18-alpine
   WORKDIR /workspace
   COPY package*.json ./
   RUN npm install
   COPY . .
   EXPOSE 3000 8080
   CMD ["npm", "start"]
   ```

2. **Push to ECR**:
   ```bash
   aws ecr create-repository --repository-name mergen/workspace
   docker build -t mergen/workspace:latest .
   docker tag mergen/workspace:latest YOUR_ACCOUNT.dkr.ecr.us-east-1.amazonaws.com/mergen/workspace:latest
   docker push YOUR_ACCOUNT.dkr.ecr.us-east-1.amazonaws.com/mergen/workspace:latest
   ```

### EC2 Deployment Steps

#### 1. Launch EC2 Instance
```bash
# Launch Ubuntu 22.04 LTS instance
aws ec2 run-instances \
  --image-id ami-0c02fb55956c7d316 \
  --instance-type t3.medium \
  --key-name your-key-pair \
  --security-group-ids sg-xxxxxxxxx \
  --subnet-id subnet-xxxxxxxxx
```

#### 2. Install Dependencies
```bash
# Connect to EC2 instance
ssh -i your-key.pem ubuntu@your-ec2-ip

# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install MongoDB
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Install PM2 for process management
sudo npm install -g pm2

# Install AWS CLI
sudo apt-get install -y awscli
```

#### 3. Deploy Application
```bash
# Clone repository
git clone https://github.com/your-org/mergen-code.git
cd mergen-code

# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend
npm install
npm run build

# Copy built frontend to backend public directory
cp -r dist/* ../backend/public/
```

#### 4. Configure Environment
```bash
# Create backend .env file
cd ../backend
cp .env.example .env

# Edit .env with your configuration
nano .env
```

#### 5. Start Services
```bash
# Start backend with PM2
pm2 start src/server.ts --name "mergen-backend" --interpreter ts-node

# Configure PM2 to start on boot
pm2 startup
pm2 save

# Check status
pm2 status
```

### ECS Workspace Setup

#### 1. Register Task Definition
```bash
# Update task definition with your account details
cd backend/src/config
cp ecs-task-definition.json ecs-task-definition-prod.json

# Replace placeholders
sed -i 's/ACCOUNT_ID/************/g' ecs-task-definition-prod.json
sed -i 's/PLACEHOLDER_EFS_ID/fs-xxxxxxxxx/g' ecs-task-definition-prod.json
sed -i 's/PLACEHOLDER_ACCESS_POINT_ID/fsap-xxxxxxxxx/g' ecs-task-definition-prod.json

# Register task definition
aws ecs register-task-definition --cli-input-json file://ecs-task-definition-prod.json
```

#### 2. Configure Auto Scaling
```bash
# Create auto scaling target
aws application-autoscaling register-scalable-target \
  --service-namespace ecs \
  --resource-id service/mergen-workspace-cluster/workspace-service \
  --scalable-dimension ecs:service:DesiredCount \
  --min-capacity 0 \
  --max-capacity 10

# Create scaling policy
aws application-autoscaling put-scaling-policy \
  --service-namespace ecs \
  --resource-id service/mergen-workspace-cluster/workspace-service \
  --scalable-dimension ecs:service:DesiredCount \
  --policy-name workspace-scaling-policy \
  --policy-type TargetTrackingScaling \
  --target-tracking-scaling-policy-configuration file://scaling-policy.json
```

### Production Configuration

#### 1. Environment Variables for Production
```bash
# Backend .env for production
NODE_ENV=production
PORT=3001
MONGODB_URI=mongodb://localhost:27017/mergen_code_prod

# Security
JWT_SECRET=your-super-secure-production-jwt-secret

# AWS Configuration
AWS_REGION=us-east-1
ECS_CLUSTER_NAME=mergen-workspace-cluster-prod

# Resource limits for production
CONTAINER_CPU=512
CONTAINER_MEMORY=1024
DEFAULT_LIFETIME_HOURS=1
CLEANUP_INTERVAL_MINUTES=10
```

#### 2. Nginx Configuration (Optional)
```nginx
# /etc/nginx/sites-available/mergen
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

#### 3. SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Monitoring and Logging

#### 1. CloudWatch Logs
```bash
# Create log group
aws logs create-log-group --log-group-name /ecs/mergen-workspace

# Configure log retention
aws logs put-retention-policy \
  --log-group-name /ecs/mergen-workspace \
  --retention-in-days 30
```

#### 2. Application Monitoring
```bash
# PM2 monitoring
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 7

# System monitoring
sudo apt install htop iotop
```

### Backup and Recovery

#### 1. Database Backup
```bash
# Create backup script
cat > /home/<USER>/backup-mongo.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mongodump --db mergen_code_prod --out /home/<USER>/backups/mongo_$DATE
find /home/<USER>/backups -name "mongo_*" -mtime +7 -exec rm -rf {} \;
EOF

chmod +x /home/<USER>/backup-mongo.sh

# Schedule daily backups
crontab -e
# Add: 0 2 * * * /home/<USER>/backup-mongo.sh
```

#### 2. EFS Backup
```bash
# Enable automatic backups for EFS
aws efs put-backup-policy \
  --file-system-id fs-xxxxxxxxx \
  --backup-policy Status=ENABLED
```

### Security Considerations

#### 1. Network Security
- Use private subnets for ECS tasks
- Configure security groups with minimal required ports
- Enable VPC Flow Logs for monitoring

#### 2. Application Security
- Use strong JWT secrets
- Enable HTTPS in production
- Implement rate limiting
- Sanitize user inputs

#### 3. AWS Security
- Use IAM roles with minimal permissions
- Enable CloudTrail for API logging
- Configure AWS Config for compliance monitoring

## Data Models and Database Schema

### BuildResult Collection
**Purpose**: Stores complete build information and chat history
**Key Fields**:
- `uuid`: Unique identifier for the build
- `interviewUuid`: Links to interview configuration
- `rawLLMResponse`: Original LLM response text
- `codeBlocks`: Extracted code files with metadata
- `projectStructure`: Hierarchical project organization
- `chatHistory`: Array of chat messages for UI display
- `ecsWorkspaceId`: Associated ECS workspace identifier
- `status`: Build processing status (processing/completed/failed)

### ConversationHistory Collection
**Purpose**: Stores conversation context for LLM API continuity
**Key Fields**:
- `interviewUuid`: Links conversations to specific interviews
- `messageType`: 'user' or 'assistant' for role-based context
- `content`: Full message content for LLM context
- `messageOrder`: Sequence number for proper ordering
- `inputTokens/outputTokens`: Token usage tracking

### InterviewConfig Collection
**Purpose**: Stores user requirements and project configuration
**Key Fields**:
- `uuid`: Unique identifier for the interview
- `user`: User information and prompt
- `projectData`: Structured technical and functional requirements
- `createdAt/updatedAt`: Timestamp tracking

### Workspace Collection
**Purpose**: Tracks ECS workspace metadata and lifecycle
**Key Fields**:
- `workspaceId`: ECS workspace identifier
- `userId`: Owner of the workspace
- `interviewUuid`: Associated interview
- `ecsContainerId`: AWS ECS container ID
- `status`: Container status (creating/running/stopped/failed)
- `expiresAt`: Automatic cleanup timestamp

### ECSContainer Collection
**Purpose**: Stores ECS container configuration and monitoring data
**Key Fields**:
- `containerId`: AWS ECS container identifier
- `taskDefinitionArn`: ECS task definition reference
- `serviceArn`: ECS service reference
- `status`: Container lifecycle status
- `previewUrl`: Dynamic preview server URL

## Architecture Optimization Opportunities

### Performance Improvements
1. **Caching Strategy**: Implement Redis caching for frequently accessed data
   - Cache BuildResult data to reduce database queries
   - Cache ECS workspace status to reduce AWS API calls
   - Cache conversation context for faster LLM API preparation

2. **Database Optimization**:
   - Add compound indexes on frequently queried fields
   - Implement database connection pooling
   - Consider read replicas for heavy read operations

3. **Frontend Optimization**:
   - Implement virtual scrolling for large file trees
   - Add debouncing for file save operations
   - Use React.memo for expensive component renders
   - Implement code splitting for Monaco Editor

### API Design Improvements
1. **Unified Endpoints**: Continue consolidating related operations
   - Combine file operations into batch endpoints
   - Create composite endpoints for common workflows

2. **Real-time Updates**: Implement WebSocket connections
   - Real-time build progress updates
   - Live collaboration features
   - Instant file synchronization

3. **Error Handling**: Standardize error responses
   - Consistent error format across all endpoints
   - Proper HTTP status codes
   - Detailed error messages for debugging

### Code Organization
1. **Service Layer**: Extract more business logic into services
   - Create dedicated services for file operations
   - Implement proper dependency injection
   - Add service-level caching

2. **Frontend Architecture**:
   - Implement proper state management (Redux/Zustand)
   - Create custom hooks for API operations
   - Add proper TypeScript interfaces for all data

3. **Configuration Management**:
   - Centralize environment configuration
   - Implement feature flags for gradual rollouts
   - Add configuration validation

## Cleanup Recommendations

### ✅ Completed Cleanup
1. **Sphere Engine Integration** (REMOVED):
   - ✅ Removed `frontend/src/components/SphereEngineWorkspace.tsx`
   - ✅ Removed `frontend/src/utils/sphereEngineApi.ts`
   - ✅ Removed `frontend/src/pages/SphereEngineLiveCoding.tsx`
   - ✅ Removed `backend/src/controllers/sphereEngineController.ts`
   - ✅ Removed `backend/src/routes/sphereEngineRoutes.ts`
   - ✅ Removed `frontend/src/components/FileManager.tsx`
   - ✅ Removed `frontend/src/components/FileUpload.tsx`
   - ✅ Cleaned all Sphere Engine imports and comments
   - ✅ Removed Sphere Engine environment variables
   - ✅ Updated Vite configuration in Anthropic service

2. **Legacy Workspace System**:
   - `backend/src/routes/workspaceRoutes.ts` (replaced by ECS)
   - `backend/src/controllers/workspaceController.ts`
   - Local filesystem workspace utilities

3. **Mock Data and Test Files**:
   - `doc/generated_code2/` (old mock data)
   - `doc/response1/` (old response format)
   - `doc/sample_response/` (deprecated samples)
   - Various test files in `backend/test-*.js`

### Remaining Cleanup Tasks
1. **Configuration Consolidation**:
   - Standardize naming conventions
   - Remove unused environment variables
   - Add proper validation for required variables

### Database Cleanup
1. **Remove Unused Collections**:
   - Legacy workspace records (if any)
   - Old Sphere Engine workspace data

2. **Optimize Indexes**:
   - Remove unused indexes
   - Add missing indexes for performance
   - Review compound index efficiency

### Dependencies Cleanup
1. **Frontend Dependencies**:
   - Remove Sphere Engine SDK dependencies
   - Update outdated packages
   - Remove unused utility libraries

2. **Backend Dependencies**:
   - Remove Sphere Engine API clients
   - Clean up unused AWS SDK modules
   - Update security-vulnerable packages

---

## Deployment Readiness Assessment

### ✅ Ready for Deployment

#### Core Functionality
- **Authentication System**: JWT-based auth with login/signup ✅
- **Build System**: Unified build API with LLM integration ✅
- **ECS Workspace Management**: Container lifecycle management ✅
- **File System Operations**: Read/write/save operations ✅
- **Chat System**: AI conversation with context persistence ✅
- **Preview Server**: Live preview functionality ✅
- **Database Models**: Complete schema with relationships ✅

#### Infrastructure Components
- **Backend API**: Express.js with TypeScript ✅
- **Frontend**: React with TypeScript and Material-UI ✅
- **Database**: MongoDB with Mongoose ODM ✅
- **Container Orchestration**: AWS ECS integration ✅
- **File Storage**: EFS for persistent workspace storage ✅
- **Monitoring**: CloudWatch logs and health checks ✅

#### Security Features
- **Authentication**: JWT token validation ✅
- **Authorization**: User-scoped workspace access ✅
- **Input Validation**: Request validation middleware ✅
- **Error Handling**: Structured error responses ✅

### ⚠️ Needs Attention Before Production

#### Security Enhancements
- **Rate Limiting**: Implement API rate limiting for abuse prevention
- **Input Sanitization**: Add comprehensive input sanitization
- **CORS Configuration**: Properly configure CORS for production domains
- **Environment Secrets**: Use AWS Secrets Manager for sensitive data
- **API Key Rotation**: Implement Anthropic API key rotation

#### Performance Optimizations
- **Database Indexing**: Add compound indexes for frequent queries
- **Caching Layer**: Implement Redis for session and data caching
- **CDN Integration**: Set up CloudFront for static asset delivery
- **Connection Pooling**: Configure database connection pooling
- **Resource Limits**: Implement proper memory and CPU limits

#### Monitoring and Observability
- **Application Metrics**: Add Prometheus/Grafana monitoring
- **Error Tracking**: Integrate Sentry for error tracking
- **Performance Monitoring**: Add APM tools (New Relic/DataDog)
- **Health Checks**: Implement comprehensive health check endpoints
- **Alerting**: Set up CloudWatch alarms for critical metrics

#### Scalability Preparations
- **Load Balancing**: Configure Application Load Balancer
- **Auto Scaling**: Set up ECS service auto-scaling
- **Database Scaling**: Consider MongoDB Atlas or replica sets
- **Session Management**: Implement distributed session storage
- **Queue System**: Add job queue for heavy operations

### 🔧 Configuration Required

#### AWS Infrastructure
```bash
# Required AWS resources to create:
1. ECS Cluster with Fargate capacity
2. VPC with public/private subnets
3. EFS file system with access points
4. IAM roles for ECS tasks
5. Security groups for container networking
6. Application Load Balancer (optional)
7. CloudWatch log groups
```

#### Environment Variables
```bash
# Critical environment variables to set:
ANTHROPIC_API_KEY=your-production-api-key
JWT_SECRET=your-production-jwt-secret
MONGODB_URI=your-production-mongodb-uri
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
ECS_CLUSTER_NAME=your-production-cluster
EFS_FILE_SYSTEM_ID=your-efs-id
```

#### Domain and SSL
```bash
# Domain configuration needed:
1. Register domain name
2. Configure DNS records
3. Set up SSL certificate (Let's Encrypt or ACM)
4. Update CORS origins in backend
5. Update frontend API base URL
```

### 📋 Deployment Checklist

#### Pre-Deployment
- [ ] Set up AWS infrastructure (ECS, EFS, VPC, IAM)
- [ ] Create production MongoDB database
- [ ] Configure environment variables
- [ ] Build and push Docker images to ECR
- [ ] Set up domain and SSL certificates
- [ ] Configure monitoring and logging
- [ ] Run security audit
- [ ] Perform load testing

#### Deployment Steps
- [ ] Deploy backend to EC2/ECS
- [ ] Deploy frontend (build and serve static files)
- [ ] Configure reverse proxy (Nginx)
- [ ] Set up database migrations
- [ ] Configure backup procedures
- [ ] Test all critical workflows
- [ ] Set up monitoring dashboards
- [ ] Configure alerting rules

#### Post-Deployment
- [ ] Monitor application performance
- [ ] Check error rates and logs
- [ ] Verify backup procedures
- [ ] Test disaster recovery
- [ ] Update documentation
- [ ] Train operations team
- [ ] Set up maintenance procedures

### 🚨 Critical Issues to Address

#### Data Persistence
- **Workspace Cleanup**: Ensure proper cleanup of expired workspaces
- **File Backup**: Implement regular backup of workspace files
- **Data Recovery**: Test data recovery procedures

#### Error Handling
- **Graceful Degradation**: Handle LLM API failures gracefully
- **Retry Logic**: Implement retry logic for transient failures
- **Circuit Breakers**: Add circuit breakers for external services

#### Resource Management
- **Memory Leaks**: Monitor for memory leaks in long-running processes
- **Container Limits**: Set appropriate resource limits for containers
- **Cleanup Jobs**: Implement cleanup jobs for orphaned resources

### 💡 Recommended Deployment Strategy

#### Phase 1: Basic Deployment
1. Deploy to staging environment
2. Test core functionality
3. Fix critical issues
4. Deploy to production with limited users

#### Phase 2: Production Hardening
1. Implement monitoring and alerting
2. Add performance optimizations
3. Enhance security measures
4. Scale infrastructure as needed

#### Phase 3: Advanced Features
1. Add advanced monitoring
2. Implement CI/CD pipeline
3. Add automated testing
4. Optimize for scale

### 🔍 Testing Strategy

#### Unit Tests
- API endpoint testing
- Service layer testing
- Database model testing
- Utility function testing

#### Integration Tests
- End-to-end workflow testing
- ECS workspace lifecycle testing
- File system operation testing
- Authentication flow testing

#### Load Tests
- Concurrent user simulation
- API endpoint load testing
- Database performance testing
- Container scaling testing

## Advanced Optimization Suggestions

### 🔍 Non-Backend API Calls Analysis

#### Direct Fetch Calls Found
The frontend currently has **4 direct fetch calls** that bypass the centralized API utility:

1. **Build Page - Chat History Loading** (`frontend/src/pages/Build/index.tsx:177`):
   ```javascript
   const response = await fetch(`http://localhost:3001/api/llm/build-exists/${uuid}`);
   ```

2. **Build Page - Existing Build Check** (`frontend/src/pages/Build/index.tsx:364`):
   ```javascript
   const existingBuildResponse = await fetch(`http://localhost:3001/api/llm/build-exists/${uuid}`);
   ```

3. **Build Page - Pre-Build Validation** (`frontend/src/pages/Build/index.tsx:783`):
   ```javascript
   const checkResponse = await fetch(`http://localhost:3001/api/llm/build-exists/${uuid}`);
   ```

4. **Build Page - Chat Message Submission** (`frontend/src/pages/Build/index.tsx:976`):
   ```javascript
   const response = await fetch(`http://localhost:3001/api/build/chat`, { ... });
   ```

#### ✅ Recommendation: Centralize API Calls
Replace direct fetch calls with centralized API methods:

```typescript
// Replace direct fetch calls with:
const response = await chatAPI.checkBuildExists(uuid);
const chatResponse = await buildAPI.chat({ interviewUuid: uuid, message, conversationHistory });
```

**Benefits**:
- Consistent error handling and authentication
- Centralized request/response interceptors
- Better debugging and logging
- Easier testing and mocking

### 🔄 Duplicated Processes Analysis

#### 1. Build Existence Checking
**Duplication**: The same build existence check is performed **3 times** in the Build page:
- Line 177: Chat history loading
- Line 364: Initial build check
- Line 783: Pre-build validation

**Optimization**: Create a single cached check:
```typescript
const useBuildExistence = (uuid: string) => {
  const [buildExists, setBuildExists] = useState<boolean | null>(null);
  const [buildData, setBuildData] = useState<any>(null);

  const checkBuildExists = useCallback(async () => {
    if (buildExists !== null) return { exists: buildExists, data: buildData };

    const response = await chatAPI.checkBuildExists(uuid);
    setBuildExists(response.exists);
    setBuildData(response.buildResult);
    return response;
  }, [uuid, buildExists, buildData]);

  return { buildExists, buildData, checkBuildExists };
};
```

#### 2. Authentication Token Handling
**Duplication**: Token retrieval and auth error handling is duplicated across:
- `buildAPI.generateCode()` (lines 259-279)
- `buildAPI.chat()` (lines 290-310)
- Direct fetch calls in Build page

**Optimization**: Create a centralized authenticated fetch utility:
```typescript
const authenticatedFetch = async (url: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('token');
  if (!token) throw new Error('Authentication required');

  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers
    }
  });

  if (response.status === 401) {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    window.location.href = '/login';
    throw new Error('Authentication expired');
  }

  return response;
};
```

#### 3. File Content Caching
**Duplication**: File content is fetched multiple times without proper caching in Monaco workspace.

**Optimization**: Implement intelligent caching:
```typescript
const useFileCache = () => {
  const [cache, setCache] = useState<Map<string, { content: string, timestamp: number }>>(new Map());

  const getCachedFile = (filePath: string, maxAge = 5 * 60 * 1000) => {
    const cached = cache.get(filePath);
    if (cached && Date.now() - cached.timestamp < maxAge) {
      return cached.content;
    }
    return null;
  };

  const setCachedFile = (filePath: string, content: string) => {
    setCache(prev => new Map(prev).set(filePath, { content, timestamp: Date.now() }));
  };

  return { getCachedFile, setCachedFile };
};
```

### 🚀 Performance Optimization Opportunities

#### 1. React Performance
**Current Issues**:
- No memoization of expensive components
- Unnecessary re-renders in Monaco workspace
- Large state objects causing full re-renders

**Optimizations**:
```typescript
// Memoize expensive components
const MemoizedMonacoEditor = React.memo(MonacoEditor, (prevProps, nextProps) => {
  return prevProps.value === nextProps.value &&
         prevProps.language === nextProps.language;
});

// Use useCallback for event handlers
const handleFileSelect = useCallback((filePath: string) => {
  // File selection logic
}, []);

// Split large state objects
const useWorkspaceState = () => {
  const [files, setFiles] = useState({});
  const [selectedFile, setSelectedFile] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Return individual setters to prevent unnecessary re-renders
  return { files, selectedFile, hasUnsavedChanges, setFiles, setSelectedFile, setHasUnsavedChanges };
};
```

#### 2. API Request Optimization
**Current Issues**:
- No request deduplication
- No request cancellation
- No retry logic for failed requests

**Optimizations**:
```typescript
// Request deduplication
const useRequestDeduplication = () => {
  const pendingRequests = useRef<Map<string, Promise<any>>>(new Map());

  const makeRequest = async (key: string, requestFn: () => Promise<any>) => {
    if (pendingRequests.current.has(key)) {
      return pendingRequests.current.get(key);
    }

    const promise = requestFn().finally(() => {
      pendingRequests.current.delete(key);
    });

    pendingRequests.current.set(key, promise);
    return promise;
  };

  return { makeRequest };
};

// Request cancellation
const useAbortableRequest = () => {
  const abortController = useRef<AbortController>();

  useEffect(() => {
    return () => {
      abortController.current?.abort();
    };
  }, []);

  const makeRequest = async (url: string, options: RequestInit = {}) => {
    abortController.current?.abort();
    abortController.current = new AbortController();

    return fetch(url, {
      ...options,
      signal: abortController.current.signal
    });
  };

  return { makeRequest };
};
```

#### 3. Bundle Size Optimization
**Current Issues**:
- Monaco Editor loaded synchronously
- All Material-UI components imported
- No code splitting for routes

**Optimizations**:
```typescript
// Lazy load Monaco Editor
const MonacoEditor = lazy(() => import('@monaco-editor/react'));

// Tree-shake Material-UI imports
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
// Instead of: import { Button, TextField } from '@mui/material';

// Code splitting for routes
const BuildPage = lazy(() => import('./pages/Build'));
const InterviewPage = lazy(() => import('./pages/Interview'));
```

### 🔧 Architecture Improvements

#### 1. State Management
**Current Issue**: Props drilling and scattered state management

**Recommendation**: Implement Zustand for global state:
```typescript
import { create } from 'zustand';

interface WorkspaceStore {
  workspaces: Map<string, WorkspaceData>;
  currentWorkspace: string | null;
  setCurrentWorkspace: (id: string) => void;
  updateWorkspace: (id: string, data: Partial<WorkspaceData>) => void;
}

const useWorkspaceStore = create<WorkspaceStore>((set) => ({
  workspaces: new Map(),
  currentWorkspace: null,
  setCurrentWorkspace: (id) => set({ currentWorkspace: id }),
  updateWorkspace: (id, data) => set((state) => {
    const newWorkspaces = new Map(state.workspaces);
    newWorkspaces.set(id, { ...newWorkspaces.get(id), ...data });
    return { workspaces: newWorkspaces };
  }),
}));
```

#### 2. Error Boundary Implementation
**Current Issue**: No error boundaries for graceful error handling

**Recommendation**: Add error boundaries:
```typescript
class WorkspaceErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Workspace error:', error, errorInfo);
    // Send to error tracking service
  }

  render() {
    if (this.state.hasError) {
      return <WorkspaceErrorFallback error={this.state.error} />;
    }

    return this.props.children;
  }
}
```

#### 3. Custom Hooks for Business Logic
**Current Issue**: Business logic mixed with UI components

**Recommendation**: Extract to custom hooks:
```typescript
// useWorkspaceSync.ts
const useWorkspaceSync = (workspaceId: string) => {
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'error'>('idle');

  const syncWorkspace = useCallback(async () => {
    setSyncStatus('syncing');
    try {
      await ecsWorkspaceAPI.syncWorkspace(workspaceId);
      setSyncStatus('idle');
    } catch (error) {
      setSyncStatus('error');
      throw error;
    }
  }, [workspaceId]);

  return { syncStatus, syncWorkspace };
};

// useChatHistory.ts
const useChatHistory = (interviewUuid: string) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const loadHistory = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await chatAPI.getHistory(interviewUuid);
      setMessages(response.data);
    } finally {
      setIsLoading(false);
    }
  }, [interviewUuid]);

  const addMessage = useCallback((message: ChatMessage) => {
    setMessages(prev => [...prev, message]);
  }, []);

  return { messages, isLoading, loadHistory, addMessage };
};
```

### 📊 Monitoring and Analytics

#### 1. Performance Monitoring
**Recommendation**: Add performance tracking:
```typescript
// Performance monitoring utility
const usePerformanceMonitoring = () => {
  const trackOperation = useCallback((operationName: string, fn: () => Promise<any>) => {
    const startTime = performance.now();

    return fn().finally(() => {
      const duration = performance.now() - startTime;
      console.log(`Operation ${operationName} took ${duration}ms`);

      // Send to analytics service
      if (duration > 1000) {
        console.warn(`Slow operation detected: ${operationName} (${duration}ms)`);
      }
    });
  }, []);

  return { trackOperation };
};
```

#### 2. User Analytics
**Recommendation**: Track user interactions:
```typescript
// Analytics utility
const useAnalytics = () => {
  const trackEvent = useCallback((event: string, properties?: Record<string, any>) => {
    // Send to analytics service (e.g., Mixpanel, Google Analytics)
    console.log('Analytics event:', event, properties);
  }, []);

  const trackPageView = useCallback((page: string) => {
    trackEvent('page_view', { page });
  }, [trackEvent]);

  const trackWorkspaceAction = useCallback((action: string, workspaceId: string) => {
    trackEvent('workspace_action', { action, workspaceId });
  }, [trackEvent]);

  return { trackEvent, trackPageView, trackWorkspaceAction };
};
```

### 🔒 Security Enhancements

#### 1. Content Security Policy
**Recommendation**: Implement CSP headers:
```typescript
// In server.ts
app.use((req, res, next) => {
  res.setHeader('Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
    "style-src 'self' 'unsafe-inline'; " +
    "connect-src 'self' https://api.anthropic.com;"
  );
  next();
});
```

#### 2. Input Sanitization
**Recommendation**: Add comprehensive input validation:
```typescript
// Input sanitization utility
import DOMPurify from 'dompurify';

const sanitizeInput = (input: string): string => {
  return DOMPurify.sanitize(input, { ALLOWED_TAGS: [] });
};

const validateFileContent = (content: string): boolean => {
  // Check for malicious patterns
  const maliciousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i
  ];

  return !maliciousPatterns.some(pattern => pattern.test(content));
};
```

This comprehensive assessment provides a clear roadmap for deploying the Mergen-AI platform to production while ensuring reliability, security, and scalability.