# Complete Loading Page to Monaco Workspace Workflow

## **Overview**

This document explains the complete workflow from when a user starts a build until they see the Monaco workspace with all files loaded correctly.

## **High-Level Flow**

```
User Starts Build → LLM Generates Code → Files Uploaded to ECS → Container Becomes Accessible → Monaco Workspace Shows
```

## **Detailed Workflow**

### **Phase 1: Build Initiation**
1. **User submits build request** → Frontend calls `/api/build/unified`
2. **Backend processes request** → LLM generates code structure
3. **Files extracted** → Code blocks converted to file structure
4. **ECS workspace created** → Container spun up in AWS ECS

### **Phase 2: File Upload**
1. **Files uploaded to ECS container** → Backend uploads generated files
2. **Upload verification** → Backend verifies files are accessible in container
3. **Build response sent** → Frontend receives LLM response + upload status

### **Phase 3: Frontend State Management**
1. **LLM completion** → `hasCodeGenerated = true`, `buildProgress.llmCompleted = true`
2. **Workspace creation** → `buildProgress.workspaceCreating = true`
3. **Polling starts** → Automatic polling begins to verify container accessibility

### **Phase 4: Container Accessibility Verification**
1. **Polling checks container** → HTTP request to `http://{publicIp}:3000/health`
2. **Container responds** → Health check passes
3. **File structure verified** → Backend recursively counts all files
4. **3-second delay** → Ensures all files are fully available
5. **Workspace ready** → `isWorkspaceReady = true`, `buildProgress.workspaceCompleted = true`

### **Phase 5: Monaco Workspace Display**
1. **Loading page disappears** → Both `hasCodeGenerated` and `isWorkspaceReady` are true
2. **Monaco component mounts** → Fetches workspace structure from ECS
3. **File structure loaded** → All files displayed in file explorer
4. **Workspace ready** → User can edit files

## **Key Components**

### **Loading Page Logic**
```typescript
const shouldShowLoading = !hasCodeGenerated || !isWorkspaceReady;

// Shows loading until BOTH conditions are met:
// 1. LLM has generated code (hasCodeGenerated = true)
// 2. ECS container is accessible with files (isWorkspaceReady = true)
```

### **Build Progress States**
```typescript
{
  llmGenerating: boolean,    // LLM is processing request
  llmCompleted: boolean,     // LLM has finished generating code
  workspaceCreating: boolean, // ECS container being set up
  workspaceCompleted: boolean, // ECS container accessible with files
  completed: boolean         // Everything ready
}
```

### **Polling Mechanism**
- **Frequency:** Every 5 seconds
- **Duration:** Up to 5 minutes (60 attempts)
- **Checks:** Container health + file availability
- **Success:** Container accessible AND has files
- **Fallback:** Safety timeout after 10 minutes

## **Backend APIs**

### **Build API (`/api/build/unified`)**
**Purpose:** Generate code and upload to ECS workspace
**Response:**
```json
{
  "success": true,
  "buildResult": {
    "uuid": "...",
    "codeBlocks": [...],
    "chatHistory": [...]
  },
  "workspace": {
    "uploadSuccess": true,
    "filesUploaded": 16,
    "ecsReady": false  // Never set to true in build process
  }
}
```

### **Status API (`/api/ecs-workspace/{name}/status`)**
**Purpose:** Verify container accessibility and file availability
**Process:**
1. Check database for workspace info
2. HTTP health check to container
3. Get workspace structure from container
4. Recursively count all files
5. Return comprehensive status

**Response:**
```json
{
  "success": true,
  "data": {
    "workspaceId": "...",
    "containerStatus": "running",
    "publicIp": "*******",
    "isReady": true,      // Container accessible via HTTP
    "hasFiles": true,     // Files available in container
    "fileCount": 16,      // Recursive count of all files
    "expiresAt": "..."
  }
}
```

## **File Counting Logic**

### **Problem Solved**
Previously, file counting used shallow counting:
```typescript
// WRONG: Only counted top-level entries
fileCount = Object.keys(structure).length; // Returns 6
```

### **Current Solution**
Now uses recursive counting:
```typescript
// CORRECT: Recursively counts all files
fileCount = countFilesInStructure(structure); // Returns 16

function countFilesInStructure(structure) {
  // Recursively traverses folders and counts actual files
}
```

## **Timing and Delays**

### **3-Second Delay in Polling**
**Purpose:** Ensure all files are fully available before showing Monaco
**Location:** After container accessibility is verified
**Reason:** Container might be accessible but still processing files

### **Monaco Workspace Retry**
**Purpose:** Handle any remaining timing issues
**Process:** 
1. Initial load with 2-second retry
2. Up to 3 retry attempts
**Fallback:** Manual refresh button in Monaco workspace

## **Error Handling**

### **Polling Failures**
- **Max attempts:** 60 (5 minutes)
- **Consecutive errors:** Stop after 5 consecutive failures
- **Fallback:** Safety timeout shows workspace anyway

### **Container Issues**
- **Health check fails:** Mark as `failed` status
- **Files not available:** Continue polling
- **Network errors:** Retry with exponential backoff

### **Frontend Resilience**
- **Safety timeout:** 10 minutes maximum loading time
- **State recovery:** Immediate status check on component mount
- **Manual refresh:** Available in Monaco workspace

## **Success Criteria**

### **Loading Page Shows Correctly**
- ✅ AI generating code (when LLM running)
- ✅ Setting up workspace environment (when polling)
- ✅ Preparing workspace container (when container ready)

### **Automatic Transition**
- ✅ No manual refresh needed
- ✅ All files visible immediately in Monaco
- ✅ Consistent file count between backend and frontend

### **Performance**
- ✅ Typical load time: 30-60 seconds
- ✅ Maximum load time: 10 minutes (with fallback)
- ✅ Efficient polling: 5-second intervals

## **Monitoring and Debugging**

### **Key Console Logs**
```
🚀 AUTO POLLING: Starting polling after build response
🔍 POLLING ATTEMPT: ECS workspace polling attempt 1/60
📊 POLLING RESPONSE: ECS container status: { isReady: true, fileCount: 16 }
⏳ ECS workspace ready, waiting 3 seconds for all files to be fully available...
✅ ECS workspace polling: Setting isWorkspaceReady=true after delay
📁 Loaded 16 files from ECS workspace
```

### **Health Checks**
- **Backend:** Container HTTP health endpoint
- **Frontend:** Polling heartbeat logs
- **Files:** Recursive file count verification

## **Architecture Benefits**

1. **Separation of Concerns:** LLM completion and workspace readiness are independent
2. **Robust Verification:** Multiple layers of container and file verification
3. **User Experience:** Clear loading states with progress indicators
4. **Reliability:** Multiple fallback mechanisms prevent infinite loading
5. **Scalability:** Efficient polling with reasonable timeouts

This workflow ensures users get a smooth, reliable experience from build initiation to workspace readiness.