# S3 Setup Instructions for Mergen ECS Workspace Migration

## Overview

This document provides step-by-step instructions for setting up AWS S3 to store ECS workspace files instead of storing them in the MongoDB database.

## 1. AWS S3 Bucket Setup

### Create the S3 Bucket

1. **Login to AWS Console** and navigate to S3
2. **Create a new bucket** with the following settings:
   - **Bucket name**: `mergen-code`
   - **Region**: `us-east-1` (US East - N. Virginia)
   - **Block Public Access**: Keep all settings enabled (recommended for security)
   - **Bucket Versioning**: Enable (optional, for file history)
   - **Default Encryption**: Enable with SSE-S3 (recommended)

### Configure Bucket Policy (Optional)

If you want to restrict access to specific IAM users/roles, you can add a bucket policy:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "MergenWorkspaceAccess",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::YOUR_ACCOUNT_ID:user/YOUR_IAM_USER"
      },
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::mergen-code",
        "arn:aws:s3:::mergen-code/*"
      ]
    }
  ]
}
```

## 2. IAM User/Role Setup

### Create IAM User (if not using existing credentials)

1. **Navigate to IAM** in AWS Console
2. **Create a new user** with programmatic access
3. **Attach the following policy** (inline or managed):

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket",
        "s3:GetBucketLocation"
      ],
      "Resource": [
        "arn:aws:s3:::mergen-code",
        "arn:aws:s3:::mergen-code/*"
      ]
    }
  ]
}
```

4. **Save the Access Key ID and Secret Access Key** - you'll need these for the environment variables

### Update Existing IAM User/Role

If you're using existing AWS credentials, ensure they have the S3 permissions listed above for the `mergen-code` bucket.

## 3. Environment Variables

Update your backend `.env` file with the following variables:

```bash
# AWS Configuration (required for S3)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_id_here
AWS_SECRET_ACCESS_KEY=your_secret_access_key_here

# Existing ECS variables (keep these as they are)
ECS_CLUSTER_NAME=your_existing_cluster_name
ECS_EXECUTION_ROLE_ARN=your_existing_execution_role_arn
ECS_TASK_ROLE_ARN=your_existing_task_role_arn
VPC_ID=your_existing_vpc_id
SUBNET_IDS=your_existing_subnet_ids
SECURITY_GROUP_ID=your_existing_security_group_id
```

**Important**: The AWS credentials you use must have both ECS permissions (for existing functionality) AND S3 permissions (for the new file storage).

## 4. Test S3 Configuration

Before running the migration, test that your S3 setup works:

```bash
cd backend
npm run test-s3
```

This will:
- Upload test files to S3
- Download them back
- List files
- Clean up test files
- Verify all operations work correctly

Expected output:
```
🧪 Testing S3 File Service...
📤 Testing file upload...
📋 Testing file listing...
📥 Testing single file download...
📥 Testing all files download...
🔍 Testing file exists...
🗑️ Testing file cleanup...
✅ All S3 tests passed!
🎉 S3 service test completed successfully
```

## 5. Migration Process

Once S3 is configured and tested:

### 5.1 Backup Database
```bash
mongodump --uri="your_mongodb_connection_string" --out=backup_before_s3_migration
```

### 5.2 Run Migration
```bash
cd backend
npm run migrate-s3 migrate-all
```

### 5.3 Verify Migration
```bash
# Verify a specific workspace
npm run migrate-s3 verify <workspace_id>

# Check S3 bucket in AWS Console to see uploaded files
```

## 6. File Structure in S3

After migration, files will be organized as:

```
mergen-code/
├── <EMAIL>/
│   ├── abc123/                    # workspace without "workspace_" prefix
│   │   ├── src/
│   │   │   ├── index.js
│   │   │   └── utils.js
│   │   ├── package.json
│   │   └── README.md
│   └── def456/
│       └── main.py
└── <EMAIL>/
    └── ghi789/
        └── app.js
```

## 7. Monitoring and Costs

### S3 Costs
- **Storage**: ~$0.023 per GB per month (Standard tier)
- **Requests**: ~$0.0004 per 1,000 PUT requests, ~$0.0004 per 10,000 GET requests
- **Data Transfer**: Free within same region

### Monitoring
- Monitor S3 usage in AWS Console
- Set up CloudWatch alarms for unusual activity
- Review costs regularly in AWS Billing

## 8. Troubleshooting

### Common Issues

**Error: "Access Denied"**
- Check IAM permissions for S3 bucket access
- Verify bucket name and region are correct
- Ensure AWS credentials are valid

**Error: "Bucket does not exist"**
- Verify bucket name is exactly `mergen-code`
- Check that bucket is in `us-east-1` region
- Ensure bucket was created successfully

**Error: "Invalid AWS credentials"**
- Check `.env` file has correct AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY
- Verify credentials are active and not expired
- Test credentials with AWS CLI: `aws s3 ls s3://mergen-code`

**Migration fails partway through**
- Check migration logs for specific errors
- Verify database connectivity
- Ensure sufficient S3 permissions
- Run migration again (it will skip already migrated workspaces)

### Rollback (Emergency Only)

If you need to rollback to database storage:

```bash
# Rollback a specific workspace
npm run migrate-s3 rollback <workspace_id>
```

**Warning**: This downloads files from S3 back to the database, which defeats the purpose of the migration. Only use in emergencies.

## 9. Benefits After Migration

1. **Reduced Database Size**: Files no longer stored in MongoDB
2. **Better Performance**: Faster database queries without large file data
3. **Scalability**: S3 can handle unlimited file storage
4. **Cost Efficiency**: S3 storage is cheaper than database storage
5. **Durability**: S3 provides 99.999999999% (11 9's) durability
6. **Backup**: Built-in redundancy and optional versioning

## 10. Next Steps

After successful migration:

1. **Monitor application performance** - should be improved
2. **Monitor S3 costs** - should be lower than database storage costs
3. **Test all workspace functionality** - create, read, write, delete files
4. **Update backup procedures** - consider S3 backup strategies
5. **Document the new architecture** for your team

## Support

If you encounter issues:
1. Check the detailed logs from the migration process
2. Verify all AWS credentials and permissions
3. Test S3 connectivity with `npm run test-s3`
4. Review the S3_MIGRATION_GUIDE.md for detailed technical information