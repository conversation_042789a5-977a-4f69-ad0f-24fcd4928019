# Enable Application Load Balancer (ALB) for ECS Previews and Route 53 DNS

This guide explains:
- What the current implementation does
- What you need to provision in AWS (ALB, Target Group, Listeners, Route 53)
- Exactly how to wire the ALB into the ECS service creation flow
- How to point a friendly domain (e.g., `xxx.mydomain.io`) to your ALB
- Options for HTTPS and host-based routing

---

## 1) Current status

- ECS tasks/services are created via code in `backend/src/services/ecsWorkspaceService.ts` using config from `backend/src/config/aws.ts`.
- The container exposes two ports by default:
  - App/control API: `CONTAINER_PORT` (default 3000)
  - Preview server: `PREVIEW_PORT` (default 8080)
- Previews currently use the task public IP and port: `http://PUBLIC_IP:8080` (no ALB/Route 53 required).
- We added optional ALB integration:
  - In `backend/src/config/aws.ts`, `getDefaultServiceConfig` will attach the ECS service to a Target Group if `ALB_TARGET_GROUP_ARN` is provided via env.
  - It uses `containerName = "workspace-container"` and `containerPort = PREVIEW_PORT`.
  - Optional `HEALTH_CHECK_GRACE_SECONDS` env lets ECS ignore health check failures while tasks warm up.

No infrastructure for ALB/Route 53 is created by the app; you provision it once, then set env vars so new ECS services attach automatically.

---

## 2) What you need to provide

- **Networking**
  - `VPC_ID`: Your VPC
  - Two+ public subnets for the ALB
  - Security group for the ALB: inbound TCP 80 and/or 443 from 0.0.0.0/0
  - Security group for ECS tasks must allow inbound `PREVIEW_PORT` from the ALB security group
- **Target Group (required)**
  - Type: `ip`
  - Port: `8080` (or your `PREVIEW_PORT`)
  - Health check path (e.g., `/` or `/health`)
- **ALB Listeners**
  - HTTP 80 (forward to Target Group)
  - Optional HTTPS 443 with ACM certificate in the same region as the ALB
- **Route 53**
  - Hosted Zone for your domain (e.g., `mydomain.io`)
  - Create an `A (ALIAS)` record for `xxx.mydomain.io` → ALB DNS name
- **App Env Vars** (in backend runtime environment)
  - `ALB_TARGET_GROUP_ARN=arn:aws:elasticloadbalancing:...:targetgroup/preview-tg/...`
  - `HEALTH_CHECK_GRACE_SECONDS=300` (optional)

---

## 3) Provisioning: AWS CLI (reference)

Replace placeholders: `vpc-XXXX`, `subnet-AAAA`, `subnet-BBBB`, `sg-ALB`, `region`, `account`, etc.

```bash
# 3.1 Create Target Group (type ip, port 8080)
aws elbv2 create-target-group \
  --name preview-tg \
  --protocol HTTP \
  --port 8080 \
  --vpc-id vpc-XXXXXXXX \
  --target-type ip \
  --health-check-path / \
  --health-check-protocol HTTP \
  --health-check-port traffic-port \
  --matcher HttpCode=200-399

# Capture the TargetGroupArn from the output and export it for reuse
export TG_ARN=arn:aws:elasticloadbalancing:REGION:ACCOUNT:targetgroup/preview-tg/XXXXXXXX

# 3.2 Create ALB
aws elbv2 create-load-balancer \
  --name preview-alb \
  --subnets subnet-AAAAAAA subnet-BBBBBBB \
  --security-groups sg-ALB

# Capture the LoadBalancerArn and DNS
aws elbv2 describe-load-balancers --names preview-alb \
  --query 'LoadBalancers[0].{Arn:LoadBalancerArn,DNS:DNSName,ZoneId:CanonicalHostedZoneId}' --output table
# Note the Arn, DNSName (e.g. preview-alb-xxxx.us-east-1.elb.amazonaws.com), and CanonicalHostedZoneId

# 3.3 Create HTTP listener forwarding to Target Group
aws elbv2 create-listener \
  --load-balancer-arn arn:aws:elasticloadbalancing:...:loadbalancer/app/preview-alb/... \
  --protocol HTTP \
  --port 80 \
  --default-actions Type=forward,TargetGroupArn=$TG_ARN

# 3.4 (Optional) Create HTTPS listener (requires ACM cert in same region)
aws elbv2 create-listener \
  --load-balancer-arn arn:aws:elasticloadbalancing:...:loadbalancer/app/preview-alb/... \
  --protocol HTTPS \
  --port 443 \
  --certificates CertificateArn=arn:aws:acm:REGION:ACCOUNT:certificate/XXXX \
  --ssl-policy ELBSecurityPolicy-2016-08 \
  --default-actions Type=forward,TargetGroupArn=$TG_ARN
```

Host‑based rule example (optional; for per-preview hostnames):
```bash
aws elbv2 create-rule \
  --listener-arn arn:aws:elasticloadbalancing:...:listener/app/preview-alb/.../80 \
  --priority 10 \
  --conditions Field=host-header,Values=xxx.mydomain.io \
  --actions Type=forward,TargetGroupArn=$TG_ARN
```

---

## 4) Route 53: point your domain to the ALB

1) Find your public hosted zone ID for the domain (e.g., `Z123ABC...`).
2) Get ALB `DNSName` and `CanonicalHostedZoneId` (see CLI above).
3) Create an `A (ALIAS)` record:

```json
{
  "Comment": "Upsert preview A record",
  "Changes": [
    {
      "Action": "UPSERT",
      "ResourceRecordSet": {
        "Name": "xxx.mydomain.io",
        "Type": "A",
        "AliasTarget": {
          "HostedZoneId": "ALB_CANONICAL_HOSTED_ZONE_ID",
          "DNSName": "preview-alb-xxxx.us-east-1.elb.amazonaws.com",
          "EvaluateTargetHealth": false
        }
      }
    }
  ]
}
```

CLI to apply the change:
```bash
aws route53 change-resource-record-sets \
  --hosted-zone-id Z123ABCDEF \
  --change-batch file://upsert-preview.json
```

Result: `http://xxx.mydomain.io` will reach your ALB (80). If you created an HTTPS listener and ACM cert, `https://xxx.mydomain.io` will also work (443).

---

## 5) Wire ALB into the app: env vars

Set these in the backend runtime environment (e.g., `.env`, ECS task env, or your process manager):

```
ALB_TARGET_GROUP_ARN=arn:aws:elasticloadbalancing:REGION:ACCOUNT:targetgroup/preview-tg/XXXXXXXX
HEALTH_CHECK_GRACE_SECONDS=300
```

How it works in code:
- File: `backend/src/config/aws.ts`
  - `getAWSConfig()` reads the vars above
  - `getDefaultServiceConfig()` conditionally adds:
    - `loadBalancers: [{ targetGroupArn: ALB_TARGET_GROUP_ARN, containerName: 'workspace-container', containerPort: PREVIEW_PORT }]`
    - `healthCheckGracePeriodSeconds`
- When a new ECS service is created by `ECSWorkspaceService`, ECS auto-registers task IPs into the Target Group.

No code changes are needed beyond setting `ALB_TARGET_GROUP_ARN`.

---

## 6) Security group rules checklist

- ALB security group (SG-ALB):
  - Inbound: TCP 80 (0.0.0.0/0)
  - Inbound: TCP 443 (0.0.0.0/0) if using HTTPS
  - Outbound: allow all (default) or at least to ECS task subnets
- ECS task security group (SG-TASK):
  - Inbound: TCP 8080 FROM SG-ALB (recommended)
  - Outbound: allow all (default)

If you currently allow public inbound 8080 to the tasks, you can tighten it to only allow from SG-ALB once ALB is in place.

---

## 7) HTTPS

- Request an ACM certificate in the same region as the ALB for `xxx.mydomain.io` (or a wildcard like `*.preview.mydomain.io`).
- Attach it to the 443 listener when creating the listener.
- Route 53 DNS validation is recommended to auto-renew.

---

## 8) Optional: wildcard domains and host-based routing

If you want a dedicated subdomain per preview, e.g., `pr-123.preview.mydomain.io`:
- Create a wildcard ACM cert: `*.preview.mydomain.io`
- Create a wildcard Route 53 record: `A (ALIAS) *.preview.mydomain.io → ALB`
- Use host-based listener rules to forward each host to a distinct target group
  - Programmatically: on preview creation, create a new target group and listener rule; on deletion, clean them up
  - Each rule must have a unique `priority`

This yields isolation between previews at the ALB layer.

---

## 9) Terraform (optional reference)

Minimal ALB + TG + listener + Route 53 (HTTP only). Adjust names/ids.

```hcl
variable "vpc_id" {}
variable "public_subnet_ids" { type = list(string) }
variable "hosted_zone_id" {}
variable "domain_name" {}     # e.g., "xxx.mydomain.io"

resource "aws_security_group" "alb_sg" {
  name        = "preview-alb-sg"
  description = "Allow HTTP"
  vpc_id      = var.vpc_id

  ingress { from_port = 80 to_port = 80 protocol = "tcp" cidr_blocks = ["0.0.0.0/0"] }
  egress  { from_port = 0  to_port = 0  protocol = "-1"  cidr_blocks = ["0.0.0.0/0"] }
}

resource "aws_lb" "preview" {
  name               = "preview-alb"
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb_sg.id]
  subnets            = var.public_subnet_ids
}

resource "aws_lb_target_group" "preview_tg" {
  name        = "preview-tg"
  port        = 8080
  protocol    = "HTTP"
  vpc_id      = var.vpc_id
  target_type = "ip"

  health_check {
    path                = "/"
    port                = "traffic-port"
    healthy_threshold   = 2
    unhealthy_threshold = 2
    interval            = 15
    timeout             = 5
    matcher             = "200-399"
  }
}

resource "aws_lb_listener" "http" {
  load_balancer_arn = aws_lb.preview.arn
  port              = 80
  protocol          = "HTTP"
  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.preview_tg.arn
  }
}

# Route 53 ALIAS to ALB
resource "aws_route53_record" "preview_alias" {
  zone_id = var.hosted_zone_id
  name    = var.domain_name
  type    = "A"
  alias {
    name                   = aws_lb.preview.dns_name
    zone_id                = aws_lb.preview.zone_id
    evaluate_target_health = false
  }
}
```

Provide `aws_lb_target_group.preview_tg.arn` as `ALB_TARGET_GROUP_ARN` to the backend environment.

---

## 10) Troubleshooting

- Target health is `unhealthy`:
  - Verify container listens on `PREVIEW_PORT` (default 8080)
  - Adjust health check `path` to match your preview
  - Ensure SG-TASK allows inbound from SG-ALB on port 8080
  - ECS service uses `containerName = workspace-container` and `containerPort = PREVIEW_PORT` — confirm these match your task def
- 404/host mismatch via ALB:
  - If your app depends on `Host` header, ensure it accepts the domain used
- HTTPS not working:
  - Cert must be in the same region as the ALB
  - Listener 443 must be created with the certificate and correct SSL policy
- DNS not resolving:
  - Confirm you created the record in the correct hosted zone
  - ALIAS must use the ALB’s `CanonicalHostedZoneId` and `DNSName`

---

## 11) Next steps checklist

- [ ] Create Target Group (ip, port 8080) and note the ARN
- [ ] Create ALB in two+ public subnets with SG allowing 80/443
- [ ] Create HTTP (and optionally HTTPS) listener(s)
- [ ] Set backend env: `ALB_TARGET_GROUP_ARN`, `HEALTH_CHECK_GRACE_SECONDS`
- [ ] Ensure ECS task SG allows port 8080 from ALB SG
- [ ] Create Route 53 A (ALIAS) record for `xxx.mydomain.io` pointing to the ALB
- [ ] Trigger a new preview workspace and visit `http(s)://xxx.mydomain.io`

If you want per-preview hostnames, add: create a new target group + host rule per preview (automate in your backend lifecycle), and optionally use a wildcard DNS record and certificate. 