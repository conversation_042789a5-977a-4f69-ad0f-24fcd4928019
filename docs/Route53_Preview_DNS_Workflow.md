### Route 53 Preview DNS: Design and Workflow

This document describes how preview URLs are generated for ECS workspaces using Route 53 DNS records, the changes introduced, configuration required, and the current end-to-end workflow.

### Overview

- Each running ECS workspace gets a public IP and exposes a preview server on the configured preview port.
- Instead of using raw public IP with port in the UI, we map each workspace to a DNS record in Route 53.
- Record format: `preview--<interviewUuid>.<PREVIEW_BASE_DOMAIN>` (e.g., `preview--1234abcd.mergenai.io`).
- We do not hide the port; the frontend opens `http://preview--<uuid>.<domain>:<port>`.
- If DNS is not configured, the system automatically falls back to `http://<public_ip>:<port>`.

### Configuration

Set these environment variables for the backend:

```bash
# Required
AWS_REGION=...
AWS_ACCESS_KEY_ID=...
AWS_SECRET_ACCESS_KEY=...
ECS_CLUSTER_NAME=...
ECS_EXECUTION_ROLE_ARN=...
ECS_TASK_ROLE_ARN=...
VPC_ID=...
SUBNET_IDS=subnet-xxxxx,subnet-yyyyy
SECURITY_GROUP_ID=sg-zzzzz

# Preview port (default 8080)
PREVIEW_PORT=8080

# Route 53 DNS settings (optional; when unset we fall back to IP)
ROUTE53_HOSTED_ZONE_ID=ZXXXXXXXXXXXXXX
PREVIEW_BASE_DOMAIN=mergenai.io
```

Required IAM permissions for the credentials used by the backend:

- route53:ChangeResourceRecordSets (for the hosted zone)

### Data model changes

- `backend/src/models/Workspace.ts`
  - Added optional field: `previewHostname?: string`
- `backend/src/models/ECSContainer.ts`
  - Added optional field: `previewHostname?: string`

These fields store the DNS name we manage in Route 53 and are used to construct the preview URL.

### New DNS service

- `backend/src/services/route53Service.ts`
  - `Route53PreviewDNSService` wraps Route 53 operations used for preview records:
    - `getRecordName(interviewUuid)` → `preview--<uuid>.<PREVIEW_BASE_DOMAIN>`
    - `upsertARecord(name, ip)` → UPSERT A record pointing to the container public IP
    - `deleteARecord(name, ip)` → best-effort delete of the A record during cleanup or IP changes

### Backend workflow changes

- `backend/src/services/ecsWorkspaceService.ts`
  - On task becoming running, `updateNetworkInfo(...)` now:
    - Resolves the task's public IP.
    - If Route 53 is configured, UPSERTs `A` record: `preview--<uuid>.<domain> -> <public IP>`.
    - If the IP changed and we already had a hostname/IP, attempts best-effort delete of the old A record.
    - Sets DB fields:
      - `workspace.previewHostname`
      - `workspace.previewUrl = http://<previewHostname or publicIp>:<PREVIEW_PORT>`
      - Mirrors the same to the container record.
  - On stopping a workspace (`stopWorkspace(...)`):
    - Best-effort deletes the A record using the last known hostname/IP.

- `backend/src/services/ecsPreviewService.ts`
  - When starting/auto-starting preview, the effective URL uses `previewHostname` if present; otherwise falls back to `publicIp`.
  - The URL is saved in `workspace.previewUrl` and returned to the caller.

- Controllers and Routes
  - No changes required. They continue to call preview APIs and return `previewUrl`.

### Frontend behavior

- The frontend already uses `previewUrl` returned by the backend to open the preview and to set the iframe `src`. No code changes required.

### Cleanup and lifecycle

- When a workspace is stopped or recreated and the public IP changes:
  - The service attempts to delete the old A record and upsert a new one to the new public IP.
- Expired workspaces are cleaned up by existing lifecycle/cleanup; DNS removal is best-effort during stop.

### Files touched

- Added
  - `backend/src/services/route53Service.ts`
  - `docs/Route53_Preview_DNS_Workflow.md` (this file)
- Modified
  - `backend/src/config/aws.ts` (added `route53HostedZoneId`, `previewBaseDomain` support)
  - `backend/src/models/Workspace.ts` (added `previewHostname`)
  - `backend/src/models/ECSContainer.ts` (added `previewHostname`)
  - `backend/src/services/ecsWorkspaceService.ts` (DNS upsert/cleanup and URL construction)
  - `backend/src/services/ecsPreviewService.ts` (prefer hostname for URL)
  - `backend/package.json` (added `@aws-sdk/client-route-53`)

### Example

Given:
- `PREVIEW_BASE_DOMAIN=mergenai.io`
- Interview UUID: `8b2f14f0-12ab-45cd-9e01-23456789abcd`
- Resolved public IP: `***********`
- `PREVIEW_PORT=8080`

Record created/updated:

```
preview--8b2f14f0-12ab-45cd-9e01-23456789abcd.mergenai.io. 60 IN A ***********
```

Preview URL returned to frontend:

```
http://preview--8b2f14f0-12ab-45cd-9e01-23456789abcd.mergenai.io:8080
```

If DNS is not configured, the URL falls back to:

```
http://***********:8080
```

### Operational notes

- TTL defaults to 60 seconds; allow brief propagation when the IP changes.
- If Route 53 env vars are not provided, the feature is inert and behavior falls back to IP-based URLs.
- Ensure the hosted zone matches `PREVIEW_BASE_DOMAIN` and is publicly resolvable.
- Ensure backend credentials can call `ChangeResourceRecordSets` on the hosted zone.

### Troubleshooting

- Preview returns IP instead of domain: Missing `ROUTE53_HOSTED_ZONE_ID` or `PREVIEW_BASE_DOMAIN`.
- DNS name resolves to old IP: Allow TTL to expire; ensure stop/recreate path runs; verify IAM permission.
- No public IP assigned: Check ECS service network config (`assignPublicIp=ENABLED`) and subnet public routing. 